import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  SafeAreaView,
  Alert,
} from 'react-native';
import { useAuthenticator } from '@aws-amplify/ui-react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

const SettingsScreen: React.FC = () => {
  const { signOut } = useAuthenticator();
  
  // Privacy settings state
  const [profileVisible, setProfileVisible] = useState(false);
  const [locationSharing, setLocationSharing] = useState(false);
  const [contactSharing, setContactSharing] = useState(false);
  const [notifications, setNotifications] = useState(true);

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Sign Out', style: 'destructive', onPress: signOut },
      ]
    );
  };

  const handleDeleteAccount = () => {
    Alert.alert(
      'Delete Account',
      'This action cannot be undone. All your data will be permanently deleted.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive', 
          onPress: () => console.log('Account deletion requested') 
        },
      ]
    );
  };

  const SettingItem = ({ 
    icon, 
    title, 
    subtitle, 
    onPress, 
    rightComponent 
  }: {
    icon: string;
    title: string;
    subtitle?: string;
    onPress?: () => void;
    rightComponent?: React.ReactNode;
  }) => (
    <TouchableOpacity style={styles.settingItem} onPress={onPress}>
      <Icon name={icon} size={24} color="#666" />
      <View style={styles.settingContent}>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
      {rightComponent || <Icon name="chevron-right" size={24} color="#ccc" />}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Privacy Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Privacy & Visibility</Text>
          
          <SettingItem
            icon="visibility"
            title="Profile Visibility"
            subtitle="Make your profile discoverable by others"
            rightComponent={
              <Switch
                value={profileVisible}
                onValueChange={setProfileVisible}
                trackColor={{ false: '#ccc', true: '#FF6B35' }}
                thumbColor="#fff"
              />
            }
          />
          
          <SettingItem
            icon="location-on"
            title="Location Sharing"
            subtitle="Allow others to see your approximate location"
            rightComponent={
              <Switch
                value={locationSharing}
                onValueChange={setLocationSharing}
                trackColor={{ false: '#ccc', true: '#FF6B35' }}
                thumbColor="#fff"
              />
            }
          />
          
          <SettingItem
            icon="contact-phone"
            title="Contact Information Sharing"
            subtitle="Share contact details with connections"
            rightComponent={
              <Switch
                value={contactSharing}
                onValueChange={setContactSharing}
                trackColor={{ false: '#ccc', true: '#FF6B35' }}
                thumbColor="#fff"
              />
            }
          />
        </View>

        {/* Notification Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Notifications</Text>
          
          <SettingItem
            icon="notifications"
            title="Push Notifications"
            subtitle="Receive notifications about connections and messages"
            rightComponent={
              <Switch
                value={notifications}
                onValueChange={setNotifications}
                trackColor={{ false: '#ccc', true: '#FF6B35' }}
                thumbColor="#fff"
              />
            }
          />
          
          <SettingItem
            icon="email"
            title="Email Notifications"
            subtitle="Receive updates via email"
            onPress={() => console.log('Email notifications')}
          />
        </View>

        {/* Account Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account</Text>
          
          <SettingItem
            icon="edit"
            title="Edit Profile"
            subtitle="Update your personal information"
            onPress={() => console.log('Edit profile')}
          />
          
          <SettingItem
            icon="security"
            title="Privacy Policy"
            subtitle="Learn how we protect your data"
            onPress={() => console.log('Privacy policy')}
          />
          
          <SettingItem
            icon="description"
            title="Terms of Service"
            subtitle="Read our terms and conditions"
            onPress={() => console.log('Terms of service')}
          />
        </View>

        {/* Support */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Support</Text>
          
          <SettingItem
            icon="help"
            title="Help & FAQ"
            subtitle="Get answers to common questions"
            onPress={() => console.log('Help & FAQ')}
          />
          
          <SettingItem
            icon="feedback"
            title="Send Feedback"
            subtitle="Help us improve RootsConnect"
            onPress={() => console.log('Send feedback')}
          />
          
          <SettingItem
            icon="info"
            title="About"
            subtitle="Version 1.0.0"
            onPress={() => console.log('About')}
          />
        </View>

        {/* Danger Zone */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account Actions</Text>
          
          <TouchableOpacity style={styles.dangerItem} onPress={handleSignOut}>
            <Icon name="logout" size={24} color="#f44336" />
            <Text style={styles.dangerText}>Sign Out</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.dangerItem} onPress={handleDeleteAccount}>
            <Icon name="delete-forever" size={24} color="#f44336" />
            <Text style={styles.dangerText}>Delete Account</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    backgroundColor: '#fff',
    marginBottom: 20,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingContent: {
    flex: 1,
    marginLeft: 16,
  },
  settingTitle: {
    fontSize: 16,
    color: '#333',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#666',
  },
  dangerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  dangerText: {
    fontSize: 16,
    color: '#f44336',
    marginLeft: 16,
    fontWeight: '500',
  },
});

export default SettingsScreen;
