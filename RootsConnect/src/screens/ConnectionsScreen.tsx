import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  SafeAreaView,
  Image,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface Connection {
  id: string;
  name: string;
  location: string;
  profession: string;
  avatar: string;
  status: 'connected' | 'pending' | 'requested';
  lastActive: string;
}

const ConnectionsScreen: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'connections' | 'requests'>('connections');

  // Sample data
  const connections: Connection[] = [
    {
      id: '1',
      name: '<PERSON><PERSON>',
      location: 'San Francisco, CA',
      profession: 'Software Engineer',
      avatar: 'https://via.placeholder.com/50',
      status: 'connected',
      lastActive: '2 hours ago',
    },
    {
      id: '2',
      name: '<PERSON>',
      location: 'New York, NY',
      profession: 'Data Scientist',
      avatar: 'https://via.placeholder.com/50',
      status: 'connected',
      lastActive: '1 day ago',
    },
  ];

  const requests: Connection[] = [
    {
      id: '3',
      name: '<PERSON>',
      location: 'Toronto, ON',
      profession: 'Product Manager',
      avatar: 'https://via.placeholder.com/50',
      status: 'pending',
      lastActive: 'Just now',
    },
  ];

  const renderConnection = ({ item }: { item: Connection }) => (
    <TouchableOpacity style={styles.connectionCard}>
      <Image source={{ uri: item.avatar }} style={styles.avatar} />
      <View style={styles.connectionInfo}>
        <Text style={styles.connectionName}>{item.name}</Text>
        <Text style={styles.connectionLocation}>{item.location}</Text>
        <Text style={styles.connectionProfession}>{item.profession}</Text>
        <Text style={styles.lastActive}>Active {item.lastActive}</Text>
      </View>
      <View style={styles.connectionActions}>
        {item.status === 'connected' && (
          <>
            <TouchableOpacity style={styles.actionButton}>
              <Icon name="message" size={20} color="#FF6B35" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <Icon name="more-vert" size={20} color="#666" />
            </TouchableOpacity>
          </>
        )}
        {item.status === 'pending' && (
          <>
            <TouchableOpacity style={[styles.actionButton, styles.acceptButton]}>
              <Icon name="check" size={20} color="#fff" />
            </TouchableOpacity>
            <TouchableOpacity style={[styles.actionButton, styles.declineButton]}>
              <Icon name="close" size={20} color="#fff" />
            </TouchableOpacity>
          </>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Icon 
        name={activeTab === 'connections' ? 'people-outline' : 'person-add'} 
        size={64} 
        color="#ccc" 
      />
      <Text style={styles.emptyStateTitle}>
        {activeTab === 'connections' ? 'No Connections Yet' : 'No Pending Requests'}
      </Text>
      <Text style={styles.emptyStateText}>
        {activeTab === 'connections' 
          ? 'Start connecting with community members to build your network.'
          : 'Connection requests will appear here when others want to connect with you.'
        }
      </Text>
      {activeTab === 'connections' && (
        <TouchableOpacity style={styles.discoverButton}>
          <Text style={styles.discoverButtonText}>Discover People</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Tab Navigation */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'connections' && styles.activeTab]}
          onPress={() => setActiveTab('connections')}
        >
          <Text style={[styles.tabText, activeTab === 'connections' && styles.activeTabText]}>
            Connections
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'requests' && styles.activeTab]}
          onPress={() => setActiveTab('requests')}
        >
          <Text style={[styles.tabText, activeTab === 'requests' && styles.activeTabText]}>
            Requests
          </Text>
          {requests.length > 0 && (
            <View style={styles.badge}>
              <Text style={styles.badgeText}>{requests.length}</Text>
            </View>
          )}
        </TouchableOpacity>
      </View>

      {/* Content */}
      <FlatList
        data={activeTab === 'connections' ? connections : requests}
        renderItem={renderConnection}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
    position: 'relative',
  },
  activeTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#FF6B35',
  },
  tabText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#FF6B35',
    fontWeight: '600',
  },
  badge: {
    position: 'absolute',
    top: 8,
    right: '30%',
    backgroundColor: '#FF6B35',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  listContainer: {
    padding: 16,
  },
  connectionCard: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#ddd',
  },
  connectionInfo: {
    flex: 1,
    marginLeft: 12,
  },
  connectionName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  connectionLocation: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  connectionProfession: {
    fontSize: 14,
    color: '#FF6B35',
    marginBottom: 4,
  },
  lastActive: {
    fontSize: 12,
    color: '#999',
  },
  connectionActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  actionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
  },
  acceptButton: {
    backgroundColor: '#4CAF50',
  },
  declineButton: {
    backgroundColor: '#f44336',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyStateTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 24,
    paddingHorizontal: 40,
  },
  discoverButton: {
    backgroundColor: '#FF6B35',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  discoverButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default ConnectionsScreen;
