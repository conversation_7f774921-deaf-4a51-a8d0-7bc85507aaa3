import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
} from 'react-native';
import { useAuthenticator } from '@aws-amplify/ui-react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

const ProfileScreen: React.FC = () => {
  const { user } = useAuthenticator();

  const profileSections = [
    {
      title: 'Personal Information',
      items: [
        { label: 'Name', value: user?.signInDetails?.loginId || 'Not set', icon: 'person' },
        { label: 'Email', value: user?.signInDetails?.loginId || 'Not set', icon: 'email' },
        { label: 'Phone', value: 'Not set', icon: 'phone' },
        { label: 'Location', value: 'Not set', icon: 'location-on' },
      ],
    },
    {
      title: 'Contact Sharing',
      items: [
        { label: 'WhatsApp', value: 'Not set', icon: 'chat' },
        { label: 'Facebook', value: 'Not set', icon: 'facebook' },
        { label: 'Telegram', value: 'Not set', icon: 'telegram' },
      ],
    },
    {
      title: 'Community Info',
      items: [
        { label: 'Profession', value: 'Not set', icon: 'work' },
        { label: 'Interests', value: 'Not set', icon: 'interests' },
        { label: 'Bio', value: 'Not set', icon: 'description' },
      ],
    },
  ];

  const privacySettings = [
    { label: 'Profile Visibility', value: 'Private', icon: 'visibility' },
    { label: 'Location Sharing', value: 'Disabled', icon: 'location-off' },
    { label: 'Contact Sharing', value: 'Disabled', icon: 'contact-phone' },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        {/* Profile Header */}
        <View style={styles.header}>
          <View style={styles.profileImageContainer}>
            <Image
              source={{ uri: 'https://via.placeholder.com/100' }}
              style={styles.profileImage}
            />
            <TouchableOpacity style={styles.editImageButton}>
              <Icon name="camera-alt" size={16} color="#fff" />
            </TouchableOpacity>
          </View>
          <Text style={styles.userName}>
            {user?.signInDetails?.loginId || 'User Name'}
          </Text>
          <Text style={styles.userStatus}>Profile Incomplete</Text>
          <TouchableOpacity style={styles.editProfileButton}>
            <Icon name="edit" size={16} color="#FF6B35" />
            <Text style={styles.editProfileText}>Complete Profile</Text>
          </TouchableOpacity>
        </View>

        {/* Profile Sections */}
        {profileSections.map((section, sectionIndex) => (
          <View key={sectionIndex} style={styles.section}>
            <Text style={styles.sectionTitle}>{section.title}</Text>
            {section.items.map((item, itemIndex) => (
              <TouchableOpacity key={itemIndex} style={styles.profileItem}>
                <Icon name={item.icon} size={24} color="#666" />
                <View style={styles.itemContent}>
                  <Text style={styles.itemLabel}>{item.label}</Text>
                  <Text style={styles.itemValue}>{item.value}</Text>
                </View>
                <Icon name="chevron-right" size={24} color="#ccc" />
              </TouchableOpacity>
            ))}
          </View>
        ))}

        {/* Privacy Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Privacy Settings</Text>
          {privacySettings.map((setting, index) => (
            <TouchableOpacity key={index} style={styles.profileItem}>
              <Icon name={setting.icon} size={24} color="#666" />
              <View style={styles.itemContent}>
                <Text style={styles.itemLabel}>{setting.label}</Text>
                <Text style={[styles.itemValue, styles.privacyValue]}>
                  {setting.value}
                </Text>
              </View>
              <Icon name="chevron-right" size={24} color="#ccc" />
            </TouchableOpacity>
          ))}
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <TouchableOpacity style={styles.actionButton}>
            <Icon name="help" size={20} color="#FF6B35" />
            <Text style={styles.actionButtonText}>Help & Support</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton}>
            <Icon name="info" size={20} color="#FF6B35" />
            <Text style={styles.actionButtonText}>About RootsConnect</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    backgroundColor: '#fff',
    alignItems: 'center',
    paddingVertical: 30,
    marginBottom: 20,
  },
  profileImageContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#ddd',
  },
  editImageButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: '#FF6B35',
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: '#fff',
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  userStatus: {
    fontSize: 14,
    color: '#FF6B35',
    marginBottom: 16,
  },
  editProfileButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#FF6B35',
  },
  editProfileText: {
    color: '#FF6B35',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
  section: {
    backgroundColor: '#fff',
    marginBottom: 20,
    paddingVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  profileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  itemContent: {
    flex: 1,
    marginLeft: 16,
  },
  itemLabel: {
    fontSize: 16,
    color: '#333',
    marginBottom: 2,
  },
  itemValue: {
    fontSize: 14,
    color: '#666',
  },
  privacyValue: {
    color: '#FF6B35',
    fontWeight: '600',
  },
  actionButtons: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  actionButtonText: {
    color: '#FF6B35',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 12,
  },
});

export default ProfileScreen;
