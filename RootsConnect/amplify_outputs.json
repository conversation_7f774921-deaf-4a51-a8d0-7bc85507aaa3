{"auth": {"user_pool_id": "us-east-1_vXjtfuZRQ", "aws_region": "us-east-1", "user_pool_client_id": "5pmn4gv468380geuog4pf07hlu", "identity_pool_id": "us-east-1:5f089b3a-8a22-4d05-a0f7-08cdcbe97b50", "mfa_methods": [], "standard_required_attributes": ["email"], "username_attributes": ["email"], "user_verification_types": ["email"], "groups": [], "mfa_configuration": "NONE", "password_policy": {"min_length": 8, "require_lowercase": true, "require_numbers": true, "require_symbols": true, "require_uppercase": true}, "unauthenticated_identities_enabled": true}, "data": {"url": "https://yy4rngmadjfrrfuayz6b6xyiqq.appsync-api.us-east-1.amazonaws.com/graphql", "aws_region": "us-east-1", "api_key": "da2-bw6ghg3hcjdljejdgncro34lsi", "default_authorization_type": "AMAZON_COGNITO_USER_POOLS", "authorization_types": ["API_KEY", "AWS_IAM"], "model_introspection": {"version": 1, "models": {"UserProfile": {"name": "UserProfile", "fields": {"id": {"name": "id", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "userId": {"name": "userId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "email": {"name": "email", "isArray": false, "type": "AWSEmail", "isRequired": true, "attributes": []}, "firstName": {"name": "firstName", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "lastName": {"name": "lastName", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "displayName": {"name": "displayName", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "profilePicture": {"name": "profilePicture", "isArray": false, "type": "AWSURL", "isRequired": false, "attributes": []}, "latitude": {"name": "latitude", "isArray": false, "type": "Float", "isRequired": false, "attributes": []}, "longitude": {"name": "longitude", "isArray": false, "type": "Float", "isRequired": false, "attributes": []}, "city": {"name": "city", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "state": {"name": "state", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "country": {"name": "country", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "isLocationVisible": {"name": "isLocationVisible", "isArray": false, "type": "Boolean", "isRequired": false, "attributes": []}, "phoneNumbers": {"name": "phoneNumbers", "isArray": true, "type": "String", "isRequired": false, "attributes": [], "isArrayNullable": true}, "whatsappNumber": {"name": "whatsappNumber", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "facebookProfile": {"name": "facebookProfile", "isArray": false, "type": "AWSURL", "isRequired": false, "attributes": []}, "telegramHandle": {"name": "telegramHandle", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "additionalEmails": {"name": "additionalEmails", "isArray": true, "type": "AWSEmail", "isRequired": false, "attributes": [], "isArrayNullable": true}, "isProfilePublic": {"name": "isProfilePublic", "isArray": false, "type": "Boolean", "isRequired": false, "attributes": []}, "shareContactInfo": {"name": "shareContactInfo", "isArray": false, "type": "Boolean", "isRequired": false, "attributes": []}, "allowDirectContact": {"name": "allowDirectContact", "isArray": false, "type": "Boolean", "isRequired": false, "attributes": []}, "interests": {"name": "interests", "isArray": true, "type": "String", "isRequired": false, "attributes": [], "isArrayNullable": true}, "profession": {"name": "profession", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "bio": {"name": "bio", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "lastActiveAt": {"name": "lastActiveAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}}, "syncable": true, "pluralName": "UserProfiles", "attributes": [{"type": "model", "properties": {}}, {"type": "auth", "properties": {"rules": [{"provider": "userPools", "ownerField": "owner", "allow": "owner", "identityClaim": "cognito:username", "operations": ["create", "update", "delete", "read"]}, {"allow": "private", "operations": ["read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": false, "primaryKeyFieldName": "id", "sortKeyFieldNames": []}}, "ConnectionRequest": {"name": "ConnectionRequest", "fields": {"id": {"name": "id", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "fromUserId": {"name": "fromUserId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "toUserId": {"name": "toUserId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "status": {"name": "status", "isArray": false, "type": {"enum": "ConnectionRequestStatus"}, "isRequired": false, "attributes": []}, "message": {"name": "message", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}}, "syncable": true, "pluralName": "ConnectionRequests", "attributes": [{"type": "model", "properties": {}}, {"type": "auth", "properties": {"rules": [{"provider": "userPools", "ownerField": "owner", "allow": "owner", "operations": ["create", "read", "update"], "identityClaim": "cognito:username"}, {"allow": "private", "operations": ["read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": false, "primaryKeyFieldName": "id", "sortKeyFieldNames": []}}, "Connection": {"name": "Connection", "fields": {"id": {"name": "id", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "user1Id": {"name": "user1Id", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "user2Id": {"name": "user2Id", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "connectedAt": {"name": "connectedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "sharedContactInfo": {"name": "sharedContactInfo", "isArray": false, "type": "Boolean", "isRequired": false, "attributes": []}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}}, "syncable": true, "pluralName": "Connections", "attributes": [{"type": "model", "properties": {}}, {"type": "auth", "properties": {"rules": [{"allow": "private", "operations": ["read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": false, "primaryKeyFieldName": "id", "sortKeyFieldNames": []}}}, "enums": {"ConnectionRequestStatus": {"name": "ConnectionRequestStatus", "values": ["pending", "accepted", "declined"]}}, "nonModels": {}}}, "version": "1.4"}