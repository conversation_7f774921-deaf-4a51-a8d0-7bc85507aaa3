PODS:
  - boost (1.84.0)
  - DoubleConversion (1.1.6)
  - EXConstants (16.0.2):
    - ExpoModulesCore
  - Expo (51.0.39):
    - ExpoModulesCore
  - ExpoAsset (10.0.10):
    - ExpoModulesCore
  - ExpoFileSystem (17.0.1):
    - ExpoModulesCore
  - ExpoFont (12.0.10):
    - ExpoModulesCore
  - ExpoKeepAwake (13.0.2):
    - ExpoModulesCore
  - ExpoModulesCore (1.12.26):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - fast_float (8.0.0)
  - FBLazyVector (0.80.0)
  - fmt (11.0.2)
  - glog (0.3.5)
  - hermes-engine (0.80.0):
    - hermes-engine/Pre-built (= 0.80.0)
  - hermes-engine/Pre-built (0.80.0)
  - RCT-Folly (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 8.0.0)
    - fmt (= 11.0.2)
    - glog
    - RCT-Folly/Default (= 2024.11.18.00)
  - RCT-Folly/Default (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 8.0.0)
    - fmt (= 11.0.2)
    - glog
  - RCT-Folly/Fabric (2024.11.18.00):
    - boost
    - DoubleConversion
    - fast_float (= 8.0.0)
    - fmt (= 11.0.2)
    - glog
  - RCTDeprecation (0.80.0)
  - RCTRequired (0.80.0)
  - RCTTypeSafety (0.80.0):
    - FBLazyVector (= 0.80.0)
    - RCTRequired (= 0.80.0)
    - React-Core (= 0.80.0)
  - React (0.80.0):
    - React-Core (= 0.80.0)
    - React-Core/DevSupport (= 0.80.0)
    - React-Core/RCTWebSocket (= 0.80.0)
    - React-RCTActionSheet (= 0.80.0)
    - React-RCTAnimation (= 0.80.0)
    - React-RCTBlob (= 0.80.0)
    - React-RCTImage (= 0.80.0)
    - React-RCTLinking (= 0.80.0)
    - React-RCTNetwork (= 0.80.0)
    - React-RCTSettings (= 0.80.0)
    - React-RCTText (= 0.80.0)
    - React-RCTVibration (= 0.80.0)
  - React-callinvoker (0.80.0)
  - React-Core (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default (= 0.80.0)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/CoreModulesHeaders (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/Default (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/DevSupport (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default (= 0.80.0)
    - React-Core/RCTWebSocket (= 0.80.0)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTAnimationHeaders (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTBlobHeaders (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTImageHeaders (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTLinkingHeaders (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTNetworkHeaders (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTSettingsHeaders (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTTextHeaders (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTVibrationHeaders (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-Core/RCTWebSocket (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTDeprecation
    - React-Core/Default (= 0.80.0)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsitooling
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-CoreModules (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety (= 0.80.0)
    - React-Core/CoreModulesHeaders (= 0.80.0)
    - React-jsi (= 0.80.0)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTFBReactNativeSpec
    - React-RCTImage (= 0.80.0)
    - ReactCommon
    - SocketRocket
  - React-cxxreact (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker (= 0.80.0)
    - React-debug (= 0.80.0)
    - React-jsi (= 0.80.0)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-logger (= 0.80.0)
    - React-perflogger (= 0.80.0)
    - React-runtimeexecutor (= 0.80.0)
    - React-timing (= 0.80.0)
    - SocketRocket
  - React-debug (0.80.0)
  - React-defaultsnativemodule (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-domnativemodule
    - React-featureflagsnativemodule
    - React-hermes
    - React-idlecallbacksnativemodule
    - React-jsi
    - React-jsiexecutor
    - React-microtasksnativemodule
    - React-RCTFBReactNativeSpec
    - SocketRocket
  - React-domnativemodule (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Fabric
    - React-FabricComponents
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-Fabric (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.80.0)
    - React-Fabric/attributedstring (= 0.80.0)
    - React-Fabric/componentregistry (= 0.80.0)
    - React-Fabric/componentregistrynative (= 0.80.0)
    - React-Fabric/components (= 0.80.0)
    - React-Fabric/consistency (= 0.80.0)
    - React-Fabric/core (= 0.80.0)
    - React-Fabric/dom (= 0.80.0)
    - React-Fabric/imagemanager (= 0.80.0)
    - React-Fabric/leakchecker (= 0.80.0)
    - React-Fabric/mounting (= 0.80.0)
    - React-Fabric/observers (= 0.80.0)
    - React-Fabric/scheduler (= 0.80.0)
    - React-Fabric/telemetry (= 0.80.0)
    - React-Fabric/templateprocessor (= 0.80.0)
    - React-Fabric/uimanager (= 0.80.0)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/animations (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/attributedstring (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/componentregistry (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/componentregistrynative (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/legacyviewmanagerinterop (= 0.80.0)
    - React-Fabric/components/root (= 0.80.0)
    - React-Fabric/components/scrollview (= 0.80.0)
    - React-Fabric/components/view (= 0.80.0)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components/legacyviewmanagerinterop (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components/root (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components/scrollview (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/components/view (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-renderercss
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-Fabric/consistency (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/core (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/dom (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/imagemanager (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/leakchecker (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/mounting (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/observers (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events (= 0.80.0)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/observers/events (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/scheduler (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-performancetimeline
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/telemetry (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/templateprocessor (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/uimanager (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager/consistency (= 0.80.0)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-Fabric/uimanager/consistency (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-FabricComponents (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components (= 0.80.0)
    - React-FabricComponents/textlayoutmanager (= 0.80.0)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components/inputaccessory (= 0.80.0)
    - React-FabricComponents/components/iostextinput (= 0.80.0)
    - React-FabricComponents/components/modal (= 0.80.0)
    - React-FabricComponents/components/rncore (= 0.80.0)
    - React-FabricComponents/components/safeareaview (= 0.80.0)
    - React-FabricComponents/components/scrollview (= 0.80.0)
    - React-FabricComponents/components/text (= 0.80.0)
    - React-FabricComponents/components/textinput (= 0.80.0)
    - React-FabricComponents/components/unimplementedview (= 0.80.0)
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/inputaccessory (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/iostextinput (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/modal (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/rncore (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/safeareaview (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/scrollview (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/text (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/textinput (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/components/unimplementedview (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricComponents/textlayoutmanager (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-FabricImage (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired (= 0.80.0)
    - RCTTypeSafety (= 0.80.0)
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor (= 0.80.0)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - SocketRocket
    - Yoga
  - React-featureflags (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-featureflagsnativemodule (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-graphics (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-utils
    - SocketRocket
  - React-hermes (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact (= 0.80.0)
    - React-jsi
    - React-jsiexecutor (= 0.80.0)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-perflogger (= 0.80.0)
    - React-runtimeexecutor
    - SocketRocket
  - React-idlecallbacksnativemodule (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - React-runtimescheduler
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-ImageManager (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
    - SocketRocket
  - React-jserrorhandler (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - ReactCommon/turbomodule/bridging
    - SocketRocket
  - React-jsi (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-jsiexecutor (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact (= 0.80.0)
    - React-jsi (= 0.80.0)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-perflogger (= 0.80.0)
    - SocketRocket
  - React-jsinspector (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-jsi
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-jsinspectortracing
    - React-perflogger (= 0.80.0)
    - React-runtimeexecutor (= 0.80.0)
    - SocketRocket
  - React-jsinspectorcdp (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-jsinspectornetwork (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-jsinspectorcdp
    - SocketRocket
  - React-jsinspectortracing (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-oscompat
    - SocketRocket
  - React-jsitooling (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact (= 0.80.0)
    - React-jsi (= 0.80.0)
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - SocketRocket
  - React-jsitracing (0.80.0):
    - React-jsi
  - React-logger (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-Mapbuffer (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-debug
    - SocketRocket
  - React-microtasksnativemodule (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-RCTFBReactNativeSpec
    - ReactCommon/turbomodule/core
    - SocketRocket
  - react-native-maps (1.24.3):
    - react-native-maps/Maps (= 1.24.3)
  - react-native-maps/Generated (1.24.3):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - react-native-maps/Maps (1.24.3):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - react-native-maps/Generated
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - react-native-safe-area-context (5.5.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - react-native-safe-area-context/common (= 5.5.0)
    - react-native-safe-area-context/fabric (= 5.5.0)
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - react-native-safe-area-context/common (5.5.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - react-native-safe-area-context/fabric (5.5.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - react-native-safe-area-context/common
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - React-NativeModulesApple (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
  - React-oscompat (0.80.0)
  - React-perflogger (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - SocketRocket
  - React-performancetimeline (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-jsinspectortracing
    - React-perflogger
    - React-timing
    - SocketRocket
  - React-RCTActionSheet (0.80.0):
    - React-Core/RCTActionSheetHeaders (= 0.80.0)
  - React-RCTAnimation (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety
    - React-Core/RCTAnimationHeaders
    - React-featureflags
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - SocketRocket
  - React-RCTAppDelegate (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-debug
    - React-defaultsnativemodule
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsitooling
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RCTImage
    - React-RCTNetwork
    - React-RCTRuntime
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-runtimescheduler
    - React-utils
    - ReactCommon
    - SocketRocket
  - React-RCTBlob (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
    - SocketRocket
  - React-RCTFabric (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-jsinspectortracing
    - React-performancetimeline
    - React-RCTAnimation
    - React-RCTImage
    - React-RCTText
    - React-rendererconsistency
    - React-renderercss
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - SocketRocket
    - Yoga
  - React-RCTFBReactNativeSpec (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - ReactCommon
    - SocketRocket
  - React-RCTImage (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - React-RCTNetwork
    - ReactCommon
    - SocketRocket
  - React-RCTLinking (0.80.0):
    - React-Core/RCTLinkingHeaders (= 0.80.0)
    - React-jsi (= 0.80.0)
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.80.0)
  - React-RCTNetwork (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety
    - React-Core/RCTNetworkHeaders
    - React-featureflags
    - React-jsi
    - React-jsinspectorcdp
    - React-jsinspectornetwork
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - SocketRocket
  - React-RCTRuntime (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-jsitooling
    - React-RuntimeApple
    - React-RuntimeCore
    - React-RuntimeHermes
    - SocketRocket
  - React-RCTSettings (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTTypeSafety
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - SocketRocket
  - React-RCTText (0.80.0):
    - React-Core/RCTTextHeaders (= 0.80.0)
    - Yoga
  - React-RCTVibration (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFBReactNativeSpec
    - ReactCommon
    - SocketRocket
  - React-rendererconsistency (0.80.0)
  - React-renderercss (0.80.0):
    - React-debug
    - React-utils
  - React-rendererdebug (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-debug
    - SocketRocket
  - React-rncore (0.80.0)
  - React-RuntimeApple (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTFBReactNativeSpec
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
    - SocketRocket
  - React-RuntimeCore (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-cxxreact
    - React-Fabric
    - React-featureflags
    - React-hermes
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-jsitooling
    - React-performancetimeline
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
    - SocketRocket
  - React-runtimeexecutor (0.80.0):
    - React-jsi (= 0.80.0)
  - React-RuntimeHermes (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsinspectorcdp
    - React-jsinspectortracing
    - React-jsitooling
    - React-jsitracing
    - React-RuntimeCore
    - React-utils
    - SocketRocket
  - React-runtimescheduler (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspectortracing
    - React-performancetimeline
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-timing
    - React-utils
    - SocketRocket
  - React-timing (0.80.0)
  - React-utils (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-debug
    - React-hermes
    - React-jsi (= 0.80.0)
    - SocketRocket
  - ReactAppDependencyProvider (0.80.0):
    - ReactCodegen
  - ReactCodegen (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
  - ReactCommon (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly
    - RCT-Folly/Fabric
    - ReactCommon/turbomodule (= 0.80.0)
    - SocketRocket
  - ReactCommon/turbomodule (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker (= 0.80.0)
    - React-cxxreact (= 0.80.0)
    - React-jsi (= 0.80.0)
    - React-logger (= 0.80.0)
    - React-perflogger (= 0.80.0)
    - ReactCommon/turbomodule/bridging (= 0.80.0)
    - ReactCommon/turbomodule/core (= 0.80.0)
    - SocketRocket
  - ReactCommon/turbomodule/bridging (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker (= 0.80.0)
    - React-cxxreact (= 0.80.0)
    - React-jsi (= 0.80.0)
    - React-logger (= 0.80.0)
    - React-perflogger (= 0.80.0)
    - SocketRocket
  - ReactCommon/turbomodule/core (0.80.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - React-callinvoker (= 0.80.0)
    - React-cxxreact (= 0.80.0)
    - React-debug (= 0.80.0)
    - React-featureflags (= 0.80.0)
    - React-jsi (= 0.80.0)
    - React-logger (= 0.80.0)
    - React-perflogger (= 0.80.0)
    - React-utils (= 0.80.0)
    - SocketRocket
  - RNCAsyncStorage (2.2.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNGestureHandler (2.26.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNScreens (4.11.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNScreens/common (= 4.11.1)
    - SocketRocket
    - Yoga
  - RNScreens/common (4.11.1):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - RNVectorIcons (10.2.0):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-ImageManager
    - React-jsi
    - React-NativeModulesApple
    - React-RCTFabric
    - React-renderercss
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - SocketRocket
    - Yoga
  - SocketRocket (0.7.1)
  - Yoga (0.0.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - EXConstants (from `../node_modules/expo-constants/ios`)
  - Expo (from `../node_modules/expo`)
  - ExpoAsset (from `../node_modules/expo-asset/ios`)
  - ExpoFileSystem (from `../node_modules/expo-file-system/ios`)
  - ExpoFont (from `../node_modules/expo-font/ios`)
  - ExpoKeepAwake (from `../node_modules/expo-keep-awake/ios`)
  - ExpoModulesCore (from `../node_modules/expo-modules-core`)
  - fast_float (from `../node_modules/react-native/third-party-podspecs/fast_float.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - fmt (from `../node_modules/react-native/third-party-podspecs/fmt.podspec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTDeprecation (from `../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-defaultsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/defaults`)
  - React-domnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/dom`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricComponents (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-featureflagsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/featureflags`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-idlecallbacksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsinspectorcdp (from `../node_modules/react-native/ReactCommon/jsinspector-modern/cdp`)
  - React-jsinspectornetwork (from `../node_modules/react-native/ReactCommon/jsinspector-modern/network`)
  - React-jsinspectortracing (from `../node_modules/react-native/ReactCommon/jsinspector-modern/tracing`)
  - React-jsitooling (from `../node_modules/react-native/ReactCommon/jsitooling`)
  - React-jsitracing (from `../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - React-microtasksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/microtasks`)
  - react-native-maps (from `../node_modules/react-native-maps`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-oscompat (from `../node_modules/react-native/ReactCommon/oscompat`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-performancetimeline (from `../node_modules/react-native/ReactCommon/react/performance/timeline`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTFBReactNativeSpec (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTRuntime (from `../node_modules/react-native/React/Runtime`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererconsistency (from `../node_modules/react-native/ReactCommon/react/renderer/consistency`)
  - React-renderercss (from `../node_modules/react-native/ReactCommon/react/renderer/css`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-RuntimeApple (from `../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-RuntimeHermes (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-timing (from `../node_modules/react-native/ReactCommon/react/timing`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactAppDependencyProvider (from `build/generated/ios`)
  - ReactCodegen (from `build/generated/ios`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - SocketRocket (~> 0.7.1)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - SocketRocket

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  EXConstants:
    :path: "../node_modules/expo-constants/ios"
  Expo:
    :path: "../node_modules/expo"
  ExpoAsset:
    :path: "../node_modules/expo-asset/ios"
  ExpoFileSystem:
    :path: "../node_modules/expo-file-system/ios"
  ExpoFont:
    :path: "../node_modules/expo-font/ios"
  ExpoKeepAwake:
    :path: "../node_modules/expo-keep-awake/ios"
  ExpoModulesCore:
    :path: "../node_modules/expo-modules-core"
  fast_float:
    :podspec: "../node_modules/react-native/third-party-podspecs/fast_float.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  fmt:
    :podspec: "../node_modules/react-native/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2025-05-06-RNv0.80.0-4eb6132a5bf0450bf4c6c91987675381d7ac8bca
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTDeprecation:
    :path: "../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-defaultsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/defaults"
  React-domnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/dom"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricComponents:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../node_modules/react-native/ReactCommon/react/featureflags"
  React-featureflagsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/featureflags"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-idlecallbacksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsinspectorcdp:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern/cdp"
  React-jsinspectornetwork:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern/network"
  React-jsinspectortracing:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern/tracing"
  React-jsitooling:
    :path: "../node_modules/react-native/ReactCommon/jsitooling"
  React-jsitracing:
    :path: "../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  React-microtasksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/microtasks"
  react-native-maps:
    :path: "../node_modules/react-native-maps"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-oscompat:
    :path: "../node_modules/react-native/ReactCommon/oscompat"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-performancetimeline:
    :path: "../node_modules/react-native/ReactCommon/react/performance/timeline"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTFBReactNativeSpec:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTRuntime:
    :path: "../node_modules/react-native/React/Runtime"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererconsistency:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/consistency"
  React-renderercss:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/css"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-RuntimeApple:
    :path: "../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-timing:
    :path: "../node_modules/react-native/ReactCommon/react/timing"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactAppDependencyProvider:
    :path: build/generated/ios
  ReactCodegen:
    :path: build/generated/ios
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 7e761d76ca2ce687f7cc98e698152abd03a18f90
  DoubleConversion: cb417026b2400c8f53ae97020b2be961b59470cb
  EXConstants: 409690fbfd5afea964e5e9d6c4eb2c2b59222c59
  Expo: 8c995afb875c15bf8439af0b20bcb9ed8f90d0bd
  ExpoAsset: 323700f291684f110fb55f0d4022a3362ea9f875
  ExpoFileSystem: 80bfe850b1f9922c16905822ecbf97acd711dc51
  ExpoFont: 00756e6c796d8f7ee8d211e29c8b619e75cbf238
  ExpoKeepAwake: 3b8815d9dd1d419ee474df004021c69fdd316d08
  ExpoModulesCore: 022fcaf45f2b4f7fdf0bb8f1d3ce1381c87f5003
  fast_float: b32c788ed9c6a8c584d114d0047beda9664e7cc6
  FBLazyVector: 778b815a6fb3fa1599f581ffb9a5e85fad313c1d
  fmt: a40bb5bd0294ea969aaaba240a927bd33d878cdd
  glog: 5683914934d5b6e4240e497e0f4a3b42d1854183
  hermes-engine: 7068e976238b29e97b3bafd09a994542af7d5c0b
  RCT-Folly: 59ec0ac1f2f39672a0c6e6cecdd39383b764646f
  RCTDeprecation: ff787f6c860a1b97dd1bc27264b61d23ad1994da
  RCTRequired: 664eb8399ed8a83e26ab65af7c2ad390f7e61696
  RCTTypeSafety: a5cf7a7e80baf972e331dc028e5d5c19bb2535a4
  React: 606d4dccbcf29aec4dc84a7921405a28e1701a22
  React-callinvoker: 0e13bd3c039df9ceef04f7381a81f017655c8361
  React-Core: 701ad54ae468c2ca1e4869d659b30ebfee30ac77
  React-CoreModules: 99d3515898255378fa2d6fc906b6dca093d280c4
  React-cxxreact: 3410a1edbe15936bcf8eae61a546af1bec06ed98
  React-debug: a9e91845f3670c3a19249f52919f0488b7842cf7
  React-defaultsnativemodule: 8fad7c7173d6133d15b1532251df550d0d1c1f87
  React-domnativemodule: 1da1f2bc921a9e4652918f37285c3830f561c86b
  React-Fabric: e6f729f372f959bda89268c2c921fac55a9579dc
  React-FabricComponents: f2ab7d78be2ea1dd06a7d8d606f5740cd1f54041
  React-FabricImage: 220e8ce3ccdb483fd4283d8b21839676e8b88e27
  React-featureflags: b64383c3268d03c3fab25c03a5c7e5fab0931a55
  React-featureflagsnativemodule: 4c7b5cbe887d120a1797f65e6676fe9e1f9396ea
  React-graphics: 4031c43a78b816dc1043dca24dfabf1d2622df9a
  React-hermes: dc21a35794633bf2aef73645d273f5ee3bdf777a
  React-idlecallbacksnativemodule: 9d6ea7839e347ffd3791315ba418370421d6c7c7
  React-ImageManager: b743a715eca9abbf69fbd50732315565c9eb3863
  React-jserrorhandler: 850fe8285385ffa783cc73e5e2eda8ddcb84e147
  React-jsi: ea8a33b23165395610436c8f0d715e2c3bbcec7e
  React-jsiexecutor: 0fb247eca0908176917380e1e1b75339f52a0c72
  React-jsinspector: dcfc9ee7f2610ff05aa8f66fc8203cf7be875d0e
  React-jsinspectorcdp: 6803046f78af0b3caace9002e28b0ca1fd97c1c4
  React-jsinspectornetwork: b25ef98ec036aa1b454ebc904b983059e1ebc6e7
  React-jsinspectortracing: 777ae30cf41f6305ffc509e53bef86bb1027395f
  React-jsitooling: 568f4974066f14597084df606a6ad79fa52715b6
  React-jsitracing: 47cb4a6c4b3c5e2d1d32ff4880d74d5faf58423c
  React-logger: b69e65dc60f768e5509ac0cc27a360124bf70478
  React-Mapbuffer: b48f9f3311fd0ec0f7a5dc39d707eff521fb5f38
  React-microtasksnativemodule: d8568d0485a350c720c061ae835e09fc88c28715
  react-native-maps: b913850b77d4d4b53c8065b6697704f5264a6545
  react-native-safe-area-context: 31fa8b0bb05496c4005aa5560283f5015e809b91
  React-NativeModulesApple: f10596688a03af66804cfbe61792be24a7888da8
  React-oscompat: 7c0a341cc31e350da71ddf2e46de0a845d1d1626
  React-perflogger: 4cc44451f694d6205f47bd8d5d87c9c862c3335c
  React-performancetimeline: a81afec7aba50bdb80e5a692b03eff2dc499fe37
  React-RCTActionSheet: 99864bd8422649219f24eca9a51445e698b70b8e
  React-RCTAnimation: 7cb99a851a514673a1e48ca5fcbdee7c7c760da1
  React-RCTAppDelegate: cd3bc49cec7cef167e920d5e54194d161cd8ab6d
  React-RCTBlob: c96068eb67bf4a587f279db91c6948fc761826b9
  React-RCTFabric: ca43b2e7bf026a8898a4eea81e9306786a892065
  React-RCTFBReactNativeSpec: 96df6e569ad40c52f286762a59d7a96644567f5b
  React-RCTImage: c40e65f565882df880c4f8994940c8b070923239
  React-RCTLinking: 88992a3fb7c8caa868a2fc3489b26741e75ac5b5
  React-RCTNetwork: 89c9222b388d90229511cc974abee608ac9c1221
  React-RCTRuntime: 8a0222f21dacd0946aaff43976a06bd082e49e42
  React-RCTSettings: 9e7a5f4262523dee5a1f9b0fd1e674b2a11bd7db
  React-RCTText: 67f2955faca189ff85c3c5686505be9526df5461
  React-RCTVibration: e4fe5861cee22c972672d29da4cdf24b6313e01d
  React-rendererconsistency: a4db9bb060c65bce8ae83d936ed0719696055bd2
  React-renderercss: 77c768faf43570d50e3657b97ce1a4c4614012d6
  React-rendererdebug: 460dacb65d9ec58ba44e5c936b89e58530dd2a06
  React-rncore: 322add36430c38049067a5d365f166256975391f
  React-RuntimeApple: 9a7b848f3ea1b2aa6eefb0e42a5e113ed9b47f3d
  React-RuntimeCore: d9feb0e71b045780372d72b9fd0e4326c2ee97d8
  React-runtimeexecutor: 49ea276161508d50b3486c385e1ca7972d1699f5
  React-RuntimeHermes: 31f857c04fda874cefef4dfbd1c8b0d234c4d606
  React-runtimescheduler: 3cb2ab6622f9580b237a110350804933f8aec680
  React-timing: a275a1c2e6112dba17f8f7dd496d439213bbea0d
  React-utils: 257f8c08cb0559e458a9a9254967058434198ced
  ReactAppDependencyProvider: cd55f820247d424280ae0b94e1ffb38963410c01
  ReactCodegen: 304f881dea867b659e4ba479608ec06f8117e0c5
  ReactCommon: 658874decaf8c4fd76cfa3a878b94a869db85b1c
  RNCAsyncStorage: 767abb068db6ad28b5f59a129fbc9fab18b377e2
  RNGestureHandler: 9d04ec6e1379b595222c2467f5e8d1c44157fcc9
  RNScreens: 45a4564413205e2a1695d40bbc0297f6eefc9b74
  RNVectorIcons: 417c003b0ce7ac7748aa548720fd7127d1d74ded
  SocketRocket: d4aabe649be1e368d1318fdf28a022d714d65748
  Yoga: 395b5d614cd7cbbfd76b05d01bd67230a6ad004e

PODFILE CHECKSUM: 9945b159153f475868f7eff509756f3ed8f8429f

COCOAPODS: 1.16.2
