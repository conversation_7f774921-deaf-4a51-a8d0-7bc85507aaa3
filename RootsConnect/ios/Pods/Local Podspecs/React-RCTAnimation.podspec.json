{"name": "React-RCTAnimation", "version": "0.80.0", "summary": "A native driver for the Animated API.", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "compiler_flags": "-Wno-nullability-completeness", "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.80.0"}, "source_files": "**/*.{h,m,mm}", "preserve_paths": ["package.json", "LICENSE", "LICENSE-docs"], "header_dir": "RCTAnimation", "pod_target_xcconfig": {"USE_HEADERMAP": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "HEADER_SEARCH_PATHS": ["\"${PODS_ROOT}/Headers/Public/ReactCodegen/react/renderer/components\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFBReactNativeSpec/React_RCTFBReactNativeSpec.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-featureflags/React_featureflags.framework/Headers\"", "$(PODS_ROOT)/glog", "$(PODS_ROOT)/boost", "$(PODS_ROOT)/DoubleConversion", "$(PODS_ROOT)/fast_float/include", "$(PODS_ROOT)/fmt/include", "$(PODS_ROOT)/SocketRocket", "$(PODS_ROOT)/RCT-Folly"]}, "frameworks": ["UIKit", "QuartzCore"], "dependencies": {"RCTTypeSafety": [], "React-jsi": [], "React-Core/RCTAnimationHeaders": [], "React-RCTFBReactNativeSpec": [], "ReactCommon": [], "React-NativeModulesApple": [], "React-featureflags": [], "glog": [], "boost": [], "DoubleConversion": [], "fast_float": [], "fmt": [], "RCT-Folly": [], "SocketRocket": [], "RCT-Folly/Fabric": []}}