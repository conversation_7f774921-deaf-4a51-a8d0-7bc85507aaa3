{"name": "React-RuntimeHermes", "version": "0.80.0", "summary": "The React Native Runtime.", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.80.0"}, "source_files": "hermes/*.{cpp,h}", "header_dir": "react/runtime/hermes", "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": ["\"${PODS_TARGET_SRCROOT}/../..\"", "\"${PODS_TARGET_SRCROOT}/../../hermes/executor\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector/jsinspector_modern.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectorcdp/jsinspector_moderncdp.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectortracing/jsinspector_moderntracing.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsitooling/JSITooling.framework/Headers\"", "$(PODS_ROOT)/glog", "$(PODS_ROOT)/boost", "$(PODS_ROOT)/DoubleConversion", "$(PODS_ROOT)/fast_float/include", "$(PODS_ROOT)/fmt/include", "$(PODS_ROOT)/SocketRocket", "$(PODS_ROOT)/RCT-Folly"], "USE_HEADERMAP": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "GCC_WARN_PEDANTIC": "YES"}, "dependencies": {"React-jsitracing": [], "React-jsi": [], "React-utils": [], "React-RuntimeCore": [], "React-featureflags": [], "React-jsinspector": [], "React-jsinspectorcdp": [], "React-jsinspectortracing": [], "React-hermes": [], "hermes-engine": [], "React-jsitooling": [], "glog": [], "boost": [], "DoubleConversion": [], "fast_float": [], "fmt": [], "RCT-Folly": [], "SocketRocket": [], "RCT-Folly/Fabric": []}}