{"name": "React-RCTLinking", "version": "0.80.0", "summary": "A general interface to interact with both incoming and outgoing app links.", "homepage": "https://reactnative.dev/", "documentation_url": "https://reactnative.dev/docs/linking", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "compiler_flags": "-Wno-nullability-completeness", "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.80.0"}, "source_files": "*.{m,mm}", "preserve_paths": ["package.json", "LICENSE", "LICENSE-docs"], "header_dir": "RCTLinking", "pod_target_xcconfig": {"USE_HEADERMAP": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "HEADER_SEARCH_PATHS": "\"${PODS_ROOT}/Headers/Public/ReactCodegen/react/renderer/components\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFBReactNativeSpec/React_RCTFBReactNativeSpec.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers\" \"${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers/build/generated/ios\""}, "frameworks": "UIKit", "dependencies": {"React-Core/RCTLinkingHeaders": ["0.80.0"], "ReactCommon/turbomodule/core": ["0.80.0"], "React-jsi": ["0.80.0"], "React-RCTFBReactNativeSpec": [], "ReactCommon": [], "React-NativeModulesApple": []}}