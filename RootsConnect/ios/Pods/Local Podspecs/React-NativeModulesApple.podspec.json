{"name": "React-NativeModulesApple", "module_name": "React_NativeModulesApple", "header_dir": "ReactCommon", "version": "0.80.0", "summary": "-", "homepage": "https://reactnative.dev/", "license": "MIT", "authors": "Meta Platforms, Inc. and its affiliates", "platforms": {"ios": "15.1"}, "source": {"git": "https://github.com/facebook/react-native.git", "tag": "v0.80.0"}, "pod_target_xcconfig": {"HEADER_SEARCH_PATHS": ["\"$(PODS_ROOT)/Headers/Private/React-Core\"", "\"$(PODS_CONFIGURATION_BUILD_DIR)/React-debug/React_featureflags.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-featureflags/React_featureflags.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector/jsinspector_modern.framework/Headers\"", "\"${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectorcdp/jsinspector_moderncdp.framework/Headers\"", "$(PODS_ROOT)/glog", "$(PODS_ROOT)/boost", "$(PODS_ROOT)/DoubleConversion", "$(PODS_ROOT)/fast_float/include", "$(PODS_ROOT)/fmt/include", "$(PODS_ROOT)/SocketRocket", "$(PODS_ROOT)/RCT-Folly"], "USE_HEADERMAP": "YES", "CLANG_CXX_LANGUAGE_STANDARD": "c++20", "GCC_WARN_PEDANTIC": "YES"}, "source_files": "ReactCommon/**/*.{mm,cpp,h}", "dependencies": {"ReactCommon/turbomodule/core": [], "ReactCommon/turbomodule/bridging": [], "React-callinvoker": [], "React-Core": [], "React-cxxreact": [], "React-jsi": [], "React-featureflags": [], "React-runtimeexecutor": [], "React-jsinspector": [], "React-jsinspectorcdp": [], "hermes-engine": [], "React-hermes": [], "glog": [], "boost": [], "DoubleConversion": [], "fast_float": [], "fmt": [], "RCT-Folly": [], "SocketRocket": [], "RCT-Folly/Fabric": []}}