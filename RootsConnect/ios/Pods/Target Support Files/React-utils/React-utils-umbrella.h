#ifdef __OBJC__
#import <UIKit/UIKit.h>
#else
#ifndef FOUNDATION_EXPORT
#if defined(__cplusplus)
#define FOUNDATION_EXPORT extern "C"
#else
#define FOUNDATION_EXPORT extern
#endif
#endif
#endif

#import "react/utils/ContextContainer.h"
#import "react/utils/FloatComparison.h"
#import "react/utils/fnv1a.h"
#import "react/utils/hash_combine.h"
#import "react/utils/iequals.h"
#import "react/utils/jsi-utils.h"
#import "react/utils/ManagedObjectWrapper.h"
#import "react/utils/OnScopeExit.h"
#import "react/utils/PackTraits.h"
#import "react/utils/RunLoopObserver.h"
#import "react/utils/SharedFunction.h"
#import "react/utils/SimpleThreadSafeCache.h"
#import "react/utils/Telemetry.h"
#import "react/utils/TemplateStringLiteral.h"
#import "react/utils/toLower.h"
#import "react/utils/to_underlying.h"
#import "react/utils/LowPriorityExecutor.h"

FOUNDATION_EXPORT double react_utilsVersionNumber;
FOUNDATION_EXPORT const unsigned char react_utilsVersionString[];

