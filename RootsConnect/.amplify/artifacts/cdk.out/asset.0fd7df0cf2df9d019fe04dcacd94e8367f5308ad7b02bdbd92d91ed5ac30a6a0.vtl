## [Start] Setting "isLocationVisible" to default value of "false". **
$util.qr($context.args.input.put("isLocationVisible", $util.defaultIfNull($ctx.args.input.isLocationVisible, false)))
## [End] Setting "isLocationVisible" to default value of "false". **
## [Start] Setting "isProfilePublic" to default value of "false". **
$util.qr($context.args.input.put("isProfilePublic", $util.defaultIfNull($ctx.args.input.isProfilePublic, false)))
## [End] Setting "isProfilePublic" to default value of "false". **
## [Start] Setting "shareContactInfo" to default value of "false". **
$util.qr($context.args.input.put("shareContactInfo", $util.defaultIfNull($ctx.args.input.shareContactInfo, false)))
## [End] Setting "shareContactInfo" to default value of "false". **
## [Start] Setting "allowDirectContact" to default value of "false". **
$util.qr($context.args.input.put("allowDirectContact", $util.defaultIfNull($ctx.args.input.allowDirectContact, false)))
## [End] Setting "allowDirectContact" to default value of "false". **
{}