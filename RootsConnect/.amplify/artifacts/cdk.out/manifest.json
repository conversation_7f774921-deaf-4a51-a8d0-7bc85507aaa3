{"version": "41.0.0", "artifacts": {"amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2.assets": {"type": "cdk:asset-manifest", "properties": {"file": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2.assets.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2": {"type": "aws:cloudformation:stack", "environment": "aws://unknown-account/unknown-region", "properties": {"templateFile": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2.template.json", "terminationProtection": false, "tags": {"amplify:deployment-type": "sandbox", "created-by": "amplify"}, "validateOnSynth": false, "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-deploy-role-${AWS::AccountId}-${AWS::Region}", "cloudFormationExecutionRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-cfn-exec-role-${AWS::AccountId}-${AWS::Region}", "stackTemplateAssetObjectUrl": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/61bc3308ebd4bebfe97eac41ab7b36eb45abdc351574c25f6fcf480c69ce0c97.json", "requiresBootstrapStackVersion": 6, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version", "additionalDependencies": ["amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2.assets"], "lookupRole": {"arn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-lookup-role-${AWS::AccountId}-${AWS::Region}", "requiresBootstrapStackVersion": 8, "bootstrapStackVersionSsmParameter": "/cdk-bootstrap/hnb659fds/version"}}, "dependencies": ["amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2.assets"], "metadata": {"/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2": [{"type": "aws:cdk:stack-tags", "data": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "created-by", "Value": "amplify"}]}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/deploymentType": [{"type": "aws:cdk:logicalId", "data": "deploymentType"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/region": [{"type": "aws:cdk:logicalId", "data": "region"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth/amplifyAuth/UserPool/Resource": [{"type": "aws:cdk:logicalId", "data": "amplifyAuthUserPool4BA7F805"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth/amplifyAuth/UserPoolAppClient/Resource": [{"type": "aws:cdk:logicalId", "data": "amplifyAuthUserPoolAppClient2626C6F8"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth/amplifyAuth/IdentityPool": [{"type": "aws:cdk:logicalId", "data": "amplifyAuthIdentityPool3FDE84CC"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth/amplifyAuth/authenticatedUserRole/Resource": [{"type": "aws:cdk:logicalId", "data": "amplifyAuthauthenticatedUserRoleD8DA3689"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth/amplifyAuth/unauthenticatedUserRole/Resource": [{"type": "aws:cdk:logicalId", "data": "amplifyAuthunauthenticatedUserRole2B524D9E"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth/amplifyAuth/IdentityPoolRoleAttachment": [{"type": "aws:cdk:logicalId", "data": "amplifyAuthIdentityPoolRoleAttachment045F17C8"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth/CDKMetadata/Condition": [{"type": "aws:cdk:logicalId", "data": "CDKMetadataAvailable"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth/amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolA19BA9C3Ref": [{"type": "aws:cdk:logicalId", "data": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolA19BA9C3Ref"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth/amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolAppClient7D0F701BRef": [{"type": "aws:cdk:logicalId", "data": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolAppClient7D0F701BRef"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth/amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef": [{"type": "aws:cdk:logicalId", "data": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth/amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref": [{"type": "aws:cdk:logicalId", "data": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth/amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef": [{"type": "aws:cdk:logicalId", "data": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth.NestedStack/auth.NestedStackResource": [{"type": "aws:cdk:logicalId", "data": "auth179371D7"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/userPoolId": [{"type": "aws:cdk:logicalId", "data": "userPoolId"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/webClientId": [{"type": "aws:cdk:logicalId", "data": "webClientId"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/identityPoolId": [{"type": "aws:cdk:logicalId", "data": "identityPoolId"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/authRegion": [{"type": "aws:cdk:logicalId", "data": "authRegion"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/allowUnauthenticatedIdentities": [{"type": "aws:cdk:logicalId", "data": "allowUnauthenticatedIdentities"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/signupAttributes": [{"type": "aws:cdk:logicalId", "data": "signupAttributes"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/usernameAttributes": [{"type": "aws:cdk:logicalId", "data": "usernameAttributes"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/verificationMechanisms": [{"type": "aws:cdk:logicalId", "data": "verificationMechanisms"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/passwordPolicyMinLength": [{"type": "aws:cdk:logicalId", "data": "passwordPolicyMinLength"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/passwordPolicyRequirements": [{"type": "aws:cdk:logicalId", "data": "passwordPolicyRequirements"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/mfaConfiguration": [{"type": "aws:cdk:logicalId", "data": "mfaConfiguration"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/mfaTypes": [{"type": "aws:cdk:logicalId", "data": "mfaTypes"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/socialProviders": [{"type": "aws:cdk:logicalId", "data": "socialProviders"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/oauthCognitoDomain": [{"type": "aws:cdk:logicalId", "data": "oauthCognitoDomain"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/oauthScope": [{"type": "aws:cdk:logicalId", "data": "oauthScope"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/oauthRedirectSignIn": [{"type": "aws:cdk:logicalId", "data": "oauthRedirectSignIn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/oauthRedirectSignOut": [{"type": "aws:cdk:logicalId", "data": "oauthRedirectSignOut"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/oauthResponseType": [{"type": "aws:cdk:logicalId", "data": "oauthResponseType"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/oauthClientId": [{"type": "aws:cdk:logicalId", "data": "oauthClientId"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/groups": [{"type": "aws:cdk:logicalId", "data": "groups"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/GraphQLAPI/Resource": [{"type": "aws:cdk:logicalId", "data": "amplifyDataGraphQLAPI42A6FA33"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/GraphQLAPI/TransformerSchema": [{"type": "aws:cdk:logicalId", "data": "amplifyDataGraphQLAPITransformerSchemaFF50A789"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/GraphQLAPI/DefaultApiKey": [{"type": "aws:cdk:logicalId", "data": "amplifyDataGraphQLAPIDefaultApiKey1C8ED374"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/GraphQLAPI/NONE_DS": [{"type": "graphqltransformer:resourceName", "data": "NONE_DS"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/GraphQLAPI/NONE_DS/Resource": [{"type": "aws:cdk:logicalId", "data": "amplifyDataGraphQLAPINONEDS684BF699"}, {"type": "graphqltransformer:resourceName", "data": "NONE_DS"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyManagedTableIsCompleteRole/Resource": [{"type": "aws:cdk:logicalId", "data": "AmplifyManagedTableIsCompleteRoleF825222C"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyManagedTableOnEventRole/Resource": [{"type": "aws:cdk:logicalId", "data": "AmplifyManagedTableOnEventRoleB4E71DEA"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyManagedTableOnEventRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "AmplifyManagedTableOnEventRoleDefaultPolicyF6DABCB6"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider/framework-onEvent/Resource": [{"type": "aws:cdk:logicalId", "data": "TableManagerCustomProviderframeworkonEvent1DFC2ECC"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider/framework-isComplete/Resource": [{"type": "aws:cdk:logicalId", "data": "TableManagerCustomProviderframeworkisComplete2E51021B"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine/Role/Resource": [{"type": "aws:cdk:logicalId", "data": "AmplifyTableWaiterStateMachineRole470BE899"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine/Role/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "AmplifyTableWaiterStateMachineRoleDefaultPolicy89F3836A"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine/Resource": [{"type": "aws:cdk:logicalId", "data": "AmplifyTableWaiterStateMachine060600BC"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/CDKMetadata/Condition": [{"type": "aws:cdk:logicalId", "data": "CDKMetadataAvailable"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn": [{"type": "aws:cdk:logicalId", "data": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager.NestedStack/AmplifyTableManager.NestedStackResource": [{"type": "aws:cdk:logicalId", "data": "amplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResource86290833"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/DynamoDBModelTableReadIOPS": [{"type": "aws:cdk:logicalId", "data": "DynamoDBModelTableReadIOPS"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/DynamoDBModelTableWriteIOPS": [{"type": "aws:cdk:logicalId", "data": "DynamoDBModelTableWriteIOPS"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/DynamoDBBillingMode": [{"type": "aws:cdk:logicalId", "data": "DynamoDBBillingMode"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/DynamoDBEnablePointInTimeRecovery": [{"type": "aws:cdk:logicalId", "data": "DynamoDBEnablePointInTimeRecovery"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/DynamoDBEnableServerSideEncryption": [{"type": "aws:cdk:logicalId", "data": "DynamoDBEnableServerSideEncryption"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/HasEnvironmentParameter": [{"type": "aws:cdk:logicalId", "data": "HasEnvironmentParameter"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/ShouldUsePayPerRequestBilling": [{"type": "aws:cdk:logicalId", "data": "ShouldUsePayPerRequestBilling"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/ShouldUsePointInTimeRecovery": [{"type": "aws:cdk:logicalId", "data": "ShouldUsePointInTimeRecovery"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileTable": [{"type": "graphqltransformer:resourceName", "data": "UserProfile"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileTable/Default/Default": [{"type": "aws:cdk:logicalId", "data": "UserProfileTable"}, {"type": "graphqltransformer:resourceName", "data": "UserProfile"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/CustomTableUserProfileTable": [{"type": "graphqltransformer:resourceName", "data": "UserProfile"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/GetAttUserProfileTableStreamArn": [{"type": "aws:cdk:logicalId", "data": "GetAttUserProfileTableStreamArn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/GetAttUserProfileTableName": [{"type": "aws:cdk:logicalId", "data": "GetAttUserProfileTableName"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileIAMRole": [{"type": "graphqltransformer:resourceName", "data": "UserProfileIAMRole"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileIAMRole/Resource": [{"type": "aws:cdk:logicalId", "data": "UserProfileIAMRoleA4E2934B"}, {"type": "graphqltransformer:resourceName", "data": "UserProfileIAMRole"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileDataSource": [{"type": "graphqltransformer:resourceName", "data": "UserProfileTable"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileDataSource/Resource": [{"type": "aws:cdk:logicalId", "data": "UserProfileDataSource"}, {"type": "graphqltransformer:resourceName", "data": "UserProfileTable"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerygetUserProfileauth0Function/QuerygetUserProfileauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QuerygetUserProfileauth0FunctionQuerygetUserProfileauth0FunctionAppSyncFunctionDAD22839"}, {"type": "graphqltransformer:resourceName", "data": "QuerygetUserProfileauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerygetUserProfilepostAuth0Function/QuerygetUserProfilepostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QuerygetUserProfilepostAuth0FunctionQuerygetUserProfilepostAuth0FunctionAppSyncFunction92890504"}, {"type": "graphqltransformer:resourceName", "data": "QuerygetUserProfilepostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QueryGetUserProfileDataResolverFn/QueryGetUserProfileDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QueryGetUserProfileDataResolverFnQueryGetUserProfileDataResolverFnAppSyncFunction8EB8BD57"}, {"type": "graphqltransformer:resourceName", "data": "QueryGetUserProfileDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/queryGetUserProfileResolver": [{"type": "aws:cdk:logicalId", "data": "GetUserProfileResolver"}, {"type": "graphqltransformer:resourceName", "data": "Query.getUserProfile"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerylistUserProfilesauth0Function/QuerylistUserProfilesauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QuerylistUserProfilesauth0FunctionQuerylistUserProfilesauth0FunctionAppSyncFunction468B41DC"}, {"type": "graphqltransformer:resourceName", "data": "QuerylistUserProfilesauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerylistUserProfilespostAuth0Function/QuerylistUserProfilespostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QuerylistUserProfilespostAuth0FunctionQuerylistUserProfilespostAuth0FunctionAppSyncFunction01122A7A"}, {"type": "graphqltransformer:resourceName", "data": "QuerylistUserProfilespostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QueryListUserProfilesDataResolverFn/QueryListUserProfilesDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QueryListUserProfilesDataResolverFnQueryListUserProfilesDataResolverFnAppSyncFunctionA05E8B7A"}, {"type": "graphqltransformer:resourceName", "data": "QueryListUserProfilesDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/queryListUserProfilesResolver": [{"type": "aws:cdk:logicalId", "data": "ListUserProfileResolver"}, {"type": "graphqltransformer:resourceName", "data": "Query.listUserProfiles"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfileinit0Function/MutationcreateUserProfileinit0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationcreateUserProfileinit0FunctionMutationcreateUserProfileinit0FunctionAppSyncFunction7EFE9ED4"}, {"type": "graphqltransformer:resourceName", "data": "MutationcreateUserProfileinit0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfileinit1Function/MutationcreateUserProfileinit1Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationcreateUserProfileinit1FunctionMutationcreateUserProfileinit1FunctionAppSyncFunction9B1EF9A1"}, {"type": "graphqltransformer:resourceName", "data": "MutationcreateUserProfileinit1Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfileauth0Function/MutationcreateUserProfileauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationcreateUserProfileauth0FunctionMutationcreateUserProfileauth0FunctionAppSyncFunctionCF3841B8"}, {"type": "graphqltransformer:resourceName", "data": "MutationcreateUserProfileauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfilepostAuth0Function/MutationcreateUserProfilepostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationcreateUserProfilepostAuth0FunctionMutationcreateUserProfilepostAuth0FunctionAppSyncFunctionF0C8FEFF"}, {"type": "graphqltransformer:resourceName", "data": "MutationcreateUserProfilepostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationCreateUserProfileDataResolverFn/MutationCreateUserProfileDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationCreateUserProfileDataResolverFnMutationCreateUserProfileDataResolverFnAppSyncFunction120FE646"}, {"type": "graphqltransformer:resourceName", "data": "MutationCreateUserProfileDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/mutationCreateUserProfileResolver": [{"type": "aws:cdk:logicalId", "data": "CreateUserProfileResolver"}, {"type": "graphqltransformer:resourceName", "data": "Mutation.createUserProfile"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationupdateUserProfileinit0Function/MutationupdateUserProfileinit0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationupdateUserProfileinit0FunctionMutationupdateUserProfileinit0FunctionAppSyncFunction9A840BAA"}, {"type": "graphqltransformer:resourceName", "data": "MutationupdateUserProfileinit0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationupdateUserProfileauth0Function/MutationupdateUserProfileauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationupdateUserProfileauth0FunctionMutationupdateUserProfileauth0FunctionAppSyncFunction7E8F945B"}, {"type": "graphqltransformer:resourceName", "data": "MutationupdateUserProfileauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationupdateUserProfilepostAuth0Function/MutationupdateUserProfilepostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationupdateUserProfilepostAuth0FunctionMutationupdateUserProfilepostAuth0FunctionAppSyncFunction206AFA76"}, {"type": "graphqltransformer:resourceName", "data": "MutationupdateUserProfilepostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationUpdateUserProfileDataResolverFn/MutationUpdateUserProfileDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationUpdateUserProfileDataResolverFnMutationUpdateUserProfileDataResolverFnAppSyncFunction1707D92F"}, {"type": "graphqltransformer:resourceName", "data": "MutationUpdateUserProfileDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/mutationUpdateUserProfileResolver": [{"type": "aws:cdk:logicalId", "data": "UpdateUserProfileResolver"}, {"type": "graphqltransformer:resourceName", "data": "Mutation.updateUserProfile"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationdeleteUserProfileauth0Function/MutationdeleteUserProfileauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationdeleteUserProfileauth0FunctionMutationdeleteUserProfileauth0FunctionAppSyncFunction4A3AC4FD"}, {"type": "graphqltransformer:resourceName", "data": "MutationdeleteUserProfileauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationdeleteUserProfilepostAuth0Function/MutationdeleteUserProfilepostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationdeleteUserProfilepostAuth0FunctionMutationdeleteUserProfilepostAuth0FunctionAppSyncFunctionEE3F21F4"}, {"type": "graphqltransformer:resourceName", "data": "MutationdeleteUserProfilepostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationDeleteUserProfileDataResolverFn/MutationDeleteUserProfileDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationDeleteUserProfileDataResolverFnMutationDeleteUserProfileDataResolverFnAppSyncFunction4835094F"}, {"type": "graphqltransformer:resourceName", "data": "MutationDeleteUserProfileDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/mutationDeleteUserProfileResolver": [{"type": "aws:cdk:logicalId", "data": "DeleteUserProfileResolver"}, {"type": "graphqltransformer:resourceName", "data": "Mutation.deleteUserProfile"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononCreateUserProfileauth0Function/SubscriptiononCreateUserProfileauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononCreateUserProfileauth0FunctionSubscriptiononCreateUserProfileauth0FunctionAppSyncFunctionD90BBD82"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononCreateUserProfileauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononCreateUserProfilepostAuth0Function/SubscriptiononCreateUserProfilepostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononCreateUserProfilepostAuth0FunctionSubscriptiononCreateUserProfilepostAuth0FunctionAppSyncFunctionD659A080"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononCreateUserProfilepostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnCreateUserProfileDataResolverFn/SubscriptionOnCreateUserProfileDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptionOnCreateUserProfileDataResolverFnSubscriptionOnCreateUserProfileDataResolverFnAppSyncFunction08AFEB08"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptionOnCreateUserProfileDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/subscriptionOnCreateUserProfileResolver": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononCreateUserProfileResolver"}, {"type": "graphqltransformer:resourceName", "data": "Subscription.onCreateUserProfile"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononUpdateUserProfileauth0Function/SubscriptiononUpdateUserProfileauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononUpdateUserProfileauth0FunctionSubscriptiononUpdateUserProfileauth0FunctionAppSyncFunction48B7E90B"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononUpdateUserProfileauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononUpdateUserProfilepostAuth0Function/SubscriptiononUpdateUserProfilepostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononUpdateUserProfilepostAuth0FunctionSubscriptiononUpdateUserProfilepostAuth0FunctionAppSyncFunction446153A7"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononUpdateUserProfilepostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnUpdateUserProfileDataResolverFn/SubscriptionOnUpdateUserProfileDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptionOnUpdateUserProfileDataResolverFnSubscriptionOnUpdateUserProfileDataResolverFnAppSyncFunctionBCF119CA"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptionOnUpdateUserProfileDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/subscriptionOnUpdateUserProfileResolver": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononUpdateUserProfileResolver"}, {"type": "graphqltransformer:resourceName", "data": "Subscription.onUpdateUserProfile"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononDeleteUserProfileauth0Function/SubscriptiononDeleteUserProfileauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononDeleteUserProfileauth0FunctionSubscriptiononDeleteUserProfileauth0FunctionAppSyncFunction4558BD0E"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononDeleteUserProfileauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononDeleteUserProfilepostAuth0Function/SubscriptiononDeleteUserProfilepostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononDeleteUserProfilepostAuth0FunctionSubscriptiononDeleteUserProfilepostAuth0FunctionAppSyncFunction3E69A1F9"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononDeleteUserProfilepostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnDeleteUserProfileDataResolverFn/SubscriptionOnDeleteUserProfileDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptionOnDeleteUserProfileDataResolverFnSubscriptionOnDeleteUserProfileDataResolverFnAppSyncFunction6EDD6652"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptionOnDeleteUserProfileDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/subscriptionOnDeleteUserProfileResolver": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononDeleteUserProfileResolver"}, {"type": "graphqltransformer:resourceName", "data": "Subscription.onDeleteUserProfile"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileOwnerDataResolverFn/UserProfileOwnerDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "UserProfileOwnerDataResolverFnUserProfileOwnerDataResolverFnAppSyncFunction23A35218"}, {"type": "graphqltransformer:resourceName", "data": "UserProfileOwnerDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/userProfileOwnerResolver": [{"type": "aws:cdk:logicalId", "data": "UserProfileownerResolver"}, {"type": "graphqltransformer:resourceName", "data": "UserProfile.owner"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/CDKMetadata/Condition": [{"type": "aws:cdk:logicalId", "data": "CDKMetadataAvailable"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916OutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableCA8BFB67"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile.NestedStack/UserProfile.NestedStackResource": [{"type": "aws:cdk:logicalId", "data": "amplifyDataUserProfileNestedStackUserProfileNestedStackResource1B3713C5"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/DynamoDBModelTableReadIOPS": [{"type": "aws:cdk:logicalId", "data": "DynamoDBModelTableReadIOPS"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/DynamoDBModelTableWriteIOPS": [{"type": "aws:cdk:logicalId", "data": "DynamoDBModelTableWriteIOPS"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/DynamoDBBillingMode": [{"type": "aws:cdk:logicalId", "data": "DynamoDBBillingMode"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/DynamoDBEnablePointInTimeRecovery": [{"type": "aws:cdk:logicalId", "data": "DynamoDBEnablePointInTimeRecovery"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/DynamoDBEnableServerSideEncryption": [{"type": "aws:cdk:logicalId", "data": "DynamoDBEnableServerSideEncryption"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/HasEnvironmentParameter": [{"type": "aws:cdk:logicalId", "data": "HasEnvironmentParameter"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ShouldUsePayPerRequestBilling": [{"type": "aws:cdk:logicalId", "data": "ShouldUsePayPerRequestBilling"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ShouldUsePointInTimeRecovery": [{"type": "aws:cdk:logicalId", "data": "ShouldUsePointInTimeRecovery"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestTable": [{"type": "graphqltransformer:resourceName", "data": "ConnectionRequest"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestTable/Default/Default": [{"type": "aws:cdk:logicalId", "data": "ConnectionRequestTable"}, {"type": "graphqltransformer:resourceName", "data": "ConnectionRequest"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/CustomTableConnectionRequestTable": [{"type": "graphqltransformer:resourceName", "data": "ConnectionRequest"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/GetAttConnectionRequestTableStreamArn": [{"type": "aws:cdk:logicalId", "data": "GetAttConnectionRequestTableStreamArn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/GetAttConnectionRequestTableName": [{"type": "aws:cdk:logicalId", "data": "GetAttConnectionRequestTableName"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestIAMRole": [{"type": "graphqltransformer:resourceName", "data": "ConnectionRequestIAMRole"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestIAMRole/Resource": [{"type": "aws:cdk:logicalId", "data": "ConnectionRequestIAMRoleDBB29E05"}, {"type": "graphqltransformer:resourceName", "data": "ConnectionRequestIAMRole"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestDataSource": [{"type": "graphqltransformer:resourceName", "data": "ConnectionRequestTable"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestDataSource/Resource": [{"type": "aws:cdk:logicalId", "data": "ConnectionRequestDataSource"}, {"type": "graphqltransformer:resourceName", "data": "ConnectionRequestTable"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerygetConnectionRequestauth0Function/QuerygetConnectionRequestauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QuerygetConnectionRequestauth0FunctionQuerygetConnectionRequestauth0FunctionAppSyncFunctionA41C0055"}, {"type": "graphqltransformer:resourceName", "data": "QuerygetConnectionRequestauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerygetConnectionRequestpostAuth0Function/QuerygetConnectionRequestpostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QuerygetConnectionRequestpostAuth0FunctionQuerygetConnectionRequestpostAuth0FunctionAppSyncFunction4CD4281F"}, {"type": "graphqltransformer:resourceName", "data": "QuerygetConnectionRequestpostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QueryGetConnectionRequestDataResolverFn/QueryGetConnectionRequestDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QueryGetConnectionRequestDataResolverFnQueryGetConnectionRequestDataResolverFnAppSyncFunctionE41CF200"}, {"type": "graphqltransformer:resourceName", "data": "QueryGetConnectionRequestDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/queryGetConnectionRequestResolver": [{"type": "aws:cdk:logicalId", "data": "GetConnectionRequestResolver"}, {"type": "graphqltransformer:resourceName", "data": "Query.getConnectionRequest"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerylistConnectionRequestsauth0Function/QuerylistConnectionRequestsauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QuerylistConnectionRequestsauth0FunctionQuerylistConnectionRequestsauth0FunctionAppSyncFunction3684FEED"}, {"type": "graphqltransformer:resourceName", "data": "QuerylistConnectionRequestsauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerylistConnectionRequestspostAuth0Function/QuerylistConnectionRequestspostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QuerylistConnectionRequestspostAuth0FunctionQuerylistConnectionRequestspostAuth0FunctionAppSyncFunction9E8C9C49"}, {"type": "graphqltransformer:resourceName", "data": "QuerylistConnectionRequestspostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QueryListConnectionRequestsDataResolverFn/QueryListConnectionRequestsDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QueryListConnectionRequestsDataResolverFnQueryListConnectionRequestsDataResolverFnAppSyncFunction824D6927"}, {"type": "graphqltransformer:resourceName", "data": "QueryListConnectionRequestsDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/queryListConnectionRequestsResolver": [{"type": "aws:cdk:logicalId", "data": "ListConnectionRequestResolver"}, {"type": "graphqltransformer:resourceName", "data": "Query.listConnectionRequests"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestinit0Function/MutationcreateConnectionRequestinit0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationcreateConnectionRequestinit0FunctionMutationcreateConnectionRequestinit0FunctionAppSyncFunction247F59E8"}, {"type": "graphqltransformer:resourceName", "data": "MutationcreateConnectionRequestinit0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestauth0Function/MutationcreateConnectionRequestauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationcreateConnectionRequestauth0FunctionMutationcreateConnectionRequestauth0FunctionAppSyncFunctionF8ADB385"}, {"type": "graphqltransformer:resourceName", "data": "MutationcreateConnectionRequestauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestpostAuth0Function/MutationcreateConnectionRequestpostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationcreateConnectionRequestpostAuth0FunctionMutationcreateConnectionRequestpostAuth0FunctionAppSyncFunctionFC25477B"}, {"type": "graphqltransformer:resourceName", "data": "MutationcreateConnectionRequestpostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationCreateConnectionRequestDataResolverFn/MutationCreateConnectionRequestDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationCreateConnectionRequestDataResolverFnMutationCreateConnectionRequestDataResolverFnAppSyncFunction6BFA2576"}, {"type": "graphqltransformer:resourceName", "data": "MutationCreateConnectionRequestDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/mutationCreateConnectionRequestResolver": [{"type": "aws:cdk:logicalId", "data": "CreateConnectionRequestResolver"}, {"type": "graphqltransformer:resourceName", "data": "Mutation.createConnectionRequest"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestinit0Function/MutationupdateConnectionRequestinit0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationupdateConnectionRequestinit0FunctionMutationupdateConnectionRequestinit0FunctionAppSyncFunction11EFE1FC"}, {"type": "graphqltransformer:resourceName", "data": "MutationupdateConnectionRequestinit0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestauth0Function/MutationupdateConnectionRequestauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationupdateConnectionRequestauth0FunctionMutationupdateConnectionRequestauth0FunctionAppSyncFunctionC3E63FA3"}, {"type": "graphqltransformer:resourceName", "data": "MutationupdateConnectionRequestauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestpostAuth0Function/MutationupdateConnectionRequestpostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationupdateConnectionRequestpostAuth0FunctionMutationupdateConnectionRequestpostAuth0FunctionAppSyncFunction5D2372C9"}, {"type": "graphqltransformer:resourceName", "data": "MutationupdateConnectionRequestpostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationUpdateConnectionRequestDataResolverFn/MutationUpdateConnectionRequestDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationUpdateConnectionRequestDataResolverFnMutationUpdateConnectionRequestDataResolverFnAppSyncFunctionB5413E62"}, {"type": "graphqltransformer:resourceName", "data": "MutationUpdateConnectionRequestDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/mutationUpdateConnectionRequestResolver": [{"type": "aws:cdk:logicalId", "data": "UpdateConnectionRequestResolver"}, {"type": "graphqltransformer:resourceName", "data": "Mutation.updateConnectionRequest"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationdeleteConnectionRequestauth0Function/MutationdeleteConnectionRequestauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationdeleteConnectionRequestauth0FunctionMutationdeleteConnectionRequestauth0FunctionAppSyncFunction12E9EEE6"}, {"type": "graphqltransformer:resourceName", "data": "MutationdeleteConnectionRequestauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationdeleteConnectionRequestpostAuth0Function/MutationdeleteConnectionRequestpostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationdeleteConnectionRequestpostAuth0FunctionMutationdeleteConnectionRequestpostAuth0FunctionAppSyncFunction3CA5FEC0"}, {"type": "graphqltransformer:resourceName", "data": "MutationdeleteConnectionRequestpostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationDeleteConnectionRequestDataResolverFn/MutationDeleteConnectionRequestDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationDeleteConnectionRequestDataResolverFnMutationDeleteConnectionRequestDataResolverFnAppSyncFunction6E38EBEC"}, {"type": "graphqltransformer:resourceName", "data": "MutationDeleteConnectionRequestDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/mutationDeleteConnectionRequestResolver": [{"type": "aws:cdk:logicalId", "data": "DeleteConnectionRequestResolver"}, {"type": "graphqltransformer:resourceName", "data": "Mutation.deleteConnectionRequest"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononCreateConnectionRequestauth0Function/SubscriptiononCreateConnectionRequestauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononCreateConnectionRequestauth0FunctionSubscriptiononCreateConnectionRequestauth0FunctionAppSyncFunction76718EB6"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononCreateConnectionRequestauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononCreateConnectionRequestpostAuth0Function/SubscriptiononCreateConnectionRequestpostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononCreateConnectionRequestpostAuth0FunctionSubscriptiononCreateConnectionRequestpostAuth0FunctionAppSyncFunction052BC134"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononCreateConnectionRequestpostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnCreateConnectionRequestDataResolverFn/SubscriptionOnCreateConnectionRequestDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptionOnCreateConnectionRequestDataResolverFnSubscriptionOnCreateConnectionRequestDataResolverFnAppSyncFunction36214076"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptionOnCreateConnectionRequestDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/subscriptionOnCreateConnectionRequestResolver": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononCreateConnectionRequestResolver"}, {"type": "graphqltransformer:resourceName", "data": "Subscription.onCreateConnectionRequest"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononUpdateConnectionRequestauth0Function/SubscriptiononUpdateConnectionRequestauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononUpdateConnectionRequestauth0FunctionSubscriptiononUpdateConnectionRequestauth0FunctionAppSyncFunction12E84EB8"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononUpdateConnectionRequestauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononUpdateConnectionRequestpostAuth0Function/SubscriptiononUpdateConnectionRequestpostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononUpdateConnectionRequestpostAuth0FunctionSubscriptiononUpdateConnectionRequestpostAuth0FunctionAppSyncFunction6109604D"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononUpdateConnectionRequestpostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnUpdateConnectionRequestDataResolverFn/SubscriptionOnUpdateConnectionRequestDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptionOnUpdateConnectionRequestDataResolverFnSubscriptionOnUpdateConnectionRequestDataResolverFnAppSyncFunction752B6F84"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptionOnUpdateConnectionRequestDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/subscriptionOnUpdateConnectionRequestResolver": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononUpdateConnectionRequestResolver"}, {"type": "graphqltransformer:resourceName", "data": "Subscription.onUpdateConnectionRequest"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononDeleteConnectionRequestauth0Function/SubscriptiononDeleteConnectionRequestauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononDeleteConnectionRequestauth0FunctionSubscriptiononDeleteConnectionRequestauth0FunctionAppSyncFunction683C01A0"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononDeleteConnectionRequestauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononDeleteConnectionRequestpostAuth0Function/SubscriptiononDeleteConnectionRequestpostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononDeleteConnectionRequestpostAuth0FunctionSubscriptiononDeleteConnectionRequestpostAuth0FunctionAppSyncFunctionE8AB16B2"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononDeleteConnectionRequestpostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnDeleteConnectionRequestDataResolverFn/SubscriptionOnDeleteConnectionRequestDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptionOnDeleteConnectionRequestDataResolverFnSubscriptionOnDeleteConnectionRequestDataResolverFnAppSyncFunctionDD6B194B"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptionOnDeleteConnectionRequestDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/subscriptionOnDeleteConnectionRequestResolver": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononDeleteConnectionRequestResolver"}, {"type": "graphqltransformer:resourceName", "data": "Subscription.onDeleteConnectionRequest"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestOwnerDataResolverFn/ConnectionRequestOwnerDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "ConnectionRequestOwnerDataResolverFnConnectionRequestOwnerDataResolverFnAppSyncFunction2C2E4DA9"}, {"type": "graphqltransformer:resourceName", "data": "ConnectionRequestOwnerDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/connectionRequestOwnerResolver": [{"type": "aws:cdk:logicalId", "data": "ConnectionRequestownerResolver"}, {"type": "graphqltransformer:resourceName", "data": "ConnectionRequest.owner"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/CDKMetadata/Condition": [{"type": "aws:cdk:logicalId", "data": "CDKMetadataAvailable"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916OutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableCA8BFB67"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest.NestedStack/ConnectionRequest.NestedStackResource": [{"type": "aws:cdk:logicalId", "data": "amplifyDataConnectionRequestNestedStackConnectionRequestNestedStackResource7B6F7DDC"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/DynamoDBModelTableReadIOPS": [{"type": "aws:cdk:logicalId", "data": "DynamoDBModelTableReadIOPS"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/DynamoDBModelTableWriteIOPS": [{"type": "aws:cdk:logicalId", "data": "DynamoDBModelTableWriteIOPS"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/DynamoDBBillingMode": [{"type": "aws:cdk:logicalId", "data": "DynamoDBBillingMode"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/DynamoDBEnablePointInTimeRecovery": [{"type": "aws:cdk:logicalId", "data": "DynamoDBEnablePointInTimeRecovery"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/DynamoDBEnableServerSideEncryption": [{"type": "aws:cdk:logicalId", "data": "DynamoDBEnableServerSideEncryption"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/HasEnvironmentParameter": [{"type": "aws:cdk:logicalId", "data": "HasEnvironmentParameter"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/ShouldUsePayPerRequestBilling": [{"type": "aws:cdk:logicalId", "data": "ShouldUsePayPerRequestBilling"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/ShouldUsePointInTimeRecovery": [{"type": "aws:cdk:logicalId", "data": "ShouldUsePointInTimeRecovery"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/ConnectionTable": [{"type": "graphqltransformer:resourceName", "data": "Connection"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/ConnectionTable/Default/Default": [{"type": "aws:cdk:logicalId", "data": "ConnectionTable"}, {"type": "graphqltransformer:resourceName", "data": "Connection"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/CustomTableConnectionTable": [{"type": "graphqltransformer:resourceName", "data": "Connection"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/GetAttConnectionTableStreamArn": [{"type": "aws:cdk:logicalId", "data": "GetAttConnectionTableStreamArn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/GetAttConnectionTableName": [{"type": "aws:cdk:logicalId", "data": "GetAttConnectionTableName"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/ConnectionIAMRole": [{"type": "graphqltransformer:resourceName", "data": "ConnectionIAMRole"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/ConnectionIAMRole/Resource": [{"type": "aws:cdk:logicalId", "data": "ConnectionIAMRole812E1EC8"}, {"type": "graphqltransformer:resourceName", "data": "ConnectionIAMRole"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/ConnectionDataSource": [{"type": "graphqltransformer:resourceName", "data": "ConnectionTable"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/ConnectionDataSource/Resource": [{"type": "aws:cdk:logicalId", "data": "ConnectionDataSource"}, {"type": "graphqltransformer:resourceName", "data": "ConnectionTable"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerygetConnectionauth0Function/QuerygetConnectionauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QuerygetConnectionauth0FunctionQuerygetConnectionauth0FunctionAppSyncFunctionFCA57863"}, {"type": "graphqltransformer:resourceName", "data": "QuerygetConnectionauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerygetConnectionpostAuth0Function/QuerygetConnectionpostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QuerygetConnectionpostAuth0FunctionQuerygetConnectionpostAuth0FunctionAppSyncFunction50FB355D"}, {"type": "graphqltransformer:resourceName", "data": "QuerygetConnectionpostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/QueryGetConnectionDataResolverFn/QueryGetConnectionDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QueryGetConnectionDataResolverFnQueryGetConnectionDataResolverFnAppSyncFunction7F579500"}, {"type": "graphqltransformer:resourceName", "data": "QueryGetConnectionDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/queryGetConnectionResolver": [{"type": "aws:cdk:logicalId", "data": "GetConnectionResolver"}, {"type": "graphqltransformer:resourceName", "data": "Query.getConnection"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerylistConnectionsauth0Function/QuerylistConnectionsauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QuerylistConnectionsauth0FunctionQuerylistConnectionsauth0FunctionAppSyncFunction35EE0264"}, {"type": "graphqltransformer:resourceName", "data": "QuerylistConnectionsauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerylistConnectionspostAuth0Function/QuerylistConnectionspostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QuerylistConnectionspostAuth0FunctionQuerylistConnectionspostAuth0FunctionAppSyncFunction8F17C74E"}, {"type": "graphqltransformer:resourceName", "data": "QuerylistConnectionspostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/QueryListConnectionsDataResolverFn/QueryListConnectionsDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "QueryListConnectionsDataResolverFnQueryListConnectionsDataResolverFnAppSyncFunction904DCC89"}, {"type": "graphqltransformer:resourceName", "data": "QueryListConnectionsDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/queryListConnectionsResolver": [{"type": "aws:cdk:logicalId", "data": "ListConnectionResolver"}, {"type": "graphqltransformer:resourceName", "data": "Query.listConnections"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectioninit0Function/MutationcreateConnectioninit0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationcreateConnectioninit0FunctionMutationcreateConnectioninit0FunctionAppSyncFunctionA6FE7EFA"}, {"type": "graphqltransformer:resourceName", "data": "MutationcreateConnectioninit0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectioninit1Function/MutationcreateConnectioninit1Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationcreateConnectioninit1FunctionMutationcreateConnectioninit1FunctionAppSyncFunction078CC4EA"}, {"type": "graphqltransformer:resourceName", "data": "MutationcreateConnectioninit1Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectionauth0Function/MutationcreateConnectionauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationcreateConnectionauth0FunctionMutationcreateConnectionauth0FunctionAppSyncFunction1149FE23"}, {"type": "graphqltransformer:resourceName", "data": "MutationcreateConnectionauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectionpostAuth0Function/MutationcreateConnectionpostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationcreateConnectionpostAuth0FunctionMutationcreateConnectionpostAuth0FunctionAppSyncFunctionBBE6C4B6"}, {"type": "graphqltransformer:resourceName", "data": "MutationcreateConnectionpostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationCreateConnectionDataResolverFn/MutationCreateConnectionDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationCreateConnectionDataResolverFnMutationCreateConnectionDataResolverFnAppSyncFunctionB09DB127"}, {"type": "graphqltransformer:resourceName", "data": "MutationCreateConnectionDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/mutationCreateConnectionResolver": [{"type": "aws:cdk:logicalId", "data": "CreateConnectionResolver"}, {"type": "graphqltransformer:resourceName", "data": "Mutation.createConnection"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectioninit0Function/MutationupdateConnectioninit0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationupdateConnectioninit0FunctionMutationupdateConnectioninit0FunctionAppSyncFunction8D243403"}, {"type": "graphqltransformer:resourceName", "data": "MutationupdateConnectioninit0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectionauth0Function/MutationupdateConnectionauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationupdateConnectionauth0FunctionMutationupdateConnectionauth0FunctionAppSyncFunction981E4BF8"}, {"type": "graphqltransformer:resourceName", "data": "MutationupdateConnectionauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectionpostAuth0Function/MutationupdateConnectionpostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationupdateConnectionpostAuth0FunctionMutationupdateConnectionpostAuth0FunctionAppSyncFunctionF984A8D8"}, {"type": "graphqltransformer:resourceName", "data": "MutationupdateConnectionpostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationUpdateConnectionDataResolverFn/MutationUpdateConnectionDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationUpdateConnectionDataResolverFnMutationUpdateConnectionDataResolverFnAppSyncFunction8C462233"}, {"type": "graphqltransformer:resourceName", "data": "MutationUpdateConnectionDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/mutationUpdateConnectionResolver": [{"type": "aws:cdk:logicalId", "data": "UpdateConnectionResolver"}, {"type": "graphqltransformer:resourceName", "data": "Mutation.updateConnection"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationdeleteConnectionauth0Function/MutationdeleteConnectionauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationdeleteConnectionauth0FunctionMutationdeleteConnectionauth0FunctionAppSyncFunction70D5026B"}, {"type": "graphqltransformer:resourceName", "data": "MutationdeleteConnectionauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationdeleteConnectionpostAuth0Function/MutationdeleteConnectionpostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationdeleteConnectionpostAuth0FunctionMutationdeleteConnectionpostAuth0FunctionAppSyncFunction78F7B2E6"}, {"type": "graphqltransformer:resourceName", "data": "MutationdeleteConnectionpostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationDeleteConnectionDataResolverFn/MutationDeleteConnectionDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "MutationDeleteConnectionDataResolverFnMutationDeleteConnectionDataResolverFnAppSyncFunction6BDC0894"}, {"type": "graphqltransformer:resourceName", "data": "MutationDeleteConnectionDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/mutationDeleteConnectionResolver": [{"type": "aws:cdk:logicalId", "data": "DeleteConnectionResolver"}, {"type": "graphqltransformer:resourceName", "data": "Mutation.deleteConnection"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononCreateConnectionauth0Function/SubscriptiononCreateConnectionauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononCreateConnectionauth0FunctionSubscriptiononCreateConnectionauth0FunctionAppSyncFunctionF1660BDA"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononCreateConnectionauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononCreateConnectionpostAuth0Function/SubscriptiononCreateConnectionpostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononCreateConnectionpostAuth0FunctionSubscriptiononCreateConnectionpostAuth0FunctionAppSyncFunction374552A2"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononCreateConnectionpostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnCreateConnectionDataResolverFn/SubscriptionOnCreateConnectionDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptionOnCreateConnectionDataResolverFnSubscriptionOnCreateConnectionDataResolverFnAppSyncFunction49D15552"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptionOnCreateConnectionDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/subscriptionOnCreateConnectionResolver": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononCreateConnectionResolver"}, {"type": "graphqltransformer:resourceName", "data": "Subscription.onCreateConnection"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononUpdateConnectionauth0Function/SubscriptiononUpdateConnectionauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononUpdateConnectionauth0FunctionSubscriptiononUpdateConnectionauth0FunctionAppSyncFunction72B83498"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononUpdateConnectionauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononUpdateConnectionpostAuth0Function/SubscriptiononUpdateConnectionpostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononUpdateConnectionpostAuth0FunctionSubscriptiononUpdateConnectionpostAuth0FunctionAppSyncFunction9A86377C"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononUpdateConnectionpostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnUpdateConnectionDataResolverFn/SubscriptionOnUpdateConnectionDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptionOnUpdateConnectionDataResolverFnSubscriptionOnUpdateConnectionDataResolverFnAppSyncFunction454D368C"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptionOnUpdateConnectionDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/subscriptionOnUpdateConnectionResolver": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononUpdateConnectionResolver"}, {"type": "graphqltransformer:resourceName", "data": "Subscription.onUpdateConnection"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononDeleteConnectionauth0Function/SubscriptiononDeleteConnectionauth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononDeleteConnectionauth0FunctionSubscriptiononDeleteConnectionauth0FunctionAppSyncFunction5B18C89C"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononDeleteConnectionauth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononDeleteConnectionpostAuth0Function/SubscriptiononDeleteConnectionpostAuth0Function.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononDeleteConnectionpostAuth0FunctionSubscriptiononDeleteConnectionpostAuth0FunctionAppSyncFunction10BDC99C"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptiononDeleteConnectionpostAuth0Function"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnDeleteConnectionDataResolverFn/SubscriptionOnDeleteConnectionDataResolverFn.AppSyncFunction": [{"type": "aws:cdk:logicalId", "data": "SubscriptionOnDeleteConnectionDataResolverFnSubscriptionOnDeleteConnectionDataResolverFnAppSyncFunctionE824ED99"}, {"type": "graphqltransformer:resourceName", "data": "SubscriptionOnDeleteConnectionDataResolverFn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/subscriptionOnDeleteConnectionResolver": [{"type": "aws:cdk:logicalId", "data": "SubscriptiononDeleteConnectionResolver"}, {"type": "graphqltransformer:resourceName", "data": "Subscription.onDeleteConnection"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/CDKMetadata/Condition": [{"type": "aws:cdk:logicalId", "data": "CDKMetadataAvailable"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916OutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableCA8BFB67"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection.NestedStack/Connection.NestedStackResource": [{"type": "aws:cdk:logicalId", "data": "amplifyDataConnectionNestedStackConnectionNestedStackResource0AD3F46E"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/Resource": [{"type": "aws:cdk:logicalId", "data": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/Policy/Resource": [{"type": "aws:cdk:logicalId", "data": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucketPolicyF1C1C548"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/AutoDeleteObjectsCustomResource/Default": [{"type": "aws:cdk:logicalId", "data": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucketAutoDeleteObjectsCustomResource437F26F5"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/AwsCliLayer/Resource": [{"type": "aws:cdk:logicalId", "data": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentAwsCliLayerE322F905"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/CustomResource-1536MiB/Default": [{"type": "aws:cdk:logicalId", "data": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentCustomResource1536MiB21775929"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/DynamoDBModelTableReadIOPS": [{"type": "aws:cdk:logicalId", "data": "DynamoDBModelTableReadIOPS"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/DynamoDBModelTableWriteIOPS": [{"type": "aws:cdk:logicalId", "data": "DynamoDBModelTableWriteIOPS"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/DynamoDBBillingMode": [{"type": "aws:cdk:logicalId", "data": "DynamoDBBillingMode"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/DynamoDBEnablePointInTimeRecovery": [{"type": "aws:cdk:logicalId", "data": "DynamoDBEnablePointInTimeRecovery"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/DynamoDBEnableServerSideEncryption": [{"type": "aws:cdk:logicalId", "data": "DynamoDBEnableServerSideEncryption"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/LatestNodeRuntimeMap": [{"type": "aws:cdk:logicalId", "data": "LatestNodeRuntimeMap"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/Custom::S3AutoDeleteObjectsCustomResourceProvider": [{"type": "aws:cdk:is-custom-resource-handler-customResourceProvider", "data": true}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/Custom::S3AutoDeleteObjectsCustomResourceProvider/Role": [{"type": "aws:cdk:logicalId", "data": "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/Custom::S3AutoDeleteObjectsCustomResourceProvider/Handler": [{"type": "aws:cdk:logicalId", "data": "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB": [{"type": "aws:cdk:is-custom-resource-handler-singleton", "data": true}, {"type": "aws:cdk:is-custom-resource-handler-runtime-family", "data": 2}, {"type": "aws:cdk:is-custom-resource-handler-singleton", "data": true}, {"type": "aws:cdk:is-custom-resource-handler-runtime-family", "data": 2}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/ServiceRole/Resource": [{"type": "aws:cdk:logicalId", "data": "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleA41FC8C2"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/ServiceRole/DefaultPolicy/Resource": [{"type": "aws:cdk:logicalId", "data": "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleDefaultPolicyFF1C635B"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/Resource": [{"type": "aws:cdk:logicalId", "data": "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBC5D8AB21"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucket/Resource": [{"type": "aws:cdk:logicalId", "data": "modelIntrospectionSchemaBucketF566B665"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucket/Policy/Resource": [{"type": "aws:cdk:logicalId", "data": "modelIntrospectionSchemaBucketPolicy4DAB0D15"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucket/AutoDeleteObjectsCustomResource/Default": [{"type": "aws:cdk:logicalId", "data": "modelIntrospectionSchemaBucketAutoDeleteObjectsCustomResourceFE57309F"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucketDeployment/AwsCliLayer/Resource": [{"type": "aws:cdk:logicalId", "data": "modelIntrospectionSchemaBucketDeploymentAwsCliLayer13C432F7"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucketDeployment/CustomResource-1536MiB/Default": [{"type": "aws:cdk:logicalId", "data": "modelIntrospectionSchemaBucketDeploymentCustomResource1536MiB104B97EC"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/AMPLIFY_DATA_GRAPHQL_ENDPOINTParameter/Resource": [{"type": "aws:cdk:logicalId", "data": "AMPLIFYDATAGRAPHQLENDPOINTParameter1C2CBB16"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_BUCKET_NAMEParameter/Resource": [{"type": "aws:cdk:logicalId", "data": "AMPLIFYDATAMODELINTROSPECTIONSCHEMABUCKETNAMEParameter47BF4F44"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_KEYParameter/Resource": [{"type": "aws:cdk:logicalId", "data": "AMPLIFYDATAMODELINTROSPECTIONSCHEMAKEYParameterB6AEAE8A"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/AMPLIFY_DATA_DEFAULT_NAMEParameter/Resource": [{"type": "aws:cdk:logicalId", "data": "AMPLIFYDATADEFAULTNAMEParameterE7C23CC4"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/CDKMetadata/Condition": [{"type": "aws:cdk:logicalId", "data": "CDKMetadataAvailable"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolA19BA9C3Ref": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolA19BA9C3Ref"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef": [{"type": "aws:cdk:logicalId", "data": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": [{"type": "aws:cdk:logicalId", "data": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2GraphQLUrl": [{"type": "aws:cdk:logicalId", "data": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2GraphQLUrl"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentCustomResource1536MiB60279CB2DestinationBucketArn": [{"type": "aws:cdk:logicalId", "data": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentCustomResource1536MiB60279CB2DestinationBucketArn"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPIDefaultApiKey9D3276BBApiKey": [{"type": "aws:cdk:logicalId", "data": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPIDefaultApiKey9D3276BBApiKey"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data.NestedStack/data.NestedStackResource": [{"type": "aws:cdk:logicalId", "data": "data7552DF31"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/awsAppsyncApiId": [{"type": "aws:cdk:logicalId", "data": "awsAppsyncApiId"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/awsAppsyncApiEndpoint": [{"type": "aws:cdk:logicalId", "data": "awsAppsyncApiEndpoint"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/awsAppsyncAuthenticationType": [{"type": "aws:cdk:logicalId", "data": "awsAppsyncAuthenticationType"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/awsAppsyncRegion": [{"type": "aws:cdk:logicalId", "data": "awsAppsyncRegion"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/amplifyApiModelSchemaS3Uri": [{"type": "aws:cdk:logicalId", "data": "amplifyApiModelSchemaS3Uri"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/awsAppsyncApiKey": [{"type": "aws:cdk:logicalId", "data": "awsAppsyncApiKey"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/awsAppsyncAdditionalAuthenticationTypes": [{"type": "aws:cdk:logicalId", "data": "awsAppsyncAdditionalAuthenticationTypes"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/CDKMetadata/Default": [{"type": "aws:cdk:logicalId", "data": "CDKMetadata"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/CDKMetadata/Condition": [{"type": "aws:cdk:logicalId", "data": "CDKMetadataAvailable"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/BootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "BootstrapVersion"}], "/amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/CheckBootstrapVersion": [{"type": "aws:cdk:logicalId", "data": "CheckBootstrapVersion"}]}, "displayName": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2"}, "Tree": {"type": "cdk:tree", "properties": {"file": "tree.json"}}}, "minimumCliVersion": "2.1005.0"}