{"version": "tree-0.1", "tree": {"id": "App", "path": "", "children": {"amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2": {"id": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2", "children": {"deploymentType": {"id": "deploymentType", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/deploymentType", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "region": {"id": "region", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/region", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "auth": {"id": "auth", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth", "children": {"amplifyAuth": {"id": "amplifyAuth", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth/amplifyAuth", "children": {"UserPool": {"id": "UserPool", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/auth/amplifyAuth/UserPool", "children": {"Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/auth/amplifyAuth/UserPool/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Cognito::UserPool", "aws:cdk:cloudformation:props": {"accountRecoverySetting": {"recoveryMechanisms": [{"name": "verified_email", "priority": 1}]}, "adminCreateUserConfig": {"allowAdminCreateUserOnly": false}, "autoVerifiedAttributes": ["email"], "emailVerificationMessage": "The verification code to your new account is {####}", "emailVerificationSubject": "Verify your new account", "policies": {"passwordPolicy": {"minimumLength": 8, "requireLowercase": true, "requireUppercase": true, "requireNumbers": true, "requireSymbols": true}}, "schema": [{"name": "email", "mutable": true, "required": true}, {"name": "phone_number", "mutable": true, "required": false}, {"name": "given_name", "mutable": true, "required": false}, {"name": "family_name", "mutable": true, "required": false}], "smsVerificationMessage": "The verification code to your new account is {####}", "userPoolTags": {"amplify:deployment-type": "sandbox", "amplify:friendly-name": "amplifyAuth", "created-by": "amplify"}, "userAttributeUpdateSettings": {"attributesRequireVerificationBeforeUpdate": ["email"]}, "usernameAttributes": ["email"], "usernameConfiguration": {"caseSensitive": false}, "verificationMessageTemplate": {"defaultEmailOption": "CONFIRM_WITH_CODE", "emailMessage": "The verification code to your new account is {####}", "emailSubject": "Verify your new account", "smsMessage": "The verification code to your new account is {####}"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_cognito.CfnUserPool", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_cognito.UserPool", "version": "2.189.1", "metadata": []}}, "UserPoolAppClient": {"id": "UserPoolAppClient", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/auth/amplifyAuth/UserPoolAppClient", "children": {"Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/auth/amplifyAuth/UserPoolAppClient/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Cognito::UserPoolClient", "aws:cdk:cloudformation:props": {"allowedOAuthFlows": ["code"], "allowedOAuthFlowsUserPoolClient": true, "allowedOAuthScopes": ["profile", "phone", "email", "openid", "aws.cognito.signin.user.admin"], "callbackUrLs": ["https://example.com"], "explicitAuthFlows": ["ALLOW_CUSTOM_AUTH", "ALLOW_USER_SRP_AUTH", "ALLOW_REFRESH_TOKEN_AUTH"], "preventUserExistenceErrors": "ENABLED", "supportedIdentityProviders": ["COGNITO"], "userPoolId": {"Ref": "amplifyAuthUserPool4BA7F805"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_cognito.CfnUserPoolClient", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_cognito.UserPoolClient", "version": "2.189.1", "metadata": []}}, "IdentityPool": {"id": "IdentityPool", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/auth/amplifyAuth/IdentityPool", "attributes": {"aws:cdk:cloudformation:type": "AWS::Cognito::IdentityPool", "aws:cdk:cloudformation:props": {"allowUnauthenticatedIdentities": true, "identityPoolTags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyAuth"}, {"key": "created-by", "value": "amplify"}], "cognitoIdentityProviders": [{"clientId": {"Ref": "amplifyAuthUserPoolAppClient2626C6F8"}, "providerName": {"Fn::Join": ["", ["cognito-idp.", {"Ref": "AWS::Region"}, ".amazonaws.com/", {"Ref": "amplifyAuthUserPool4BA7F805"}]]}}], "supportedLoginProviders": {}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_cognito.CfnIdentityPool", "version": "2.189.1"}}, "authenticatedUserRole": {"id": "authenticatedUserRole", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/auth/amplifyAuth/authenticatedUserRole", "children": {"ImportauthenticatedUserRole": {"id": "ImportauthenticatedUserRole", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/auth/amplifyAuth/authenticatedUserRole/ImportauthenticatedUserRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON><PERSON>-sandbox-183ad0d0d2/auth/amplifyAuth/authenticatedUserRole/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:AssumeRoleWithWebIdentity", "Condition": {"StringEquals": {"cognito-identity.amazonaws.com:aud": {"Ref": "amplifyAuthIdentityPool3FDE84CC"}}, "ForAnyValue:StringLike": {"cognito-identity.amazonaws.com:amr": "authenticated"}}, "Effect": "Allow", "Principal": {"Federated": "cognito-identity.amazonaws.com"}}], "Version": "2012-10-17"}, "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyAuth"}, {"key": "created-by", "value": "amplify"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.189.1", "metadata": []}}, "unauthenticatedUserRole": {"id": "unauthenticatedUserRole", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/auth/amplifyAuth/unauthenticatedUserRole", "children": {"ImportunauthenticatedUserRole": {"id": "ImportunauthenticatedUserRole", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/auth/amplifyAuth/unauthenticatedUserRole/ImportunauthenticatedUserRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/auth/amplifyAuth/unauthenticatedUserRole/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:AssumeRoleWithWebIdentity", "Condition": {"StringEquals": {"cognito-identity.amazonaws.com:aud": {"Ref": "amplifyAuthIdentityPool3FDE84CC"}}, "ForAnyValue:StringLike": {"cognito-identity.amazonaws.com:amr": "unauthenticated"}}, "Effect": "Allow", "Principal": {"Federated": "cognito-identity.amazonaws.com"}}], "Version": "2012-10-17"}, "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyAuth"}, {"key": "created-by", "value": "amplify"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.189.1", "metadata": []}}, "IdentityPoolRoleAttachment": {"id": "IdentityPoolRoleAttachment", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/auth/amplifyAuth/IdentityPoolRoleAttachment", "attributes": {"aws:cdk:cloudformation:type": "AWS::Cognito::IdentityPoolRoleAttachment", "aws:cdk:cloudformation:props": {"identityPoolId": {"Ref": "amplifyAuthIdentityPool3FDE84CC"}, "roleMappings": {"UserPoolWebClientRoleMapping": {"type": "Token", "ambiguousRoleResolution": "AuthenticatedRole", "identityProvider": {"Fn::Join": ["", ["cognito-idp.", {"Ref": "AWS::Region"}, ".amazonaws.com/", {"Ref": "amplifyAuthUserPool4BA7F805"}, ":", {"Ref": "amplifyAuthUserPoolAppClient2626C6F8"}]]}}}, "roles": {"unauthenticated": {"Fn::GetAtt": ["amplifyAuthunauthenticatedUserRole2B524D9E", "<PERSON><PERSON>"]}, "authenticated": {"Fn::GetAtt": ["amplifyAuthauthenticatedUserRoleD8DA3689", "<PERSON><PERSON>"]}}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_cognito.CfnIdentityPoolRoleAttachment", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "CDKMetadata": {"id": "CDKMetadata", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth/CDKMetadata", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}, "Condition": {"id": "Condition", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/auth/CDKMetadata/Condition", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolA19BA9C3Ref": {"id": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolA19BA9C3Ref", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth/amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolA19BA9C3Ref", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolAppClient7D0F701BRef": {"id": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolAppClient7D0F701BRef", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth/amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolAppClient7D0F701BRef", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef": {"id": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth/amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref": {"id": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth/amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef": {"id": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth/amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.NestedStack", "version": "2.189.1"}}, "auth.NestedStack": {"id": "auth.<PERSON>", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth.NestedStack", "children": {"auth.NestedStackResource": {"id": "auth.NestedStackResource", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth.NestedStack/auth.NestedStackResource", "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFormation::Stack", "aws:cdk:cloudformation:props": {"tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "created-by", "value": "amplify"}], "templateUrl": {"Fn::Join": ["", ["https://s3.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/c66d0248273a35b9d9d4fae4883e6c8f7c0ab44e0096721ecde31e426d93d530.json"]]}}}, "constructInfo": {"fqn": "aws-cdk-lib.CfnStack", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "userPoolId": {"id": "userPoolId", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/userPoolId", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "webClientId": {"id": "webClientId", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/webClientId", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "identityPoolId": {"id": "identityPoolId", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/identityPoolId", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "authRegion": {"id": "authRegion", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/authRegion", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "allowUnauthenticatedIdentities": {"id": "allowUnauthenticatedIdentities", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/allowUnauthenticatedIdentities", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "signupAttributes": {"id": "signupAttributes", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/signupAttributes", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "usernameAttributes": {"id": "usernameAttributes", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/usernameAttributes", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "verificationMechanisms": {"id": "verificationMechanisms", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/verificationMechanisms", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "passwordPolicyMinLength": {"id": "passwordPolicyMinLength", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/passwordPolicyMinLength", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "passwordPolicyRequirements": {"id": "passwordPolicyRequirements", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/passwordPolicyRequirements", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "mfaConfiguration": {"id": "mfaConfiguration", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/mfaConfiguration", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "mfaTypes": {"id": "mfaTypes", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/mfaTypes", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "socialProviders": {"id": "socialProviders", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/socialProviders", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "oauthCognitoDomain": {"id": "oauthCognitoDomain", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/oauthCognitoDomain", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "oauthScope": {"id": "oauthScope", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/oauthScope", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "oauthRedirectSignIn": {"id": "oauthRedirectSignIn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/oauthRedirectSignIn", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "oauthRedirectSignOut": {"id": "oauthRedirectSignOut", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/oauthRedirectSignOut", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "oauthResponseType": {"id": "oauthResponseType", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/oauthResponseType", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "oauthClientId": {"id": "oauthClientId", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/oauthClientId", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "groups": {"id": "groups", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/groups", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "data": {"id": "data", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data", "children": {"amplifyData": {"id": "amplifyData", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData", "children": {"transformer-user-pool": {"id": "transformer-user-pool", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/transformer-user-pool", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "GraphQLAPI": {"id": "GraphQLAPI", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/GraphQLAPI", "children": {"Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/GraphQLAPI/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::GraphQLApi", "aws:cdk:cloudformation:props": {"additionalAuthenticationProviders": [{"authenticationType": "API_KEY"}, {"authenticationType": "AWS_IAM"}], "authenticationType": "AMAZON_COGNITO_USER_POOLS", "name": "amplifyData", "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}], "userPoolConfig": {"userPoolId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolA19BA9C3Ref"}, "awsRegion": {"Ref": "AWS::Region"}, "defaultAction": "ALLOW"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnGraphQLApi", "version": "2.189.1"}}, "TransformerSchema": {"id": "TransformerSchema", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/GraphQLAPI/TransformerSchema", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::GraphQLSchema", "aws:cdk:cloudformation:props": {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "definitionS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/6614acf21da877481fe2119444d7f87a06f93eb4fb0b938caaa77f109f816e43.graphql"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnGraphQLSchema", "version": "2.189.1"}}, "DefaultApiKey": {"id": "DefaultApiKey", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/GraphQLAPI/DefaultApiKey", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Api<PERSON>ey", "aws:cdk:cloudformation:props": {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "expires": **********}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnApiKey", "version": "2.189.1"}}, "NONE_DS": {"id": "NONE_DS", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/GraphQLAPI/NONE_DS", "children": {"Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/GraphQLAPI/NONE_DS/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::DataSource", "aws:cdk:cloudformation:props": {"apiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "description": "None Data Source for Pipeline functions", "name": "NONE_DS", "type": "NONE"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnDataSource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.NoneDataSource", "version": "2.189.1"}}, "schema": {"id": "schema", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/GraphQLAPI/schema", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/GraphQLAPI/schema/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/GraphQLAPI/schema/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.GraphqlApiBase", "version": "2.189.1", "metadata": []}}, "AmplifyTableManager": {"id": "AmplifyTableManager", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager", "children": {"AmplifyManagedTableIsCompleteRole": {"id": "AmplifyManagedTableIsCompleteRole", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyManagedTableIsCompleteRole", "children": {"ImportAmplifyManagedTableIsCompleteRole": {"id": "ImportAmplifyManagedTableIsCompleteRole", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyManagedTableIsCompleteRole/ImportAmplifyManagedTableIsCompleteRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyManagedTableIsCompleteRole/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}], "policies": [{"policyName": "CreateUpdateDeleteTablesPolicy", "policyDocument": {"Statement": [{"Action": ["dynamodb:CreateTable", "dynamodb:UpdateTable", "dynamodb:DeleteTable", "dynamodb:DescribeTable", "dynamodb:DescribeContinuousBackups", "dynamodb:DescribeTimeToLive", "dynamodb:UpdateContinuousBackups", "dynamodb:UpdateTimeToLive", "dynamodb:TagResource", "dynamodb:UntagResource", "dynamodb:ListTagsOfResource"], "Effect": "Allow", "Resource": {"Fn::Sub": ["arn:${AWS::Partition}:dynamodb:${AWS::Region}:${AWS::AccountId}:table/*-${apiId}-${envName}", {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "envName": "NONE"}]}}, {"Action": "lambda:ListTags", "Effect": "Allow", "Resource": {"Fn::Sub": ["arn:${AWS::Partition}:lambda:${AWS::Region}:${AWS::AccountId}:function:*TableManager*", {}]}}], "Version": "2012-10-17"}}], "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.189.1", "metadata": []}}, "ImmutableRoleAmplifyManagedTableIsCompleteRole": {"id": "ImmutableRoleAmplifyManagedTableIsCompleteRole", "path": "amplify-RootsConnect-n<PERSON><PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/ImmutableRoleAmplifyManagedTableIsCompleteRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "AmplifyManagedTableOnEventRole": {"id": "AmplifyManagedTableOnEventRole", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyManagedTableOnEventRole", "children": {"ImportAmplifyManagedTableOnEventRole": {"id": "ImportAmplifyManagedTableOnEventRole", "path": "amplify-RootsConnect-<PERSON><PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyManagedTableOnEventRole/ImportAmplifyManagedTableOnEventRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyManagedTableOnEventRole/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}], "policies": [{"policyName": "CreateUpdateDeleteTablesPolicy", "policyDocument": {"Statement": [{"Action": ["dynamodb:CreateTable", "dynamodb:UpdateTable", "dynamodb:DeleteTable", "dynamodb:DescribeTable", "dynamodb:DescribeContinuousBackups", "dynamodb:DescribeTimeToLive", "dynamodb:UpdateContinuousBackups", "dynamodb:UpdateTimeToLive", "dynamodb:TagResource", "dynamodb:UntagResource", "dynamodb:ListTagsOfResource"], "Effect": "Allow", "Resource": {"Fn::Sub": ["arn:${AWS::Partition}:dynamodb:${AWS::Region}:${AWS::AccountId}:table/*-${apiId}-${envName}", {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "envName": "NONE"}]}}, {"Action": "lambda:ListTags", "Effect": "Allow", "Resource": {"Fn::Sub": ["arn:${AWS::Partition}:lambda:${AWS::Region}:${AWS::AccountId}:function:*TableManager*", {}]}}], "Version": "2012-10-17"}}], "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.189.1"}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyManagedTableOnEventRole/DefaultPolicy", "children": {"Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyManagedTableOnEventRole/DefaultPolicy/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": "states:StartExecution", "Effect": "Allow", "Resource": {"Ref": "AmplifyTableWaiterStateMachine060600BC"}}], "Version": "2012-10-17"}, "policyName": "AmplifyManagedTableOnEventRoleDefaultPolicyF6DABCB6", "roles": [{"Ref": "AmplifyManagedTableOnEventRoleB4E71DEA"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.189.1", "metadata": []}}, "TableManagerCustomProvider": {"id": "TableManagerCustomProvider", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider", "children": {"framework-onEvent": {"id": "framework-onEvent", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider/framework-onEvent", "children": {"Code": {"id": "Code", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider/framework-onEvent/Code", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider/framework-onEvent/Code/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider/framework-onEvent/Code/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider/framework-onEvent/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Function", "aws:cdk:cloudformation:props": {"code": {"s3Bucket": {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "s3Key": "23df4a10f7743e90a710888ea87205f23d214aad2b34e617221bec49c1d24ebc.zip"}, "description": "AmplifyManagedTable - onEvent (amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider)", "environment": {"variables": {"WAITER_STATE_MACHINE_ARN": {"Ref": "AmplifyTableWaiterStateMachine060600BC"}}}, "handler": "amplify-table-manager-handler.onEvent", "role": {"Fn::GetAtt": ["AmplifyManagedTableOnEventRoleB4E71DEA", "<PERSON><PERSON>"]}, "runtime": "nodejs20.x", "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}], "timeout": 840}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnFunction", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.Function", "version": "2.189.1", "metadata": []}}, "framework-isComplete": {"id": "framework-isComplete", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider/framework-isComplete", "children": {"Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider/framework-isComplete/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Function", "aws:cdk:cloudformation:props": {"code": {"s3Bucket": {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "s3Key": "23df4a10f7743e90a710888ea87205f23d214aad2b34e617221bec49c1d24ebc.zip"}, "description": "AmplifyManagedTable - isComplete (amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider)", "handler": "amplify-table-manager-handler.isComplete", "role": {"Fn::GetAtt": ["AmplifyManagedTableIsCompleteRoleF825222C", "<PERSON><PERSON>"]}, "runtime": "nodejs20.x", "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}], "timeout": 840}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnFunction", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.Function", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "AmplifyTableWaiterStateMachine": {"id": "AmplifyTableWaiterStateMachine", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine", "children": {"Role": {"id": "Role", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine/Role", "children": {"ImportRole": {"id": "ImportRole", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine/Role/ImportRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "Resource": {"id": "Resource", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine/Role/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "states.amazonaws.com"}}], "Version": "2012-10-17"}, "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.189.1"}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine/Role/DefaultPolicy", "children": {"Resource": {"id": "Resource", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine/Role/DefaultPolicy/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": "lambda:InvokeFunction", "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["TableManagerCustomProviderframeworkisComplete2E51021B", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["TableManagerCustomProviderframeworkisComplete2E51021B", "<PERSON><PERSON>"]}, ":*"]]}]}], "Version": "2012-10-17"}, "policyName": "AmplifyTableWaiterStateMachineRoleDefaultPolicy89F3836A", "roles": [{"Ref": "AmplifyTableWaiterStateMachineRole470BE899"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.189.1", "metadata": []}}, "Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine/Resource", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "CDKMetadata": {"id": "CDKMetadata", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/CDKMetadata", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}, "Condition": {"id": "Condition", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/CDKMetadata/Condition", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn": {"id": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.NestedStack", "version": "2.189.1"}}, "AmplifyTableManager.NestedStack": {"id": "AmplifyTableManager.NestedStack", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager.NestedStack", "children": {"AmplifyTableManager.NestedStackResource": {"id": "AmplifyTableManager.NestedStackResource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager.NestedStack/AmplifyTableManager.NestedStackResource", "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFormation::Stack", "aws:cdk:cloudformation:props": {"parameters": {"referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}}, "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}], "templateUrl": {"Fn::Join": ["", ["https://s3.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/dd481eb31c5caa20140d9d422348dbdab98d913903c1e918eafdb4531b770f5b.json"]]}}}, "constructInfo": {"fqn": "aws-cdk-lib.CfnStack", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "UserProfile": {"id": "UserProfile", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile", "children": {"DynamoDBModelTableReadIOPS": {"id": "DynamoDBModelTableReadIOPS", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/DynamoDBModelTableReadIOPS", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBModelTableWriteIOPS": {"id": "DynamoDBModelTableWriteIOPS", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/DynamoDBModelTableWriteIOPS", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBBillingMode": {"id": "DynamoDBBillingMode", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/DynamoDBBillingMode", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBEnablePointInTimeRecovery": {"id": "DynamoDBEnablePointInTimeRecovery", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/DynamoDBEnablePointInTimeRecovery", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBEnableServerSideEncryption": {"id": "DynamoDBEnableServerSideEncryption", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/DynamoDBEnableServerSideEncryption", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "HasEnvironmentParameter": {"id": "HasEnvironmentParameter", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/HasEnvironmentParameter", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}, "ShouldUsePayPerRequestBilling": {"id": "ShouldUsePayPerRequestBilling", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/ShouldUsePayPerRequestBilling", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}, "ShouldUsePointInTimeRecovery": {"id": "ShouldUsePointInTimeRecovery", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/ShouldUsePointInTimeRecovery", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}, "UserProfileTable": {"id": "UserProfileTable", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileTable", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileTable/Default", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileTable/Default/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.CustomResource", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "CustomTableUserProfileTable": {"id": "CustomTableUserProfileTable", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/CustomTableUserProfileTable", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.TableBase", "version": "2.189.1", "metadata": []}}, "GetAttUserProfileTableStreamArn": {"id": "GetAttUserProfileTableStreamArn", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/GetAttUserProfileTableStreamArn", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "GetAttUserProfileTableName": {"id": "GetAttUserProfileTableName", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/GetAttUserProfileTableName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "UserProfileIAMRole": {"id": "UserProfileIAMRole", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileIAMRole", "children": {"ImportUserProfileIAMRole": {"id": "ImportUserProfileIAMRole", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileIAMRole/ImportUserProfileIAMRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileIAMRole/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "appsync.amazonaws.com"}}], "Version": "2012-10-17"}, "policies": [{"policyName": "DynamoDBAccess", "policyDocument": {"Statement": [{"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:PutItem", "dynamodb:DeleteItem", "dynamodb:GetItem", "dynamodb:<PERSON><PERSON>", "dynamodb:Query", "dynamodb:UpdateItem", "dynamodb:ConditionCheckItem", "dynamodb:DescribeTable", "dynamodb:GetRecords", "dynamodb:GetShardIterator"], "Effect": "Allow", "Resource": [{"Fn::Sub": ["arn:${AWS::Partition}:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${tablename}", {"tablename": {"Fn::Join": ["", ["UserProfile-", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "-NONE"]]}}]}, {"Fn::Sub": ["arn:${AWS::Partition}:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${tablename}/*", {"tablename": {"Fn::Join": ["", ["UserProfile-", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "-NONE"]]}}]}]}], "Version": "2012-10-17"}}], "roleName": {"Fn::Join": ["", ["UserProfileIAMRole52f158-", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "-NONE"]]}, "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.189.1", "metadata": []}}, "ImmutableRoleUserProfileIAMRole": {"id": "ImmutableRoleUserProfileIAMRole", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/ImmutableRoleUserProfileIAMRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "UserProfileDataSource": {"id": "UserProfileDataSource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileDataSource", "children": {"Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileDataSource/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::DataSource", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dynamoDbConfig": {"tableName": {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["UserProfileTable", "TableArn"]}]}]}]}]}, "awsRegion": {"Ref": "AWS::Region"}}, "name": "UserProfileTable", "serviceRoleArn": {"Fn::GetAtt": ["UserProfileIAMRoleA4E2934B", "<PERSON><PERSON>"]}, "type": "AMAZON_DYNAMODB"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnDataSource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.DynamoDbDataSource", "version": "2.189.1"}}, "QuerygetUserProfileauth0Function": {"id": "QuerygetUserProfileauth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerygetUserProfileauth0Function", "children": {"Templateresolvers--Query.getUserProfile.auth.1.req.vtl": {"id": "Templateresolvers--Query.getUserProfile.auth.1.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerygetUserProfileauth0Function/Templateresolvers--Query.getUserProfile.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerygetUserProfileauth0Function/Templateresolvers--Query.getUserProfile.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerygetUserProfileauth0Function/Templateresolvers--Query.getUserProfile.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QuerygetUserProfileauth0Function.AppSyncFunction": {"id": "QuerygetUserProfileauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerygetUserProfileauth0Function/QuerygetUserProfileauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "QuerygetUserProfileauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/6f1b01c4b2882f213b91241f489e2393bc199b215b8b1695ea62da2156719905.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "QuerygetUserProfilepostAuth0Function": {"id": "QuerygetUserProfilepostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerygetUserProfilepostAuth0Function", "children": {"Templateresolvers--Query.getUserProfile.postAuth.1.req.vtl": {"id": "Templateresolvers--Query.getUserProfile.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerygetUserProfilepostAuth0Function/Templateresolvers--Query.getUserProfile.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerygetUserProfilepostAuth0Function/Templateresolvers--Query.getUserProfile.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerygetUserProfilepostAuth0Function/Templateresolvers--Query.getUserProfile.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QuerygetUserProfilepostAuth0Function.AppSyncFunction": {"id": "QuerygetUserProfilepostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerygetUserProfilepostAuth0Function/QuerygetUserProfilepostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "QuerygetUserProfilepostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "QueryGetUserProfileDataResolverFn": {"id": "QueryGetUserProfileDataResolverFn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QueryGetUserProfileDataResolverFn", "children": {"Templateresolvers--Query.getUserProfile.req.vtl": {"id": "Templateresolvers--Query.getUserProfile.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QueryGetUserProfileDataResolverFn/Templateresolvers--Query.getUserProfile.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QueryGetUserProfileDataResolverFn/Templateresolvers--Query.getUserProfile.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QueryGetUserProfileDataResolverFn/Templateresolvers--Query.getUserProfile.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Query.getUserProfile.res.vtl": {"id": "Templateresolvers--Query.getUserProfile.res.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QueryGetUserProfileDataResolverFn/Templateresolvers--Query.getUserProfile.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QueryGetUserProfileDataResolverFn/Templateresolvers--Query.getUserProfile.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QueryGetUserProfileDataResolverFn/Templateresolvers--Query.getUserProfile.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QueryGetUserProfileDataResolverFn.AppSyncFunction": {"id": "QueryGetUserProfileDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QueryGetUserProfileDataResolverFn/QueryGetUserProfileDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Fn::GetAtt": ["UserProfileDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "QueryGetUserProfileDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/08f4d557693d96c1a4efba0f9dc91330e4b19772fd5477c156468843e3d9cb5e.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/4c6a2d29f01c6091bd1d9afe16e5849d456c96f17c3b215938c8067399532719.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "queryGetUserProfileResolver": {"id": "queryGetUserProfileResolver", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/queryGetUserProfileResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "getUserProfile", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["QuerygetUserProfileauth0FunctionQuerygetUserProfileauth0FunctionAppSyncFunctionDAD22839", "FunctionId"]}, {"Fn::GetAtt": ["QuerygetUserProfilepostAuth0FunctionQuerygetUserProfilepostAuth0FunctionAppSyncFunction92890504", "FunctionId"]}, {"Fn::GetAtt": ["QueryGetUserProfileDataResolverFnQueryGetUserProfileDataResolverFnAppSyncFunction8EB8BD57", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Query\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"getUserProfile\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["UserProfileTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Query"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "QuerylistUserProfilesauth0Function": {"id": "QuerylistUserProfilesauth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerylistUserProfilesauth0Function", "children": {"Templateresolvers--Query.listUserProfiles.auth.1.req.vtl": {"id": "Templateresolvers--Query.listUserProfiles.auth.1.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerylistUserProfilesauth0Function/Templateresolvers--Query.listUserProfiles.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerylistUserProfilesauth0Function/Templateresolvers--Query.listUserProfiles.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerylistUserProfilesauth0Function/Templateresolvers--Query.listUserProfiles.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QuerylistUserProfilesauth0Function.AppSyncFunction": {"id": "QuerylistUserProfilesauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerylistUserProfilesauth0Function/QuerylistUserProfilesauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "QuerylistUserProfilesauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/6f1b01c4b2882f213b91241f489e2393bc199b215b8b1695ea62da2156719905.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "QuerylistUserProfilespostAuth0Function": {"id": "QuerylistUserProfilespostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerylistUserProfilespostAuth0Function", "children": {"Templateresolvers--Query.listUserProfiles.postAuth.1.req.vtl": {"id": "Templateresolvers--Query.listUserProfiles.postAuth.1.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerylistUserProfilespostAuth0Function/Templateresolvers--Query.listUserProfiles.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerylistUserProfilespostAuth0Function/Templateresolvers--Query.listUserProfiles.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerylistUserProfilespostAuth0Function/Templateresolvers--Query.listUserProfiles.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QuerylistUserProfilespostAuth0Function.AppSyncFunction": {"id": "QuerylistUserProfilespostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QuerylistUserProfilespostAuth0Function/QuerylistUserProfilespostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "QuerylistUserProfilespostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "QueryListUserProfilesDataResolverFn": {"id": "QueryListUserProfilesDataResolverFn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QueryListUserProfilesDataResolverFn", "children": {"Templateresolvers--Query.listUserProfiles.req.vtl": {"id": "Templateresolvers--Query.listUserProfiles.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QueryListUserProfilesDataResolverFn/Templateresolvers--Query.listUserProfiles.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QueryListUserProfilesDataResolverFn/Templateresolvers--Query.listUserProfiles.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QueryListUserProfilesDataResolverFn/Templateresolvers--Query.listUserProfiles.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Query.listUserProfiles.res.vtl": {"id": "Templateresolvers--Query.listUserProfiles.res.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QueryListUserProfilesDataResolverFn/Templateresolvers--Query.listUserProfiles.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QueryListUserProfilesDataResolverFn/Templateresolvers--Query.listUserProfiles.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QueryListUserProfilesDataResolverFn/Templateresolvers--Query.listUserProfiles.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QueryListUserProfilesDataResolverFn.AppSyncFunction": {"id": "QueryListUserProfilesDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/QueryListUserProfilesDataResolverFn/QueryListUserProfilesDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Fn::GetAtt": ["UserProfileDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "QueryListUserProfilesDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/9fcbe070ecd3023c5bf5b966fa9584757db9762eef123bad0820bd87591b2174.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/cc01911d0269d4080ea57505dc445dfc315ef7ad85d3d9d4ea1357858bff451d.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "queryListUserProfilesResolver": {"id": "queryListUserProfilesResolver", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/queryListUserProfilesResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "listUserProfiles", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["QuerylistUserProfilesauth0FunctionQuerylistUserProfilesauth0FunctionAppSyncFunction468B41DC", "FunctionId"]}, {"Fn::GetAtt": ["QuerylistUserProfilespostAuth0FunctionQuerylistUserProfilespostAuth0FunctionAppSyncFunction01122A7A", "FunctionId"]}, {"Fn::GetAtt": ["QueryListUserProfilesDataResolverFnQueryListUserProfilesDataResolverFnAppSyncFunctionA05E8B7A", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Query\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"listUserProfiles\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["UserProfileTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Query"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "MutationcreateUserProfileinit0Function": {"id": "MutationcreateUserProfileinit0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfileinit0Function", "children": {"Templateresolvers--Mutation.createUserProfile.init.1.req.vtl": {"id": "Templateresolvers--Mutation.createUserProfile.init.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfileinit0Function/Templateresolvers--Mutation.createUserProfile.init.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfileinit0Function/Templateresolvers--Mutation.createUserProfile.init.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfileinit0Function/Templateresolvers--Mutation.createUserProfile.init.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationcreateUserProfileinit0Function.AppSyncFunction": {"id": "MutationcreateUserProfileinit0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfileinit0Function/MutationcreateUserProfileinit0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "MutationcreateUserProfileinit0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/a183ddccbd956316c38ef97177b8f088ef0826f62023323f5ae6053d348ccffc.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationcreateUserProfileinit1Function": {"id": "MutationcreateUserProfileinit1Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfileinit1Function", "children": {"Templateresolvers--Mutation.createUserProfile.init.2.req.vtl": {"id": "Templateresolvers--Mutation.createUserProfile.init.2.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfileinit1Function/Templateresolvers--Mutation.createUserProfile.init.2.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfileinit1Function/Templateresolvers--Mutation.createUserProfile.init.2.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfileinit1Function/Templateresolvers--Mutation.createUserProfile.init.2.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationcreateUserProfileinit1Function.AppSyncFunction": {"id": "MutationcreateUserProfileinit1Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfileinit1Function/MutationcreateUserProfileinit1Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "MutationcreateUserProfileinit1Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/0fd7df0cf2df9d019fe04dcacd94e8367f5308ad7b02bdbd92d91ed5ac30a6a0.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationcreateUserProfileauth0Function": {"id": "MutationcreateUserProfileauth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfileauth0Function", "children": {"Templateresolvers--Mutation.createUserProfile.auth.1.req.vtl": {"id": "Templateresolvers--Mutation.createUserProfile.auth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfileauth0Function/Templateresolvers--Mutation.createUserProfile.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfileauth0Function/Templateresolvers--Mutation.createUserProfile.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfileauth0Function/Templateresolvers--Mutation.createUserProfile.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationcreateUserProfileauth0Function.AppSyncFunction": {"id": "MutationcreateUserProfileauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfileauth0Function/MutationcreateUserProfileauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "MutationcreateUserProfileauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/9e2e2c2ea03e23aab3194d48982c3069731b170aca59702ffdc812cd2aed7521.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationcreateUserProfilepostAuth0Function": {"id": "MutationcreateUserProfilepostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfilepostAuth0Function", "children": {"Templateresolvers--Mutation.createUserProfile.postAuth.1.req.vtl": {"id": "Templateresolvers--Mutation.createUserProfile.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfilepostAuth0Function/Templateresolvers--Mutation.createUserProfile.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfilepostAuth0Function/Templateresolvers--Mutation.createUserProfile.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfilepostAuth0Function/Templateresolvers--Mutation.createUserProfile.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationcreateUserProfilepostAuth0Function.AppSyncFunction": {"id": "MutationcreateUserProfilepostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationcreateUserProfilepostAuth0Function/MutationcreateUserProfilepostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "MutationcreateUserProfilepostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationCreateUserProfileDataResolverFn": {"id": "MutationCreateUserProfileDataResolverFn", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationCreateUserProfileDataResolverFn", "children": {"Templateresolvers--Mutation.createUserProfile.req.vtl": {"id": "Templateresolvers--Mutation.createUserProfile.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationCreateUserProfileDataResolverFn/Templateresolvers--Mutation.createUserProfile.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationCreateUserProfileDataResolverFn/Templateresolvers--Mutation.createUserProfile.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationCreateUserProfileDataResolverFn/Templateresolvers--Mutation.createUserProfile.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Mutation.createUserProfile.res.vtl": {"id": "Templateresolvers--Mutation.createUserProfile.res.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationCreateUserProfileDataResolverFn/Templateresolvers--Mutation.createUserProfile.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationCreateUserProfileDataResolverFn/Templateresolvers--Mutation.createUserProfile.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationCreateUserProfileDataResolverFn/Templateresolvers--Mutation.createUserProfile.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationCreateUserProfileDataResolverFn.AppSyncFunction": {"id": "MutationCreateUserProfileDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationCreateUserProfileDataResolverFn/MutationCreateUserProfileDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Fn::GetAtt": ["UserProfileDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "MutationCreateUserProfileDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/5591194755d6c82901ab61fc77e043768b6b515f63c3a032cf0b8d285671c1ea.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a52b72209a9dfa197b5e7367a5c378c5bb86de6e29ddd9e48b49a3fe54b249.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "mutationCreateUserProfileResolver": {"id": "mutationCreateUserProfileResolver", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/mutationCreateUserProfileResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "createUserProfile", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["MutationcreateUserProfileinit0FunctionMutationcreateUserProfileinit0FunctionAppSyncFunction7EFE9ED4", "FunctionId"]}, {"Fn::GetAtt": ["MutationcreateUserProfileinit1FunctionMutationcreateUserProfileinit1FunctionAppSyncFunction9B1EF9A1", "FunctionId"]}, {"Fn::GetAtt": ["MutationcreateUserProfileauth0FunctionMutationcreateUserProfileauth0FunctionAppSyncFunctionCF3841B8", "FunctionId"]}, {"Fn::GetAtt": ["MutationcreateUserProfilepostAuth0FunctionMutationcreateUserProfilepostAuth0FunctionAppSyncFunctionF0C8FEFF", "FunctionId"]}, {"Fn::GetAtt": ["MutationCreateUserProfileDataResolverFnMutationCreateUserProfileDataResolverFnAppSyncFunction120FE646", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Mutation\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"createUserProfile\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["UserProfileTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Mutation"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "MutationupdateUserProfileinit0Function": {"id": "MutationupdateUserProfileinit0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationupdateUserProfileinit0Function", "children": {"Templateresolvers--Mutation.updateUserProfile.init.1.req.vtl": {"id": "Templateresolvers--Mutation.updateUserProfile.init.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationupdateUserProfileinit0Function/Templateresolvers--Mutation.updateUserProfile.init.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationupdateUserProfileinit0Function/Templateresolvers--Mutation.updateUserProfile.init.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationupdateUserProfileinit0Function/Templateresolvers--Mutation.updateUserProfile.init.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationupdateUserProfileinit0Function.AppSyncFunction": {"id": "MutationupdateUserProfileinit0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationupdateUserProfileinit0Function/MutationupdateUserProfileinit0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "MutationupdateUserProfileinit0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/06db846fd14e6fc371f22b12b5545ba8e2dbfeda85d8c8d586c71c282166657b.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationupdateUserProfileauth0Function": {"id": "MutationupdateUserProfileauth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationupdateUserProfileauth0Function", "children": {"Templateresolvers--Mutation.updateUserProfile.auth.1.req.vtl": {"id": "Templateresolvers--Mutation.updateUserProfile.auth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationupdateUserProfileauth0Function/Templateresolvers--Mutation.updateUserProfile.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationupdateUserProfileauth0Function/Templateresolvers--Mutation.updateUserProfile.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationupdateUserProfileauth0Function/Templateresolvers--Mutation.updateUserProfile.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Mutation.updateUserProfile.auth.1.res.vtl": {"id": "Templateresolvers--Mutation.updateUserProfile.auth.1.res.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationupdateUserProfileauth0Function/Templateresolvers--Mutation.updateUserProfile.auth.1.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationupdateUserProfileauth0Function/Templateresolvers--Mutation.updateUserProfile.auth.1.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationupdateUserProfileauth0Function/Templateresolvers--Mutation.updateUserProfile.auth.1.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationupdateUserProfileauth0Function.AppSyncFunction": {"id": "MutationupdateUserProfileauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationupdateUserProfileauth0Function/MutationupdateUserProfileauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Fn::GetAtt": ["UserProfileDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "MutationupdateUserProfileauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/1f5fed297da9c32ae3af922bf3a38ccf23b956078887d16891ec06c20c64722c.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/80680c06297d8e4f4943fe8215d637144ea24bf53ec05404e841459b81fc3063.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationupdateUserProfilepostAuth0Function": {"id": "MutationupdateUserProfilepostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationupdateUserProfilepostAuth0Function", "children": {"Templateresolvers--Mutation.updateUserProfile.postAuth.1.req.vtl": {"id": "Templateresolvers--Mutation.updateUserProfile.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationupdateUserProfilepostAuth0Function/Templateresolvers--Mutation.updateUserProfile.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationupdateUserProfilepostAuth0Function/Templateresolvers--Mutation.updateUserProfile.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationupdateUserProfilepostAuth0Function/Templateresolvers--Mutation.updateUserProfile.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationupdateUserProfilepostAuth0Function.AppSyncFunction": {"id": "MutationupdateUserProfilepostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationupdateUserProfilepostAuth0Function/MutationupdateUserProfilepostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "MutationupdateUserProfilepostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationUpdateUserProfileDataResolverFn": {"id": "MutationUpdateUserProfileDataResolverFn", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationUpdateUserProfileDataResolverFn", "children": {"Templateresolvers--Mutation.updateUserProfile.req.vtl": {"id": "Templateresolvers--Mutation.updateUserProfile.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationUpdateUserProfileDataResolverFn/Templateresolvers--Mutation.updateUserProfile.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationUpdateUserProfileDataResolverFn/Templateresolvers--Mutation.updateUserProfile.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationUpdateUserProfileDataResolverFn/Templateresolvers--Mutation.updateUserProfile.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Mutation.updateUserProfile.res.vtl": {"id": "Templateresolvers--Mutation.updateUserProfile.res.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationUpdateUserProfileDataResolverFn/Templateresolvers--Mutation.updateUserProfile.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationUpdateUserProfileDataResolverFn/Templateresolvers--Mutation.updateUserProfile.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationUpdateUserProfileDataResolverFn/Templateresolvers--Mutation.updateUserProfile.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationUpdateUserProfileDataResolverFn.AppSyncFunction": {"id": "MutationUpdateUserProfileDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationUpdateUserProfileDataResolverFn/MutationUpdateUserProfileDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Fn::GetAtt": ["UserProfileDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "MutationUpdateUserProfileDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/474bf0776ec2164a13191d1a0a9e057154931e4918fea5086f49850d02a5371b.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a52b72209a9dfa197b5e7367a5c378c5bb86de6e29ddd9e48b49a3fe54b249.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "mutationUpdateUserProfileResolver": {"id": "mutationUpdateUserProfileResolver", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/mutationUpdateUserProfileResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "updateUserProfile", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["MutationupdateUserProfileinit0FunctionMutationupdateUserProfileinit0FunctionAppSyncFunction9A840BAA", "FunctionId"]}, {"Fn::GetAtt": ["MutationupdateUserProfileauth0FunctionMutationupdateUserProfileauth0FunctionAppSyncFunction7E8F945B", "FunctionId"]}, {"Fn::GetAtt": ["MutationupdateUserProfilepostAuth0FunctionMutationupdateUserProfilepostAuth0FunctionAppSyncFunction206AFA76", "FunctionId"]}, {"Fn::GetAtt": ["MutationUpdateUserProfileDataResolverFnMutationUpdateUserProfileDataResolverFnAppSyncFunction1707D92F", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Mutation\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"updateUserProfile\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["UserProfileTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Mutation"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "MutationdeleteUserProfileauth0Function": {"id": "MutationdeleteUserProfileauth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationdeleteUserProfileauth0Function", "children": {"Templateresolvers--Mutation.deleteUserProfile.auth.1.req.vtl": {"id": "Templateresolvers--Mutation.deleteUserProfile.auth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationdeleteUserProfileauth0Function/Templateresolvers--Mutation.deleteUserProfile.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationdeleteUserProfileauth0Function/Templateresolvers--Mutation.deleteUserProfile.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationdeleteUserProfileauth0Function/Templateresolvers--Mutation.deleteUserProfile.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Mutation.deleteUserProfile.auth.1.res.vtl": {"id": "Templateresolvers--Mutation.deleteUserProfile.auth.1.res.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationdeleteUserProfileauth0Function/Templateresolvers--Mutation.deleteUserProfile.auth.1.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationdeleteUserProfileauth0Function/Templateresolvers--Mutation.deleteUserProfile.auth.1.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationdeleteUserProfileauth0Function/Templateresolvers--Mutation.deleteUserProfile.auth.1.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationdeleteUserProfileauth0Function.AppSyncFunction": {"id": "MutationdeleteUserProfileauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationdeleteUserProfileauth0Function/MutationdeleteUserProfileauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Fn::GetAtt": ["UserProfileDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "MutationdeleteUserProfileauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/1f5fed297da9c32ae3af922bf3a38ccf23b956078887d16891ec06c20c64722c.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/6257bfd1ef2992bd01df135516c0df15c5ff692f426e0c71c93960be8f8c81df.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationdeleteUserProfilepostAuth0Function": {"id": "MutationdeleteUserProfilepostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationdeleteUserProfilepostAuth0Function", "children": {"Templateresolvers--Mutation.deleteUserProfile.postAuth.1.req.vtl": {"id": "Templateresolvers--Mutation.deleteUserProfile.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationdeleteUserProfilepostAuth0Function/Templateresolvers--Mutation.deleteUserProfile.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationdeleteUserProfilepostAuth0Function/Templateresolvers--Mutation.deleteUserProfile.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationdeleteUserProfilepostAuth0Function/Templateresolvers--Mutation.deleteUserProfile.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationdeleteUserProfilepostAuth0Function.AppSyncFunction": {"id": "MutationdeleteUserProfilepostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationdeleteUserProfilepostAuth0Function/MutationdeleteUserProfilepostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "MutationdeleteUserProfilepostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationDeleteUserProfileDataResolverFn": {"id": "MutationDeleteUserProfileDataResolverFn", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationDeleteUserProfileDataResolverFn", "children": {"Templateresolvers--Mutation.deleteUserProfile.req.vtl": {"id": "Templateresolvers--Mutation.deleteUserProfile.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationDeleteUserProfileDataResolverFn/Templateresolvers--Mutation.deleteUserProfile.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationDeleteUserProfileDataResolverFn/Templateresolvers--Mutation.deleteUserProfile.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationDeleteUserProfileDataResolverFn/Templateresolvers--Mutation.deleteUserProfile.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Mutation.deleteUserProfile.res.vtl": {"id": "Templateresolvers--Mutation.deleteUserProfile.res.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationDeleteUserProfileDataResolverFn/Templateresolvers--Mutation.deleteUserProfile.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationDeleteUserProfileDataResolverFn/Templateresolvers--Mutation.deleteUserProfile.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationDeleteUserProfileDataResolverFn/Templateresolvers--Mutation.deleteUserProfile.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationDeleteUserProfileDataResolverFn.AppSyncFunction": {"id": "MutationDeleteUserProfileDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/MutationDeleteUserProfileDataResolverFn/MutationDeleteUserProfileDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Fn::GetAtt": ["UserProfileDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "MutationDeleteUserProfileDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/4f7907d1209a2c9953a0c053df402c634e359546d70c7cc5c2e8e21ea734880f.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a52b72209a9dfa197b5e7367a5c378c5bb86de6e29ddd9e48b49a3fe54b249.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "mutationDeleteUserProfileResolver": {"id": "mutationDeleteUserProfileResolver", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/mutationDeleteUserProfileResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "deleteUserProfile", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["MutationdeleteUserProfileauth0FunctionMutationdeleteUserProfileauth0FunctionAppSyncFunction4A3AC4FD", "FunctionId"]}, {"Fn::GetAtt": ["MutationdeleteUserProfilepostAuth0FunctionMutationdeleteUserProfilepostAuth0FunctionAppSyncFunctionEE3F21F4", "FunctionId"]}, {"Fn::GetAtt": ["MutationDeleteUserProfileDataResolverFnMutationDeleteUserProfileDataResolverFnAppSyncFunction4835094F", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Mutation\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"deleteUserProfile\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["UserProfileTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Mutation"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "SubscriptiononCreateUserProfileauth0Function": {"id": "SubscriptiononCreateUserProfileauth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononCreateUserProfileauth0Function", "children": {"Templateresolvers--Subscription.onCreateUserProfile.auth.1.req.vtl": {"id": "Templateresolvers--Subscription.onCreateUserProfile.auth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononCreateUserProfileauth0Function/Templateresolvers--Subscription.onCreateUserProfile.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononCreateUserProfileauth0Function/Templateresolvers--Subscription.onCreateUserProfile.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononCreateUserProfileauth0Function/Templateresolvers--Subscription.onCreateUserProfile.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononCreateUserProfileauth0Function.AppSyncFunction": {"id": "SubscriptiononCreateUserProfileauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononCreateUserProfileauth0Function/SubscriptiononCreateUserProfileauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptiononCreateUserProfileauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a6f34d8595215fbaec56ec853de13cd014504c4abe3ff7457af53316ba2779.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptiononCreateUserProfilepostAuth0Function": {"id": "SubscriptiononCreateUserProfilepostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononCreateUserProfilepostAuth0Function", "children": {"Templateresolvers--Subscription.onCreateUserProfile.postAuth.1.req.vtl": {"id": "Templateresolvers--Subscription.onCreateUserProfile.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononCreateUserProfilepostAuth0Function/Templateresolvers--Subscription.onCreateUserProfile.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononCreateUserProfilepostAuth0Function/Templateresolvers--Subscription.onCreateUserProfile.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononCreateUserProfilepostAuth0Function/Templateresolvers--Subscription.onCreateUserProfile.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononCreateUserProfilepostAuth0Function.AppSyncFunction": {"id": "SubscriptiononCreateUserProfilepostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononCreateUserProfilepostAuth0Function/SubscriptiononCreateUserProfilepostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptiononCreateUserProfilepostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptionOnCreateUserProfileDataResolverFn": {"id": "SubscriptionOnCreateUserProfileDataResolverFn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnCreateUserProfileDataResolverFn", "children": {"Templateresolvers--Subscription.onCreateUserProfile.req.vtl": {"id": "Templateresolvers--Subscription.onCreateUserProfile.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnCreateUserProfileDataResolverFn/Templateresolvers--Subscription.onCreateUserProfile.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnCreateUserProfileDataResolverFn/Templateresolvers--Subscription.onCreateUserProfile.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnCreateUserProfileDataResolverFn/Templateresolvers--Subscription.onCreateUserProfile.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Subscription.onCreateUserProfile.res.vtl": {"id": "Templateresolvers--Subscription.onCreateUserProfile.res.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnCreateUserProfileDataResolverFn/Templateresolvers--Subscription.onCreateUserProfile.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnCreateUserProfileDataResolverFn/Templateresolvers--Subscription.onCreateUserProfile.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnCreateUserProfileDataResolverFn/Templateresolvers--Subscription.onCreateUserProfile.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptionOnCreateUserProfileDataResolverFn.AppSyncFunction": {"id": "SubscriptionOnCreateUserProfileDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnCreateUserProfileDataResolverFn/SubscriptionOnCreateUserProfileDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptionOnCreateUserProfileDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/fe3c43ada4b9d681a5e2312663ef7a73386424d73b73e51f8e2e9d4b50f7c502.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/e0cff47fb007f0bbf2a4e43ca256d6aa7ec109821769fd79fa7c5e83f0e7f9fc.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "subscriptionOnCreateUserProfileResolver": {"id": "subscriptionOnCreateUserProfileResolver", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/subscriptionOnCreateUserProfileResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "onCreateUserProfile", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["SubscriptiononCreateUserProfileauth0FunctionSubscriptiononCreateUserProfileauth0FunctionAppSyncFunctionD90BBD82", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptiononCreateUserProfilepostAuth0FunctionSubscriptiononCreateUserProfilepostAuth0FunctionAppSyncFunctionD659A080", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptionOnCreateUserProfileDataResolverFnSubscriptionOnCreateUserProfileDataResolverFnAppSyncFunction08AFEB08", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Subscription\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"onCreateUserProfile\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"NONE\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Subscription"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "SubscriptiononUpdateUserProfileauth0Function": {"id": "SubscriptiononUpdateUserProfileauth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononUpdateUserProfileauth0Function", "children": {"Templateresolvers--Subscription.onUpdateUserProfile.auth.1.req.vtl": {"id": "Templateresolvers--Subscription.onUpdateUserProfile.auth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononUpdateUserProfileauth0Function/Templateresolvers--Subscription.onUpdateUserProfile.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononUpdateUserProfileauth0Function/Templateresolvers--Subscription.onUpdateUserProfile.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononUpdateUserProfileauth0Function/Templateresolvers--Subscription.onUpdateUserProfile.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononUpdateUserProfileauth0Function.AppSyncFunction": {"id": "SubscriptiononUpdateUserProfileauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononUpdateUserProfileauth0Function/SubscriptiononUpdateUserProfileauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptiononUpdateUserProfileauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a6f34d8595215fbaec56ec853de13cd014504c4abe3ff7457af53316ba2779.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptiononUpdateUserProfilepostAuth0Function": {"id": "SubscriptiononUpdateUserProfilepostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononUpdateUserProfilepostAuth0Function", "children": {"Templateresolvers--Subscription.onUpdateUserProfile.postAuth.1.req.vtl": {"id": "Templateresolvers--Subscription.onUpdateUserProfile.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononUpdateUserProfilepostAuth0Function/Templateresolvers--Subscription.onUpdateUserProfile.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononUpdateUserProfilepostAuth0Function/Templateresolvers--Subscription.onUpdateUserProfile.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononUpdateUserProfilepostAuth0Function/Templateresolvers--Subscription.onUpdateUserProfile.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononUpdateUserProfilepostAuth0Function.AppSyncFunction": {"id": "SubscriptiononUpdateUserProfilepostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononUpdateUserProfilepostAuth0Function/SubscriptiononUpdateUserProfilepostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptiononUpdateUserProfilepostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptionOnUpdateUserProfileDataResolverFn": {"id": "SubscriptionOnUpdateUserProfileDataResolverFn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnUpdateUserProfileDataResolverFn", "children": {"Templateresolvers--Subscription.onUpdateUserProfile.req.vtl": {"id": "Templateresolvers--Subscription.onUpdateUserProfile.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnUpdateUserProfileDataResolverFn/Templateresolvers--Subscription.onUpdateUserProfile.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnUpdateUserProfileDataResolverFn/Templateresolvers--Subscription.onUpdateUserProfile.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnUpdateUserProfileDataResolverFn/Templateresolvers--Subscription.onUpdateUserProfile.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Subscription.onUpdateUserProfile.res.vtl": {"id": "Templateresolvers--Subscription.onUpdateUserProfile.res.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnUpdateUserProfileDataResolverFn/Templateresolvers--Subscription.onUpdateUserProfile.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnUpdateUserProfileDataResolverFn/Templateresolvers--Subscription.onUpdateUserProfile.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnUpdateUserProfileDataResolverFn/Templateresolvers--Subscription.onUpdateUserProfile.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptionOnUpdateUserProfileDataResolverFn.AppSyncFunction": {"id": "SubscriptionOnUpdateUserProfileDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnUpdateUserProfileDataResolverFn/SubscriptionOnUpdateUserProfileDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptionOnUpdateUserProfileDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/fe3c43ada4b9d681a5e2312663ef7a73386424d73b73e51f8e2e9d4b50f7c502.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/e0cff47fb007f0bbf2a4e43ca256d6aa7ec109821769fd79fa7c5e83f0e7f9fc.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "subscriptionOnUpdateUserProfileResolver": {"id": "subscriptionOnUpdateUserProfileResolver", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/subscriptionOnUpdateUserProfileResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "onUpdateUserProfile", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["SubscriptiononUpdateUserProfileauth0FunctionSubscriptiononUpdateUserProfileauth0FunctionAppSyncFunction48B7E90B", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptiononUpdateUserProfilepostAuth0FunctionSubscriptiononUpdateUserProfilepostAuth0FunctionAppSyncFunction446153A7", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptionOnUpdateUserProfileDataResolverFnSubscriptionOnUpdateUserProfileDataResolverFnAppSyncFunctionBCF119CA", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Subscription\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"onUpdateUserProfile\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"NONE\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Subscription"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "SubscriptiononDeleteUserProfileauth0Function": {"id": "SubscriptiononDeleteUserProfileauth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononDeleteUserProfileauth0Function", "children": {"Templateresolvers--Subscription.onDeleteUserProfile.auth.1.req.vtl": {"id": "Templateresolvers--Subscription.onDeleteUserProfile.auth.1.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononDeleteUserProfileauth0Function/Templateresolvers--Subscription.onDeleteUserProfile.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononDeleteUserProfileauth0Function/Templateresolvers--Subscription.onDeleteUserProfile.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononDeleteUserProfileauth0Function/Templateresolvers--Subscription.onDeleteUserProfile.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononDeleteUserProfileauth0Function.AppSyncFunction": {"id": "SubscriptiononDeleteUserProfileauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononDeleteUserProfileauth0Function/SubscriptiononDeleteUserProfileauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptiononDeleteUserProfileauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a6f34d8595215fbaec56ec853de13cd014504c4abe3ff7457af53316ba2779.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptiononDeleteUserProfilepostAuth0Function": {"id": "SubscriptiononDeleteUserProfilepostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononDeleteUserProfilepostAuth0Function", "children": {"Templateresolvers--Subscription.onDeleteUserProfile.postAuth.1.req.vtl": {"id": "Templateresolvers--Subscription.onDeleteUserProfile.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononDeleteUserProfilepostAuth0Function/Templateresolvers--Subscription.onDeleteUserProfile.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononDeleteUserProfilepostAuth0Function/Templateresolvers--Subscription.onDeleteUserProfile.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononDeleteUserProfilepostAuth0Function/Templateresolvers--Subscription.onDeleteUserProfile.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononDeleteUserProfilepostAuth0Function.AppSyncFunction": {"id": "SubscriptiononDeleteUserProfilepostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptiononDeleteUserProfilepostAuth0Function/SubscriptiononDeleteUserProfilepostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptiononDeleteUserProfilepostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptionOnDeleteUserProfileDataResolverFn": {"id": "SubscriptionOnDeleteUserProfileDataResolverFn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnDeleteUserProfileDataResolverFn", "children": {"Templateresolvers--Subscription.onDeleteUserProfile.req.vtl": {"id": "Templateresolvers--Subscription.onDeleteUserProfile.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnDeleteUserProfileDataResolverFn/Templateresolvers--Subscription.onDeleteUserProfile.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnDeleteUserProfileDataResolverFn/Templateresolvers--Subscription.onDeleteUserProfile.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnDeleteUserProfileDataResolverFn/Templateresolvers--Subscription.onDeleteUserProfile.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Subscription.onDeleteUserProfile.res.vtl": {"id": "Templateresolvers--Subscription.onDeleteUserProfile.res.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnDeleteUserProfileDataResolverFn/Templateresolvers--Subscription.onDeleteUserProfile.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnDeleteUserProfileDataResolverFn/Templateresolvers--Subscription.onDeleteUserProfile.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnDeleteUserProfileDataResolverFn/Templateresolvers--Subscription.onDeleteUserProfile.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptionOnDeleteUserProfileDataResolverFn.AppSyncFunction": {"id": "SubscriptionOnDeleteUserProfileDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/SubscriptionOnDeleteUserProfileDataResolverFn/SubscriptionOnDeleteUserProfileDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptionOnDeleteUserProfileDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/fe3c43ada4b9d681a5e2312663ef7a73386424d73b73e51f8e2e9d4b50f7c502.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/e0cff47fb007f0bbf2a4e43ca256d6aa7ec109821769fd79fa7c5e83f0e7f9fc.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "subscriptionOnDeleteUserProfileResolver": {"id": "subscriptionOnDeleteUserProfileResolver", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/subscriptionOnDeleteUserProfileResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "onDeleteUserProfile", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["SubscriptiononDeleteUserProfileauth0FunctionSubscriptiononDeleteUserProfileauth0FunctionAppSyncFunction4558BD0E", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptiononDeleteUserProfilepostAuth0FunctionSubscriptiononDeleteUserProfilepostAuth0FunctionAppSyncFunction3E69A1F9", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptionOnDeleteUserProfileDataResolverFnSubscriptionOnDeleteUserProfileDataResolverFnAppSyncFunction6EDD6652", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Subscription\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"onDeleteUserProfile\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"NONE\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Subscription"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "UserProfileOwnerDataResolverFn": {"id": "UserProfileOwnerDataResolverFn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileOwnerDataResolverFn", "children": {"Templateresolvers--UserProfile.owner.req.vtl": {"id": "Templateresolvers--UserProfile.owner.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileOwnerDataResolverFn/Templateresolvers--UserProfile.owner.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileOwnerDataResolverFn/Templateresolvers--UserProfile.owner.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileOwnerDataResolverFn/Templateresolvers--UserProfile.owner.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--UserProfile.owner.res.vtl": {"id": "Templateresolvers--UserProfile.owner.res.vtl", "path": "amplify-RootsConnect-nkan<PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileOwnerDataResolverFn/Templateresolvers--UserProfile.owner.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileOwnerDataResolverFn/Templateresolvers--UserProfile.owner.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileOwnerDataResolverFn/Templateresolvers--UserProfile.owner.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "UserProfileOwnerDataResolverFn.AppSyncFunction": {"id": "UserProfileOwnerDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/UserProfileOwnerDataResolverFn/UserProfileOwnerDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "UserProfileOwnerDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/041534e5fd916595f752318f161512d7c7f83b9f2cf32d0f0be381c12253ff68.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/064303962e481067b44300212516363b99aaee539b6bafaf756fdd83ff0b60f0.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "userProfileOwnerResolver": {"id": "userProfileOwnerResolver", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/userProfileOwnerResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "owner", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["UserProfileOwnerDataResolverFnUserProfileOwnerDataResolverFnAppSyncFunction23A35218", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"UserProfile\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"owner\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"NONE\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "UserProfile"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "CDKMetadata": {"id": "CDKMetadata", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/CDKMetadata", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}, "Condition": {"id": "Condition", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/CDKMetadata/Condition", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/UserProfile/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.NestedStack", "version": "2.189.1"}}, "UserProfile.NestedStack": {"id": "UserProfile.NestedStack", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/UserProfile.NestedStack", "children": {"UserProfile.NestedStackResource": {"id": "UserProfile.NestedStackResource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile.NestedStack/UserProfile.NestedStackResource", "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFormation::Stack", "aws:cdk:cloudformation:props": {"parameters": {"DynamoDBModelTableReadIOPS": {"Ref": "DynamoDBModelTableReadIOPS"}, "DynamoDBModelTableWriteIOPS": {"Ref": "DynamoDBModelTableWriteIOPS"}, "DynamoDBBillingMode": {"Ref": "DynamoDBBillingMode"}, "DynamoDBEnablePointInTimeRecovery": {"Ref": "DynamoDBEnablePointInTimeRecovery"}, "DynamoDBEnableServerSideEncryption": {"Ref": "DynamoDBEnableServerSideEncryption"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916OutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableCA8BFB67": {"Fn::GetAtt": ["amplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResource86290833", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName": {"Fn::GetAtt": ["amplifyDataGraphQLAPINONEDS684BF699", "Name"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}}, "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}], "templateUrl": {"Fn::Join": ["", ["https://s3.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/ad5b215a6c21dd88a6acdc93b3d118a21f09feba2cf9df993ce9e4b1a429aad4.json"]]}}}, "constructInfo": {"fqn": "aws-cdk-lib.CfnStack", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "ConnectionRequest": {"id": "ConnectionRequest", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest", "children": {"DynamoDBModelTableReadIOPS": {"id": "DynamoDBModelTableReadIOPS", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/DynamoDBModelTableReadIOPS", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBModelTableWriteIOPS": {"id": "DynamoDBModelTableWriteIOPS", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/DynamoDBModelTableWriteIOPS", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBBillingMode": {"id": "DynamoDBBillingMode", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/DynamoDBBillingMode", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBEnablePointInTimeRecovery": {"id": "DynamoDBEnablePointInTimeRecovery", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/DynamoDBEnablePointInTimeRecovery", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBEnableServerSideEncryption": {"id": "DynamoDBEnableServerSideEncryption", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/DynamoDBEnableServerSideEncryption", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "HasEnvironmentParameter": {"id": "HasEnvironmentParameter", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/HasEnvironmentParameter", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}, "ShouldUsePayPerRequestBilling": {"id": "ShouldUsePayPerRequestBilling", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ShouldUsePayPerRequestBilling", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}, "ShouldUsePointInTimeRecovery": {"id": "ShouldUsePointInTimeRecovery", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ShouldUsePointInTimeRecovery", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}, "ConnectionRequestTable": {"id": "ConnectionRequestTable", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestTable", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestTable/Default", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestTable/Default/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.CustomResource", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "CustomTableConnectionRequestTable": {"id": "CustomTableConnectionRequestTable", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/CustomTableConnectionRequestTable", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.TableBase", "version": "2.189.1", "metadata": []}}, "GetAttConnectionRequestTableStreamArn": {"id": "GetAttConnectionRequestTableStreamArn", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/GetAttConnectionRequestTableStreamArn", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "GetAttConnectionRequestTableName": {"id": "GetAttConnectionRequestTableName", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/GetAttConnectionRequestTableName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "ConnectionRequestIAMRole": {"id": "ConnectionRequestIAMRole", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestIAMRole", "children": {"ImportConnectionRequestIAMRole": {"id": "ImportConnectionRequestIAMRole", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestIAMRole/ImportConnectionRequestIAMRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestIAMRole/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "appsync.amazonaws.com"}}], "Version": "2012-10-17"}, "policies": [{"policyName": "DynamoDBAccess", "policyDocument": {"Statement": [{"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:PutItem", "dynamodb:DeleteItem", "dynamodb:GetItem", "dynamodb:<PERSON><PERSON>", "dynamodb:Query", "dynamodb:UpdateItem", "dynamodb:ConditionCheckItem", "dynamodb:DescribeTable", "dynamodb:GetRecords", "dynamodb:GetShardIterator"], "Effect": "Allow", "Resource": [{"Fn::Sub": ["arn:${AWS::Partition}:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${tablename}", {"tablename": {"Fn::Join": ["", ["ConnectionRequest-", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "-NONE"]]}}]}, {"Fn::Sub": ["arn:${AWS::Partition}:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${tablename}/*", {"tablename": {"Fn::Join": ["", ["ConnectionRequest-", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "-NONE"]]}}]}]}], "Version": "2012-10-17"}}], "roleName": {"Fn::Join": ["", ["ConnectionRequestIAM90112b-", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "-NONE"]]}, "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.189.1", "metadata": []}}, "ImmutableRoleConnectionRequestIAMRole": {"id": "ImmutableRoleConnectionRequestIAMRole", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ImmutableRoleConnectionRequestIAMRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "ConnectionRequestDataSource": {"id": "ConnectionRequestDataSource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestDataSource", "children": {"Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestDataSource/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::DataSource", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dynamoDbConfig": {"tableName": {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionRequestTable", "TableArn"]}]}]}]}]}, "awsRegion": {"Ref": "AWS::Region"}}, "name": "ConnectionRequestTable", "serviceRoleArn": {"Fn::GetAtt": ["ConnectionRequestIAMRoleDBB29E05", "<PERSON><PERSON>"]}, "type": "AMAZON_DYNAMODB"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnDataSource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.DynamoDbDataSource", "version": "2.189.1"}}, "QuerygetConnectionRequestauth0Function": {"id": "QuerygetConnectionRequestauth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerygetConnectionRequestauth0Function", "children": {"Templateresolvers--Query.getConnectionRequest.auth.1.req.vtl": {"id": "Templateresolvers--Query.getConnectionRequest.auth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerygetConnectionRequestauth0Function/Templateresolvers--Query.getConnectionRequest.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerygetConnectionRequestauth0Function/Templateresolvers--Query.getConnectionRequest.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerygetConnectionRequestauth0Function/Templateresolvers--Query.getConnectionRequest.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QuerygetConnectionRequestauth0Function.AppSyncFunction": {"id": "QuerygetConnectionRequestauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerygetConnectionRequestauth0Function/QuerygetConnectionRequestauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "QuerygetConnectionRequestauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/6f1b01c4b2882f213b91241f489e2393bc199b215b8b1695ea62da2156719905.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "QuerygetConnectionRequestpostAuth0Function": {"id": "QuerygetConnectionRequestpostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerygetConnectionRequestpostAuth0Function", "children": {"Templateresolvers--Query.getConnectionRequest.postAuth.1.req.vtl": {"id": "Templateresolvers--Query.getConnectionRequest.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerygetConnectionRequestpostAuth0Function/Templateresolvers--Query.getConnectionRequest.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerygetConnectionRequestpostAuth0Function/Templateresolvers--Query.getConnectionRequest.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerygetConnectionRequestpostAuth0Function/Templateresolvers--Query.getConnectionRequest.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QuerygetConnectionRequestpostAuth0Function.AppSyncFunction": {"id": "QuerygetConnectionRequestpostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerygetConnectionRequestpostAuth0Function/QuerygetConnectionRequestpostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "QuerygetConnectionRequestpostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "QueryGetConnectionRequestDataResolverFn": {"id": "QueryGetConnectionRequestDataResolverFn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QueryGetConnectionRequestDataResolverFn", "children": {"Templateresolvers--Query.getConnectionRequest.req.vtl": {"id": "Templateresolvers--Query.getConnectionRequest.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QueryGetConnectionRequestDataResolverFn/Templateresolvers--Query.getConnectionRequest.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QueryGetConnectionRequestDataResolverFn/Templateresolvers--Query.getConnectionRequest.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QueryGetConnectionRequestDataResolverFn/Templateresolvers--Query.getConnectionRequest.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Query.getConnectionRequest.res.vtl": {"id": "Templateresolvers--Query.getConnectionRequest.res.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QueryGetConnectionRequestDataResolverFn/Templateresolvers--Query.getConnectionRequest.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QueryGetConnectionRequestDataResolverFn/Templateresolvers--Query.getConnectionRequest.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QueryGetConnectionRequestDataResolverFn/Templateresolvers--Query.getConnectionRequest.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QueryGetConnectionRequestDataResolverFn.AppSyncFunction": {"id": "QueryGetConnectionRequestDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QueryGetConnectionRequestDataResolverFn/QueryGetConnectionRequestDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Fn::GetAtt": ["ConnectionRequestDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "QueryGetConnectionRequestDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/08f4d557693d96c1a4efba0f9dc91330e4b19772fd5477c156468843e3d9cb5e.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/4c6a2d29f01c6091bd1d9afe16e5849d456c96f17c3b215938c8067399532719.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "queryGetConnectionRequestResolver": {"id": "queryGetConnectionRequestResolver", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/queryGetConnectionRequestResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "getConnectionRequest", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["QuerygetConnectionRequestauth0FunctionQuerygetConnectionRequestauth0FunctionAppSyncFunctionA41C0055", "FunctionId"]}, {"Fn::GetAtt": ["QuerygetConnectionRequestpostAuth0FunctionQuerygetConnectionRequestpostAuth0FunctionAppSyncFunction4CD4281F", "FunctionId"]}, {"Fn::GetAtt": ["QueryGetConnectionRequestDataResolverFnQueryGetConnectionRequestDataResolverFnAppSyncFunctionE41CF200", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Query\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"getConnectionRequest\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionRequestTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Query"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "QuerylistConnectionRequestsauth0Function": {"id": "QuerylistConnectionRequestsauth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerylistConnectionRequestsauth0Function", "children": {"Templateresolvers--Query.listConnectionRequests.auth.1.req.vtl": {"id": "Templateresolvers--Query.listConnectionRequests.auth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerylistConnectionRequestsauth0Function/Templateresolvers--Query.listConnectionRequests.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerylistConnectionRequestsauth0Function/Templateresolvers--Query.listConnectionRequests.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerylistConnectionRequestsauth0Function/Templateresolvers--Query.listConnectionRequests.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QuerylistConnectionRequestsauth0Function.AppSyncFunction": {"id": "QuerylistConnectionRequestsauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerylistConnectionRequestsauth0Function/QuerylistConnectionRequestsauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "QuerylistConnectionRequestsauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/6f1b01c4b2882f213b91241f489e2393bc199b215b8b1695ea62da2156719905.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "QuerylistConnectionRequestspostAuth0Function": {"id": "QuerylistConnectionRequestspostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerylistConnectionRequestspostAuth0Function", "children": {"Templateresolvers--Query.listConnectionRequests.postAuth.1.req.vtl": {"id": "Templateresolvers--Query.listConnectionRequests.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerylistConnectionRequestspostAuth0Function/Templateresolvers--Query.listConnectionRequests.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerylistConnectionRequestspostAuth0Function/Templateresolvers--Query.listConnectionRequests.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerylistConnectionRequestspostAuth0Function/Templateresolvers--Query.listConnectionRequests.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QuerylistConnectionRequestspostAuth0Function.AppSyncFunction": {"id": "QuerylistConnectionRequestspostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerylistConnectionRequestspostAuth0Function/QuerylistConnectionRequestspostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "QuerylistConnectionRequestspostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "QueryListConnectionRequestsDataResolverFn": {"id": "QueryListConnectionRequestsDataResolverFn", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QueryListConnectionRequestsDataResolverFn", "children": {"Templateresolvers--Query.listConnectionRequests.req.vtl": {"id": "Templateresolvers--Query.listConnectionRequests.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QueryListConnectionRequestsDataResolverFn/Templateresolvers--Query.listConnectionRequests.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QueryListConnectionRequestsDataResolverFn/Templateresolvers--Query.listConnectionRequests.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QueryListConnectionRequestsDataResolverFn/Templateresolvers--Query.listConnectionRequests.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Query.listConnectionRequests.res.vtl": {"id": "Templateresolvers--Query.listConnectionRequests.res.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QueryListConnectionRequestsDataResolverFn/Templateresolvers--Query.listConnectionRequests.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QueryListConnectionRequestsDataResolverFn/Templateresolvers--Query.listConnectionRequests.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QueryListConnectionRequestsDataResolverFn/Templateresolvers--Query.listConnectionRequests.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QueryListConnectionRequestsDataResolverFn.AppSyncFunction": {"id": "QueryListConnectionRequestsDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QueryListConnectionRequestsDataResolverFn/QueryListConnectionRequestsDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Fn::GetAtt": ["ConnectionRequestDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "QueryListConnectionRequestsDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/9fcbe070ecd3023c5bf5b966fa9584757db9762eef123bad0820bd87591b2174.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/cc01911d0269d4080ea57505dc445dfc315ef7ad85d3d9d4ea1357858bff451d.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "queryListConnectionRequestsResolver": {"id": "queryListConnectionRequestsResolver", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/queryListConnectionRequestsResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "listConnectionRequests", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["QuerylistConnectionRequestsauth0FunctionQuerylistConnectionRequestsauth0FunctionAppSyncFunction3684FEED", "FunctionId"]}, {"Fn::GetAtt": ["QuerylistConnectionRequestspostAuth0FunctionQuerylistConnectionRequestspostAuth0FunctionAppSyncFunction9E8C9C49", "FunctionId"]}, {"Fn::GetAtt": ["QueryListConnectionRequestsDataResolverFnQueryListConnectionRequestsDataResolverFnAppSyncFunction824D6927", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Query\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"listConnectionRequests\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionRequestTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Query"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "MutationcreateConnectionRequestinit0Function": {"id": "MutationcreateConnectionRequestinit0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestinit0Function", "children": {"Templateresolvers--Mutation.createConnectionRequest.init.1.req.vtl": {"id": "Templateresolvers--Mutation.createConnectionRequest.init.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestinit0Function/Templateresolvers--Mutation.createConnectionRequest.init.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestinit0Function/Templateresolvers--Mutation.createConnectionRequest.init.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestinit0Function/Templateresolvers--Mutation.createConnectionRequest.init.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationcreateConnectionRequestinit0Function.AppSyncFunction": {"id": "MutationcreateConnectionRequestinit0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestinit0Function/MutationcreateConnectionRequestinit0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "MutationcreateConnectionRequestinit0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/a183ddccbd956316c38ef97177b8f088ef0826f62023323f5ae6053d348ccffc.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationcreateConnectionRequestauth0Function": {"id": "MutationcreateConnectionRequestauth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestauth0Function", "children": {"Templateresolvers--Mutation.createConnectionRequest.auth.1.req.vtl": {"id": "Templateresolvers--Mutation.createConnectionRequest.auth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestauth0Function/Templateresolvers--Mutation.createConnectionRequest.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestauth0Function/Templateresolvers--Mutation.createConnectionRequest.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestauth0Function/Templateresolvers--Mutation.createConnectionRequest.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationcreateConnectionRequestauth0Function.AppSyncFunction": {"id": "MutationcreateConnectionRequestauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestauth0Function/MutationcreateConnectionRequestauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "MutationcreateConnectionRequestauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/6160f4b79b6a93016faacf67febf2c5ed13e1085b161db7c2d9ec18efa09197a.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationcreateConnectionRequestpostAuth0Function": {"id": "MutationcreateConnectionRequestpostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestpostAuth0Function", "children": {"Templateresolvers--Mutation.createConnectionRequest.postAuth.1.req.vtl": {"id": "Templateresolvers--Mutation.createConnectionRequest.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestpostAuth0Function/Templateresolvers--Mutation.createConnectionRequest.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestpostAuth0Function/Templateresolvers--Mutation.createConnectionRequest.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestpostAuth0Function/Templateresolvers--Mutation.createConnectionRequest.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationcreateConnectionRequestpostAuth0Function.AppSyncFunction": {"id": "MutationcreateConnectionRequestpostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestpostAuth0Function/MutationcreateConnectionRequestpostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "MutationcreateConnectionRequestpostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationCreateConnectionRequestDataResolverFn": {"id": "MutationCreateConnectionRequestDataResolverFn", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationCreateConnectionRequestDataResolverFn", "children": {"Templateresolvers--Mutation.createConnectionRequest.req.vtl": {"id": "Templateresolvers--Mutation.createConnectionRequest.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationCreateConnectionRequestDataResolverFn/Templateresolvers--Mutation.createConnectionRequest.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationCreateConnectionRequestDataResolverFn/Templateresolvers--Mutation.createConnectionRequest.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationCreateConnectionRequestDataResolverFn/Templateresolvers--Mutation.createConnectionRequest.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Mutation.createConnectionRequest.res.vtl": {"id": "Templateresolvers--Mutation.createConnectionRequest.res.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationCreateConnectionRequestDataResolverFn/Templateresolvers--Mutation.createConnectionRequest.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationCreateConnectionRequestDataResolverFn/Templateresolvers--Mutation.createConnectionRequest.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationCreateConnectionRequestDataResolverFn/Templateresolvers--Mutation.createConnectionRequest.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationCreateConnectionRequestDataResolverFn.AppSyncFunction": {"id": "MutationCreateConnectionRequestDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationCreateConnectionRequestDataResolverFn/MutationCreateConnectionRequestDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Fn::GetAtt": ["ConnectionRequestDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "MutationCreateConnectionRequestDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/06d7f74c3fda50f2346af03e3b3fc281f9cee655ea01b997d3e8b240b0eefd5d.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a52b72209a9dfa197b5e7367a5c378c5bb86de6e29ddd9e48b49a3fe54b249.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "mutationCreateConnectionRequestResolver": {"id": "mutationCreateConnectionRequestResolver", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/mutationCreateConnectionRequestResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "createConnectionRequest", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["MutationcreateConnectionRequestinit0FunctionMutationcreateConnectionRequestinit0FunctionAppSyncFunction247F59E8", "FunctionId"]}, {"Fn::GetAtt": ["MutationcreateConnectionRequestauth0FunctionMutationcreateConnectionRequestauth0FunctionAppSyncFunctionF8ADB385", "FunctionId"]}, {"Fn::GetAtt": ["MutationcreateConnectionRequestpostAuth0FunctionMutationcreateConnectionRequestpostAuth0FunctionAppSyncFunctionFC25477B", "FunctionId"]}, {"Fn::GetAtt": ["MutationCreateConnectionRequestDataResolverFnMutationCreateConnectionRequestDataResolverFnAppSyncFunction6BFA2576", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Mutation\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"createConnectionRequest\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionRequestTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Mutation"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "MutationupdateConnectionRequestinit0Function": {"id": "MutationupdateConnectionRequestinit0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestinit0Function", "children": {"Templateresolvers--Mutation.updateConnectionRequest.init.1.req.vtl": {"id": "Templateresolvers--Mutation.updateConnectionRequest.init.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestinit0Function/Templateresolvers--Mutation.updateConnectionRequest.init.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestinit0Function/Templateresolvers--Mutation.updateConnectionRequest.init.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestinit0Function/Templateresolvers--Mutation.updateConnectionRequest.init.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationupdateConnectionRequestinit0Function.AppSyncFunction": {"id": "MutationupdateConnectionRequestinit0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestinit0Function/MutationupdateConnectionRequestinit0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "MutationupdateConnectionRequestinit0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/06db846fd14e6fc371f22b12b5545ba8e2dbfeda85d8c8d586c71c282166657b.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationupdateConnectionRequestauth0Function": {"id": "MutationupdateConnectionRequestauth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestauth0Function", "children": {"Templateresolvers--Mutation.updateConnectionRequest.auth.1.req.vtl": {"id": "Templateresolvers--Mutation.updateConnectionRequest.auth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestauth0Function/Templateresolvers--Mutation.updateConnectionRequest.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestauth0Function/Templateresolvers--Mutation.updateConnectionRequest.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestauth0Function/Templateresolvers--Mutation.updateConnectionRequest.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Mutation.updateConnectionRequest.auth.1.res.vtl": {"id": "Templateresolvers--Mutation.updateConnectionRequest.auth.1.res.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestauth0Function/Templateresolvers--Mutation.updateConnectionRequest.auth.1.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestauth0Function/Templateresolvers--Mutation.updateConnectionRequest.auth.1.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestauth0Function/Templateresolvers--Mutation.updateConnectionRequest.auth.1.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationupdateConnectionRequestauth0Function.AppSyncFunction": {"id": "MutationupdateConnectionRequestauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestauth0Function/MutationupdateConnectionRequestauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Fn::GetAtt": ["ConnectionRequestDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "MutationupdateConnectionRequestauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/1f5fed297da9c32ae3af922bf3a38ccf23b956078887d16891ec06c20c64722c.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/48ab3887a29703dea320f36be3db1a3e35f7f87be239a424744d3fd05edd9e0d.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationupdateConnectionRequestpostAuth0Function": {"id": "MutationupdateConnectionRequestpostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestpostAuth0Function", "children": {"Templateresolvers--Mutation.updateConnectionRequest.postAuth.1.req.vtl": {"id": "Templateresolvers--Mutation.updateConnectionRequest.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestpostAuth0Function/Templateresolvers--Mutation.updateConnectionRequest.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestpostAuth0Function/Templateresolvers--Mutation.updateConnectionRequest.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestpostAuth0Function/Templateresolvers--Mutation.updateConnectionRequest.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationupdateConnectionRequestpostAuth0Function.AppSyncFunction": {"id": "MutationupdateConnectionRequestpostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestpostAuth0Function/MutationupdateConnectionRequestpostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "MutationupdateConnectionRequestpostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationUpdateConnectionRequestDataResolverFn": {"id": "MutationUpdateConnectionRequestDataResolverFn", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationUpdateConnectionRequestDataResolverFn", "children": {"Templateresolvers--Mutation.updateConnectionRequest.req.vtl": {"id": "Templateresolvers--Mutation.updateConnectionRequest.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationUpdateConnectionRequestDataResolverFn/Templateresolvers--Mutation.updateConnectionRequest.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationUpdateConnectionRequestDataResolverFn/Templateresolvers--Mutation.updateConnectionRequest.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationUpdateConnectionRequestDataResolverFn/Templateresolvers--Mutation.updateConnectionRequest.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Mutation.updateConnectionRequest.res.vtl": {"id": "Templateresolvers--Mutation.updateConnectionRequest.res.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationUpdateConnectionRequestDataResolverFn/Templateresolvers--Mutation.updateConnectionRequest.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationUpdateConnectionRequestDataResolverFn/Templateresolvers--Mutation.updateConnectionRequest.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationUpdateConnectionRequestDataResolverFn/Templateresolvers--Mutation.updateConnectionRequest.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationUpdateConnectionRequestDataResolverFn.AppSyncFunction": {"id": "MutationUpdateConnectionRequestDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationUpdateConnectionRequestDataResolverFn/MutationUpdateConnectionRequestDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Fn::GetAtt": ["ConnectionRequestDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "MutationUpdateConnectionRequestDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/474bf0776ec2164a13191d1a0a9e057154931e4918fea5086f49850d02a5371b.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a52b72209a9dfa197b5e7367a5c378c5bb86de6e29ddd9e48b49a3fe54b249.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "mutationUpdateConnectionRequestResolver": {"id": "mutationUpdateConnectionRequestResolver", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/mutationUpdateConnectionRequestResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "updateConnectionRequest", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["MutationupdateConnectionRequestinit0FunctionMutationupdateConnectionRequestinit0FunctionAppSyncFunction11EFE1FC", "FunctionId"]}, {"Fn::GetAtt": ["MutationupdateConnectionRequestauth0FunctionMutationupdateConnectionRequestauth0FunctionAppSyncFunctionC3E63FA3", "FunctionId"]}, {"Fn::GetAtt": ["MutationupdateConnectionRequestpostAuth0FunctionMutationupdateConnectionRequestpostAuth0FunctionAppSyncFunction5D2372C9", "FunctionId"]}, {"Fn::GetAtt": ["MutationUpdateConnectionRequestDataResolverFnMutationUpdateConnectionRequestDataResolverFnAppSyncFunctionB5413E62", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Mutation\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"updateConnectionRequest\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionRequestTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Mutation"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "MutationdeleteConnectionRequestauth0Function": {"id": "MutationdeleteConnectionRequestauth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationdeleteConnectionRequestauth0Function", "children": {"Templateresolvers--Mutation.deleteConnectionRequest.auth.1.req.vtl": {"id": "Templateresolvers--Mutation.deleteConnectionRequest.auth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationdeleteConnectionRequestauth0Function/Templateresolvers--Mutation.deleteConnectionRequest.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationdeleteConnectionRequestauth0Function/Templateresolvers--Mutation.deleteConnectionRequest.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationdeleteConnectionRequestauth0Function/Templateresolvers--Mutation.deleteConnectionRequest.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Mutation.deleteConnectionRequest.auth.1.res.vtl": {"id": "Templateresolvers--Mutation.deleteConnectionRequest.auth.1.res.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationdeleteConnectionRequestauth0Function/Templateresolvers--Mutation.deleteConnectionRequest.auth.1.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationdeleteConnectionRequestauth0Function/Templateresolvers--Mutation.deleteConnectionRequest.auth.1.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationdeleteConnectionRequestauth0Function/Templateresolvers--Mutation.deleteConnectionRequest.auth.1.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationdeleteConnectionRequestauth0Function.AppSyncFunction": {"id": "MutationdeleteConnectionRequestauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationdeleteConnectionRequestauth0Function/MutationdeleteConnectionRequestauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Fn::GetAtt": ["ConnectionRequestDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "MutationdeleteConnectionRequestauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/1f5fed297da9c32ae3af922bf3a38ccf23b956078887d16891ec06c20c64722c.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/05de9eab8d843ef7ef353a7792e3969abe58aae4a1016c0995c6ebddc6f9068f.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationdeleteConnectionRequestpostAuth0Function": {"id": "MutationdeleteConnectionRequestpostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationdeleteConnectionRequestpostAuth0Function", "children": {"Templateresolvers--Mutation.deleteConnectionRequest.postAuth.1.req.vtl": {"id": "Templateresolvers--Mutation.deleteConnectionRequest.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationdeleteConnectionRequestpostAuth0Function/Templateresolvers--Mutation.deleteConnectionRequest.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationdeleteConnectionRequestpostAuth0Function/Templateresolvers--Mutation.deleteConnectionRequest.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationdeleteConnectionRequestpostAuth0Function/Templateresolvers--Mutation.deleteConnectionRequest.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationdeleteConnectionRequestpostAuth0Function.AppSyncFunction": {"id": "MutationdeleteConnectionRequestpostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationdeleteConnectionRequestpostAuth0Function/MutationdeleteConnectionRequestpostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "MutationdeleteConnectionRequestpostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationDeleteConnectionRequestDataResolverFn": {"id": "MutationDeleteConnectionRequestDataResolverFn", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationDeleteConnectionRequestDataResolverFn", "children": {"Templateresolvers--Mutation.deleteConnectionRequest.req.vtl": {"id": "Templateresolvers--Mutation.deleteConnectionRequest.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationDeleteConnectionRequestDataResolverFn/Templateresolvers--Mutation.deleteConnectionRequest.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationDeleteConnectionRequestDataResolverFn/Templateresolvers--Mutation.deleteConnectionRequest.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationDeleteConnectionRequestDataResolverFn/Templateresolvers--Mutation.deleteConnectionRequest.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Mutation.deleteConnectionRequest.res.vtl": {"id": "Templateresolvers--Mutation.deleteConnectionRequest.res.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationDeleteConnectionRequestDataResolverFn/Templateresolvers--Mutation.deleteConnectionRequest.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationDeleteConnectionRequestDataResolverFn/Templateresolvers--Mutation.deleteConnectionRequest.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationDeleteConnectionRequestDataResolverFn/Templateresolvers--Mutation.deleteConnectionRequest.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationDeleteConnectionRequestDataResolverFn.AppSyncFunction": {"id": "MutationDeleteConnectionRequestDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationDeleteConnectionRequestDataResolverFn/MutationDeleteConnectionRequestDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Fn::GetAtt": ["ConnectionRequestDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "MutationDeleteConnectionRequestDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/4f7907d1209a2c9953a0c053df402c634e359546d70c7cc5c2e8e21ea734880f.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a52b72209a9dfa197b5e7367a5c378c5bb86de6e29ddd9e48b49a3fe54b249.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "mutationDeleteConnectionRequestResolver": {"id": "mutationDeleteConnectionRequestResolver", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/mutationDeleteConnectionRequestResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "deleteConnectionRequest", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["MutationdeleteConnectionRequestauth0FunctionMutationdeleteConnectionRequestauth0FunctionAppSyncFunction12E9EEE6", "FunctionId"]}, {"Fn::GetAtt": ["MutationdeleteConnectionRequestpostAuth0FunctionMutationdeleteConnectionRequestpostAuth0FunctionAppSyncFunction3CA5FEC0", "FunctionId"]}, {"Fn::GetAtt": ["MutationDeleteConnectionRequestDataResolverFnMutationDeleteConnectionRequestDataResolverFnAppSyncFunction6E38EBEC", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Mutation\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"deleteConnectionRequest\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionRequestTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Mutation"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "SubscriptiononCreateConnectionRequestauth0Function": {"id": "SubscriptiononCreateConnectionRequestauth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononCreateConnectionRequestauth0Function", "children": {"Templateresolvers--Subscription.onCreateConnectionRequest.auth.1.req.vtl": {"id": "Templateresolvers--Subscription.onCreateConnectionRequest.auth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononCreateConnectionRequestauth0Function/Templateresolvers--Subscription.onCreateConnectionRequest.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononCreateConnectionRequestauth0Function/Templateresolvers--Subscription.onCreateConnectionRequest.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononCreateConnectionRequestauth0Function/Templateresolvers--Subscription.onCreateConnectionRequest.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononCreateConnectionRequestauth0Function.AppSyncFunction": {"id": "SubscriptiononCreateConnectionRequestauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononCreateConnectionRequestauth0Function/SubscriptiononCreateConnectionRequestauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptiononCreateConnectionRequestauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a6f34d8595215fbaec56ec853de13cd014504c4abe3ff7457af53316ba2779.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptiononCreateConnectionRequestpostAuth0Function": {"id": "SubscriptiononCreateConnectionRequestpostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononCreateConnectionRequestpostAuth0Function", "children": {"Templateresolvers--Subscription.onCreateConnectionRequest.postAuth.1.req.vtl": {"id": "Templateresolvers--Subscription.onCreateConnectionRequest.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononCreateConnectionRequestpostAuth0Function/Templateresolvers--Subscription.onCreateConnectionRequest.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononCreateConnectionRequestpostAuth0Function/Templateresolvers--Subscription.onCreateConnectionRequest.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononCreateConnectionRequestpostAuth0Function/Templateresolvers--Subscription.onCreateConnectionRequest.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononCreateConnectionRequestpostAuth0Function.AppSyncFunction": {"id": "SubscriptiononCreateConnectionRequestpostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononCreateConnectionRequestpostAuth0Function/SubscriptiononCreateConnectionRequestpostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptiononCreateConnectionRequestpostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptionOnCreateConnectionRequestDataResolverFn": {"id": "SubscriptionOnCreateConnectionRequestDataResolverFn", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnCreateConnectionRequestDataResolverFn", "children": {"Templateresolvers--Subscription.onCreateConnectionRequest.req.vtl": {"id": "Templateresolvers--Subscription.onCreateConnectionRequest.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnCreateConnectionRequestDataResolverFn/Templateresolvers--Subscription.onCreateConnectionRequest.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnCreateConnectionRequestDataResolverFn/Templateresolvers--Subscription.onCreateConnectionRequest.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnCreateConnectionRequestDataResolverFn/Templateresolvers--Subscription.onCreateConnectionRequest.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Subscription.onCreateConnectionRequest.res.vtl": {"id": "Templateresolvers--Subscription.onCreateConnectionRequest.res.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnCreateConnectionRequestDataResolverFn/Templateresolvers--Subscription.onCreateConnectionRequest.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnCreateConnectionRequestDataResolverFn/Templateresolvers--Subscription.onCreateConnectionRequest.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnCreateConnectionRequestDataResolverFn/Templateresolvers--Subscription.onCreateConnectionRequest.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptionOnCreateConnectionRequestDataResolverFn.AppSyncFunction": {"id": "SubscriptionOnCreateConnectionRequestDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnCreateConnectionRequestDataResolverFn/SubscriptionOnCreateConnectionRequestDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptionOnCreateConnectionRequestDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/fe3c43ada4b9d681a5e2312663ef7a73386424d73b73e51f8e2e9d4b50f7c502.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/e0cff47fb007f0bbf2a4e43ca256d6aa7ec109821769fd79fa7c5e83f0e7f9fc.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "subscriptionOnCreateConnectionRequestResolver": {"id": "subscriptionOnCreateConnectionRequestResolver", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/subscriptionOnCreateConnectionRequestResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "onCreateConnectionRequest", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["SubscriptiononCreateConnectionRequestauth0FunctionSubscriptiononCreateConnectionRequestauth0FunctionAppSyncFunction76718EB6", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptiononCreateConnectionRequestpostAuth0FunctionSubscriptiononCreateConnectionRequestpostAuth0FunctionAppSyncFunction052BC134", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptionOnCreateConnectionRequestDataResolverFnSubscriptionOnCreateConnectionRequestDataResolverFnAppSyncFunction36214076", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Subscription\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"onCreateConnectionRequest\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"NONE\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Subscription"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "SubscriptiononUpdateConnectionRequestauth0Function": {"id": "SubscriptiononUpdateConnectionRequestauth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononUpdateConnectionRequestauth0Function", "children": {"Templateresolvers--Subscription.onUpdateConnectionRequest.auth.1.req.vtl": {"id": "Templateresolvers--Subscription.onUpdateConnectionRequest.auth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononUpdateConnectionRequestauth0Function/Templateresolvers--Subscription.onUpdateConnectionRequest.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononUpdateConnectionRequestauth0Function/Templateresolvers--Subscription.onUpdateConnectionRequest.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononUpdateConnectionRequestauth0Function/Templateresolvers--Subscription.onUpdateConnectionRequest.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononUpdateConnectionRequestauth0Function.AppSyncFunction": {"id": "SubscriptiononUpdateConnectionRequestauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononUpdateConnectionRequestauth0Function/SubscriptiononUpdateConnectionRequestauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptiononUpdateConnectionRequestauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a6f34d8595215fbaec56ec853de13cd014504c4abe3ff7457af53316ba2779.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptiononUpdateConnectionRequestpostAuth0Function": {"id": "SubscriptiononUpdateConnectionRequestpostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononUpdateConnectionRequestpostAuth0Function", "children": {"Templateresolvers--Subscription.onUpdateConnectionRequest.postAuth.1.req.vtl": {"id": "Templateresolvers--Subscription.onUpdateConnectionRequest.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononUpdateConnectionRequestpostAuth0Function/Templateresolvers--Subscription.onUpdateConnectionRequest.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononUpdateConnectionRequestpostAuth0Function/Templateresolvers--Subscription.onUpdateConnectionRequest.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononUpdateConnectionRequestpostAuth0Function/Templateresolvers--Subscription.onUpdateConnectionRequest.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononUpdateConnectionRequestpostAuth0Function.AppSyncFunction": {"id": "SubscriptiononUpdateConnectionRequestpostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononUpdateConnectionRequestpostAuth0Function/SubscriptiononUpdateConnectionRequestpostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptiononUpdateConnectionRequestpostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptionOnUpdateConnectionRequestDataResolverFn": {"id": "SubscriptionOnUpdateConnectionRequestDataResolverFn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnUpdateConnectionRequestDataResolverFn", "children": {"Templateresolvers--Subscription.onUpdateConnectionRequest.req.vtl": {"id": "Templateresolvers--Subscription.onUpdateConnectionRequest.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnUpdateConnectionRequestDataResolverFn/Templateresolvers--Subscription.onUpdateConnectionRequest.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnUpdateConnectionRequestDataResolverFn/Templateresolvers--Subscription.onUpdateConnectionRequest.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnUpdateConnectionRequestDataResolverFn/Templateresolvers--Subscription.onUpdateConnectionRequest.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Subscription.onUpdateConnectionRequest.res.vtl": {"id": "Templateresolvers--Subscription.onUpdateConnectionRequest.res.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnUpdateConnectionRequestDataResolverFn/Templateresolvers--Subscription.onUpdateConnectionRequest.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnUpdateConnectionRequestDataResolverFn/Templateresolvers--Subscription.onUpdateConnectionRequest.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnUpdateConnectionRequestDataResolverFn/Templateresolvers--Subscription.onUpdateConnectionRequest.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptionOnUpdateConnectionRequestDataResolverFn.AppSyncFunction": {"id": "SubscriptionOnUpdateConnectionRequestDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnUpdateConnectionRequestDataResolverFn/SubscriptionOnUpdateConnectionRequestDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptionOnUpdateConnectionRequestDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/fe3c43ada4b9d681a5e2312663ef7a73386424d73b73e51f8e2e9d4b50f7c502.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/e0cff47fb007f0bbf2a4e43ca256d6aa7ec109821769fd79fa7c5e83f0e7f9fc.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "subscriptionOnUpdateConnectionRequestResolver": {"id": "subscriptionOnUpdateConnectionRequestResolver", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/subscriptionOnUpdateConnectionRequestResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "onUpdateConnectionRequest", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["SubscriptiononUpdateConnectionRequestauth0FunctionSubscriptiononUpdateConnectionRequestauth0FunctionAppSyncFunction12E84EB8", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptiononUpdateConnectionRequestpostAuth0FunctionSubscriptiononUpdateConnectionRequestpostAuth0FunctionAppSyncFunction6109604D", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptionOnUpdateConnectionRequestDataResolverFnSubscriptionOnUpdateConnectionRequestDataResolverFnAppSyncFunction752B6F84", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Subscription\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"onUpdateConnectionRequest\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"NONE\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Subscription"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "SubscriptiononDeleteConnectionRequestauth0Function": {"id": "SubscriptiononDeleteConnectionRequestauth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononDeleteConnectionRequestauth0Function", "children": {"Templateresolvers--Subscription.onDeleteConnectionRequest.auth.1.req.vtl": {"id": "Templateresolvers--Subscription.onDeleteConnectionRequest.auth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononDeleteConnectionRequestauth0Function/Templateresolvers--Subscription.onDeleteConnectionRequest.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononDeleteConnectionRequestauth0Function/Templateresolvers--Subscription.onDeleteConnectionRequest.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononDeleteConnectionRequestauth0Function/Templateresolvers--Subscription.onDeleteConnectionRequest.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononDeleteConnectionRequestauth0Function.AppSyncFunction": {"id": "SubscriptiononDeleteConnectionRequestauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononDeleteConnectionRequestauth0Function/SubscriptiononDeleteConnectionRequestauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptiononDeleteConnectionRequestauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a6f34d8595215fbaec56ec853de13cd014504c4abe3ff7457af53316ba2779.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptiononDeleteConnectionRequestpostAuth0Function": {"id": "SubscriptiononDeleteConnectionRequestpostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononDeleteConnectionRequestpostAuth0Function", "children": {"Templateresolvers--Subscription.onDeleteConnectionRequest.postAuth.1.req.vtl": {"id": "Templateresolvers--Subscription.onDeleteConnectionRequest.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononDeleteConnectionRequestpostAuth0Function/Templateresolvers--Subscription.onDeleteConnectionRequest.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononDeleteConnectionRequestpostAuth0Function/Templateresolvers--Subscription.onDeleteConnectionRequest.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononDeleteConnectionRequestpostAuth0Function/Templateresolvers--Subscription.onDeleteConnectionRequest.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononDeleteConnectionRequestpostAuth0Function.AppSyncFunction": {"id": "SubscriptiononDeleteConnectionRequestpostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononDeleteConnectionRequestpostAuth0Function/SubscriptiononDeleteConnectionRequestpostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptiononDeleteConnectionRequestpostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptionOnDeleteConnectionRequestDataResolverFn": {"id": "SubscriptionOnDeleteConnectionRequestDataResolverFn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnDeleteConnectionRequestDataResolverFn", "children": {"Templateresolvers--Subscription.onDeleteConnectionRequest.req.vtl": {"id": "Templateresolvers--Subscription.onDeleteConnectionRequest.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnDeleteConnectionRequestDataResolverFn/Templateresolvers--Subscription.onDeleteConnectionRequest.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnDeleteConnectionRequestDataResolverFn/Templateresolvers--Subscription.onDeleteConnectionRequest.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnDeleteConnectionRequestDataResolverFn/Templateresolvers--Subscription.onDeleteConnectionRequest.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Subscription.onDeleteConnectionRequest.res.vtl": {"id": "Templateresolvers--Subscription.onDeleteConnectionRequest.res.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnDeleteConnectionRequestDataResolverFn/Templateresolvers--Subscription.onDeleteConnectionRequest.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnDeleteConnectionRequestDataResolverFn/Templateresolvers--Subscription.onDeleteConnectionRequest.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnDeleteConnectionRequestDataResolverFn/Templateresolvers--Subscription.onDeleteConnectionRequest.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptionOnDeleteConnectionRequestDataResolverFn.AppSyncFunction": {"id": "SubscriptionOnDeleteConnectionRequestDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnDeleteConnectionRequestDataResolverFn/SubscriptionOnDeleteConnectionRequestDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptionOnDeleteConnectionRequestDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/fe3c43ada4b9d681a5e2312663ef7a73386424d73b73e51f8e2e9d4b50f7c502.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/e0cff47fb007f0bbf2a4e43ca256d6aa7ec109821769fd79fa7c5e83f0e7f9fc.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "subscriptionOnDeleteConnectionRequestResolver": {"id": "subscriptionOnDeleteConnectionRequestResolver", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/subscriptionOnDeleteConnectionRequestResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "onDeleteConnectionRequest", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["SubscriptiononDeleteConnectionRequestauth0FunctionSubscriptiononDeleteConnectionRequestauth0FunctionAppSyncFunction683C01A0", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptiononDeleteConnectionRequestpostAuth0FunctionSubscriptiononDeleteConnectionRequestpostAuth0FunctionAppSyncFunctionE8AB16B2", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptionOnDeleteConnectionRequestDataResolverFnSubscriptionOnDeleteConnectionRequestDataResolverFnAppSyncFunctionDD6B194B", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Subscription\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"onDeleteConnectionRequest\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"NONE\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Subscription"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "ConnectionRequestOwnerDataResolverFn": {"id": "ConnectionRequestOwnerDataResolverFn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestOwnerDataResolverFn", "children": {"Templateresolvers--ConnectionRequest.owner.req.vtl": {"id": "Templateresolvers--ConnectionRequest.owner.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestOwnerDataResolverFn/Templateresolvers--ConnectionRequest.owner.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestOwnerDataResolverFn/Templateresolvers--ConnectionRequest.owner.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestOwnerDataResolverFn/Templateresolvers--ConnectionRequest.owner.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--ConnectionRequest.owner.res.vtl": {"id": "Templateresolvers--ConnectionRequest.owner.res.vtl", "path": "amplify-RootsConnect-nkan<PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestOwnerDataResolverFn/Templateresolvers--ConnectionRequest.owner.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestOwnerDataResolverFn/Templateresolvers--ConnectionRequest.owner.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestOwnerDataResolverFn/Templateresolvers--ConnectionRequest.owner.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "ConnectionRequestOwnerDataResolverFn.AppSyncFunction": {"id": "ConnectionRequestOwnerDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestOwnerDataResolverFn/ConnectionRequestOwnerDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "ConnectionRequestOwnerDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/041534e5fd916595f752318f161512d7c7f83b9f2cf32d0f0be381c12253ff68.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/064303962e481067b44300212516363b99aaee539b6bafaf756fdd83ff0b60f0.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "connectionRequestOwnerResolver": {"id": "connectionRequestOwnerResolver", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/connectionRequestOwnerResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "owner", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["ConnectionRequestOwnerDataResolverFnConnectionRequestOwnerDataResolverFnAppSyncFunction2C2E4DA9", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"ConnectionRequest\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"owner\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"NONE\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "ConnectionRequest"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "CDKMetadata": {"id": "CDKMetadata", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/CDKMetadata", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}, "Condition": {"id": "Condition", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/CDKMetadata/Condition", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.NestedStack", "version": "2.189.1"}}, "ConnectionRequest.NestedStack": {"id": "ConnectionRequest.NestedStack", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest.NestedStack", "children": {"ConnectionRequest.NestedStackResource": {"id": "ConnectionRequest.NestedStackResource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest.NestedStack/ConnectionRequest.NestedStackResource", "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFormation::Stack", "aws:cdk:cloudformation:props": {"parameters": {"DynamoDBModelTableReadIOPS": {"Ref": "DynamoDBModelTableReadIOPS"}, "DynamoDBModelTableWriteIOPS": {"Ref": "DynamoDBModelTableWriteIOPS"}, "DynamoDBBillingMode": {"Ref": "DynamoDBBillingMode"}, "DynamoDBEnablePointInTimeRecovery": {"Ref": "DynamoDBEnablePointInTimeRecovery"}, "DynamoDBEnableServerSideEncryption": {"Ref": "DynamoDBEnableServerSideEncryption"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916OutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableCA8BFB67": {"Fn::GetAtt": ["amplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResource86290833", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName": {"Fn::GetAtt": ["amplifyDataGraphQLAPINONEDS684BF699", "Name"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}}, "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}], "templateUrl": {"Fn::Join": ["", ["https://s3.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/efec2b1b3967e7146f0451460b9881ff86048864fe6b8861196a490317bade5c.json"]]}}}, "constructInfo": {"fqn": "aws-cdk-lib.CfnStack", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "Connection": {"id": "Connection", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection", "children": {"DynamoDBModelTableReadIOPS": {"id": "DynamoDBModelTableReadIOPS", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/DynamoDBModelTableReadIOPS", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBModelTableWriteIOPS": {"id": "DynamoDBModelTableWriteIOPS", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/DynamoDBModelTableWriteIOPS", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBBillingMode": {"id": "DynamoDBBillingMode", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/DynamoDBBillingMode", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBEnablePointInTimeRecovery": {"id": "DynamoDBEnablePointInTimeRecovery", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/DynamoDBEnablePointInTimeRecovery", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBEnableServerSideEncryption": {"id": "DynamoDBEnableServerSideEncryption", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/DynamoDBEnableServerSideEncryption", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "HasEnvironmentParameter": {"id": "HasEnvironmentParameter", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/HasEnvironmentParameter", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}, "ShouldUsePayPerRequestBilling": {"id": "ShouldUsePayPerRequestBilling", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/ShouldUsePayPerRequestBilling", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}, "ShouldUsePointInTimeRecovery": {"id": "ShouldUsePointInTimeRecovery", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/ShouldUsePointInTimeRecovery", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}, "ConnectionTable": {"id": "ConnectionTable", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/ConnectionTable", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/ConnectionTable/Default", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/ConnectionTable/Default/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.CustomResource", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "CustomTableConnectionTable": {"id": "CustomTableConnectionTable", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/CustomTableConnectionTable", "constructInfo": {"fqn": "aws-cdk-lib.aws_dynamodb.TableBase", "version": "2.189.1", "metadata": []}}, "GetAttConnectionTableStreamArn": {"id": "GetAttConnectionTableStreamArn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/GetAttConnectionTableStreamArn", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "GetAttConnectionTableName": {"id": "GetAttConnectionTableName", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/GetAttConnectionTableName", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "ConnectionIAMRole": {"id": "ConnectionIAMRole", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/ConnectionIAMRole", "children": {"ImportConnectionIAMRole": {"id": "ImportConnectionIAMRole", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/ConnectionIAMRole/ImportConnectionIAMRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/ConnectionIAMRole/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "appsync.amazonaws.com"}}], "Version": "2012-10-17"}, "policies": [{"policyName": "DynamoDBAccess", "policyDocument": {"Statement": [{"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:PutItem", "dynamodb:DeleteItem", "dynamodb:GetItem", "dynamodb:<PERSON><PERSON>", "dynamodb:Query", "dynamodb:UpdateItem", "dynamodb:ConditionCheckItem", "dynamodb:DescribeTable", "dynamodb:GetRecords", "dynamodb:GetShardIterator"], "Effect": "Allow", "Resource": [{"Fn::Sub": ["arn:${AWS::Partition}:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${tablename}", {"tablename": {"Fn::Join": ["", ["Connection-", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "-NONE"]]}}]}, {"Fn::Sub": ["arn:${AWS::Partition}:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${tablename}/*", {"tablename": {"Fn::Join": ["", ["Connection-", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "-NONE"]]}}]}]}], "Version": "2012-10-17"}}], "roleName": {"Fn::Join": ["", ["ConnectionIAMRole1fac14-", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "-NONE"]]}, "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.189.1", "metadata": []}}, "ImmutableRoleConnectionIAMRole": {"id": "ImmutableRoleConnectionIAMRole", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/ImmutableRoleConnectionIAMRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "ConnectionDataSource": {"id": "ConnectionDataSource", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/ConnectionDataSource", "children": {"Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/ConnectionDataSource/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::DataSource", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dynamoDbConfig": {"tableName": {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionTable", "TableArn"]}]}]}]}]}, "awsRegion": {"Ref": "AWS::Region"}}, "name": "ConnectionTable", "serviceRoleArn": {"Fn::GetAtt": ["ConnectionIAMRole812E1EC8", "<PERSON><PERSON>"]}, "type": "AMAZON_DYNAMODB"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnDataSource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.DynamoDbDataSource", "version": "2.189.1"}}, "QuerygetConnectionauth0Function": {"id": "QuerygetConnectionauth0Function", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerygetConnectionauth0Function", "children": {"Templateresolvers--Query.getConnection.auth.1.req.vtl": {"id": "Templateresolvers--Query.getConnection.auth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerygetConnectionauth0Function/Templateresolvers--Query.getConnection.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerygetConnectionauth0Function/Templateresolvers--Query.getConnection.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerygetConnectionauth0Function/Templateresolvers--Query.getConnection.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QuerygetConnectionauth0Function.AppSyncFunction": {"id": "QuerygetConnectionauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerygetConnectionauth0Function/QuerygetConnectionauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "QuerygetConnectionauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/6677b1986afebbf1f4026c57099890e32ab4e450e422c5e3c930b085a73b6b60.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "QuerygetConnectionpostAuth0Function": {"id": "QuerygetConnectionpostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerygetConnectionpostAuth0Function", "children": {"Templateresolvers--Query.getConnection.postAuth.1.req.vtl": {"id": "Templateresolvers--Query.getConnection.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerygetConnectionpostAuth0Function/Templateresolvers--Query.getConnection.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerygetConnectionpostAuth0Function/Templateresolvers--Query.getConnection.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerygetConnectionpostAuth0Function/Templateresolvers--Query.getConnection.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QuerygetConnectionpostAuth0Function.AppSyncFunction": {"id": "QuerygetConnectionpostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerygetConnectionpostAuth0Function/QuerygetConnectionpostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "QuerygetConnectionpostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "QueryGetConnectionDataResolverFn": {"id": "QueryGetConnectionDataResolverFn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/QueryGetConnectionDataResolverFn", "children": {"Templateresolvers--Query.getConnection.req.vtl": {"id": "Templateresolvers--Query.getConnection.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/QueryGetConnectionDataResolverFn/Templateresolvers--Query.getConnection.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/QueryGetConnectionDataResolverFn/Templateresolvers--Query.getConnection.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QueryGetConnectionDataResolverFn/Templateresolvers--Query.getConnection.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Query.getConnection.res.vtl": {"id": "Templateresolvers--Query.getConnection.res.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/QueryGetConnectionDataResolverFn/Templateresolvers--Query.getConnection.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QueryGetConnectionDataResolverFn/Templateresolvers--Query.getConnection.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QueryGetConnectionDataResolverFn/Templateresolvers--Query.getConnection.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QueryGetConnectionDataResolverFn.AppSyncFunction": {"id": "QueryGetConnectionDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/QueryGetConnectionDataResolverFn/QueryGetConnectionDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Fn::GetAtt": ["ConnectionDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "QueryGetConnectionDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/08f4d557693d96c1a4efba0f9dc91330e4b19772fd5477c156468843e3d9cb5e.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/4c6a2d29f01c6091bd1d9afe16e5849d456c96f17c3b215938c8067399532719.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "queryGetConnectionResolver": {"id": "queryGetConnectionResolver", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/queryGetConnectionResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "getConnection", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["QuerygetConnectionauth0FunctionQuerygetConnectionauth0FunctionAppSyncFunctionFCA57863", "FunctionId"]}, {"Fn::GetAtt": ["QuerygetConnectionpostAuth0FunctionQuerygetConnectionpostAuth0FunctionAppSyncFunction50FB355D", "FunctionId"]}, {"Fn::GetAtt": ["QueryGetConnectionDataResolverFnQueryGetConnectionDataResolverFnAppSyncFunction7F579500", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Query\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"getConnection\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Query"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "QuerylistConnectionsauth0Function": {"id": "QuerylistConnectionsauth0Function", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerylistConnectionsauth0Function", "children": {"Templateresolvers--Query.listConnections.auth.1.req.vtl": {"id": "Templateresolvers--Query.listConnections.auth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerylistConnectionsauth0Function/Templateresolvers--Query.listConnections.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerylistConnectionsauth0Function/Templateresolvers--Query.listConnections.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerylistConnectionsauth0Function/Templateresolvers--Query.listConnections.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QuerylistConnectionsauth0Function.AppSyncFunction": {"id": "QuerylistConnectionsauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerylistConnectionsauth0Function/QuerylistConnectionsauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "QuerylistConnectionsauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/6677b1986afebbf1f4026c57099890e32ab4e450e422c5e3c930b085a73b6b60.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "QuerylistConnectionspostAuth0Function": {"id": "QuerylistConnectionspostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerylistConnectionspostAuth0Function", "children": {"Templateresolvers--Query.listConnections.postAuth.1.req.vtl": {"id": "Templateresolvers--Query.listConnections.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerylistConnectionspostAuth0Function/Templateresolvers--Query.listConnections.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerylistConnectionspostAuth0Function/Templateresolvers--Query.listConnections.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerylistConnectionspostAuth0Function/Templateresolvers--Query.listConnections.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QuerylistConnectionspostAuth0Function.AppSyncFunction": {"id": "QuerylistConnectionspostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerylistConnectionspostAuth0Function/QuerylistConnectionspostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "QuerylistConnectionspostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "QueryListConnectionsDataResolverFn": {"id": "QueryListConnectionsDataResolverFn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/QueryListConnectionsDataResolverFn", "children": {"Templateresolvers--Query.listConnections.req.vtl": {"id": "Templateresolvers--Query.listConnections.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/QueryListConnectionsDataResolverFn/Templateresolvers--Query.listConnections.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/QueryListConnectionsDataResolverFn/Templateresolvers--Query.listConnections.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QueryListConnectionsDataResolverFn/Templateresolvers--Query.listConnections.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Query.listConnections.res.vtl": {"id": "Templateresolvers--Query.listConnections.res.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QueryListConnectionsDataResolverFn/Templateresolvers--Query.listConnections.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QueryListConnectionsDataResolverFn/Templateresolvers--Query.listConnections.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QueryListConnectionsDataResolverFn/Templateresolvers--Query.listConnections.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "QueryListConnectionsDataResolverFn.AppSyncFunction": {"id": "QueryListConnectionsDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/QueryListConnectionsDataResolverFn/QueryListConnectionsDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Fn::GetAtt": ["ConnectionDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "QueryListConnectionsDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/9fcbe070ecd3023c5bf5b966fa9584757db9762eef123bad0820bd87591b2174.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/cc01911d0269d4080ea57505dc445dfc315ef7ad85d3d9d4ea1357858bff451d.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "queryListConnectionsResolver": {"id": "queryListConnectionsResolver", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/queryListConnectionsResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "listConnections", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["QuerylistConnectionsauth0FunctionQuerylistConnectionsauth0FunctionAppSyncFunction35EE0264", "FunctionId"]}, {"Fn::GetAtt": ["QuerylistConnectionspostAuth0FunctionQuerylistConnectionspostAuth0FunctionAppSyncFunction8F17C74E", "FunctionId"]}, {"Fn::GetAtt": ["QueryListConnectionsDataResolverFnQueryListConnectionsDataResolverFnAppSyncFunction904DCC89", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Query\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"listConnections\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Query"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "MutationcreateConnectioninit0Function": {"id": "MutationcreateConnectioninit0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectioninit0Function", "children": {"Templateresolvers--Mutation.createConnection.init.1.req.vtl": {"id": "Templateresolvers--Mutation.createConnection.init.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectioninit0Function/Templateresolvers--Mutation.createConnection.init.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectioninit0Function/Templateresolvers--Mutation.createConnection.init.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectioninit0Function/Templateresolvers--Mutation.createConnection.init.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationcreateConnectioninit0Function.AppSyncFunction": {"id": "MutationcreateConnectioninit0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectioninit0Function/MutationcreateConnectioninit0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "MutationcreateConnectioninit0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/a183ddccbd956316c38ef97177b8f088ef0826f62023323f5ae6053d348ccffc.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationcreateConnectioninit1Function": {"id": "MutationcreateConnectioninit1Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectioninit1Function", "children": {"Templateresolvers--Mutation.createConnection.init.2.req.vtl": {"id": "Templateresolvers--Mutation.createConnection.init.2.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectioninit1Function/Templateresolvers--Mutation.createConnection.init.2.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectioninit1Function/Templateresolvers--Mutation.createConnection.init.2.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectioninit1Function/Templateresolvers--Mutation.createConnection.init.2.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationcreateConnectioninit1Function.AppSyncFunction": {"id": "MutationcreateConnectioninit1Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectioninit1Function/MutationcreateConnectioninit1Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "MutationcreateConnectioninit1Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/747896db78e02d287e1575351ce928b81bd0fd0aab966b2ffc55a576f37cf80e.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationcreateConnectionauth0Function": {"id": "MutationcreateConnectionauth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectionauth0Function", "children": {"Templateresolvers--Mutation.createConnection.auth.1.req.vtl": {"id": "Templateresolvers--Mutation.createConnection.auth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectionauth0Function/Templateresolvers--Mutation.createConnection.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectionauth0Function/Templateresolvers--Mutation.createConnection.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectionauth0Function/Templateresolvers--Mutation.createConnection.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationcreateConnectionauth0Function.AppSyncFunction": {"id": "MutationcreateConnectionauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectionauth0Function/MutationcreateConnectionauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "MutationcreateConnectionauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/eb2c75f91fb7c102ca64406094e96480b71004b54804dc990a5fdb8903ef2bca.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationcreateConnectionpostAuth0Function": {"id": "MutationcreateConnectionpostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectionpostAuth0Function", "children": {"Templateresolvers--Mutation.createConnection.postAuth.1.req.vtl": {"id": "Templateresolvers--Mutation.createConnection.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectionpostAuth0Function/Templateresolvers--Mutation.createConnection.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectionpostAuth0Function/Templateresolvers--Mutation.createConnection.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectionpostAuth0Function/Templateresolvers--Mutation.createConnection.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationcreateConnectionpostAuth0Function.AppSyncFunction": {"id": "MutationcreateConnectionpostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectionpostAuth0Function/MutationcreateConnectionpostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "MutationcreateConnectionpostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationCreateConnectionDataResolverFn": {"id": "MutationCreateConnectionDataResolverFn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationCreateConnectionDataResolverFn", "children": {"Templateresolvers--Mutation.createConnection.req.vtl": {"id": "Templateresolvers--Mutation.createConnection.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationCreateConnectionDataResolverFn/Templateresolvers--Mutation.createConnection.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationCreateConnectionDataResolverFn/Templateresolvers--Mutation.createConnection.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationCreateConnectionDataResolverFn/Templateresolvers--Mutation.createConnection.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Mutation.createConnection.res.vtl": {"id": "Templateresolvers--Mutation.createConnection.res.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationCreateConnectionDataResolverFn/Templateresolvers--Mutation.createConnection.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationCreateConnectionDataResolverFn/Templateresolvers--Mutation.createConnection.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationCreateConnectionDataResolverFn/Templateresolvers--Mutation.createConnection.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationCreateConnectionDataResolverFn.AppSyncFunction": {"id": "MutationCreateConnectionDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationCreateConnectionDataResolverFn/MutationCreateConnectionDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Fn::GetAtt": ["ConnectionDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "MutationCreateConnectionDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/94c06debde92fed2e2c6ad051e85a3e024e4712d18d6ee6fb1cd56cda8155001.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a52b72209a9dfa197b5e7367a5c378c5bb86de6e29ddd9e48b49a3fe54b249.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "mutationCreateConnectionResolver": {"id": "mutationCreateConnectionResolver", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/mutationCreateConnectionResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "createConnection", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["MutationcreateConnectioninit0FunctionMutationcreateConnectioninit0FunctionAppSyncFunctionA6FE7EFA", "FunctionId"]}, {"Fn::GetAtt": ["MutationcreateConnectioninit1FunctionMutationcreateConnectioninit1FunctionAppSyncFunction078CC4EA", "FunctionId"]}, {"Fn::GetAtt": ["MutationcreateConnectionauth0FunctionMutationcreateConnectionauth0FunctionAppSyncFunction1149FE23", "FunctionId"]}, {"Fn::GetAtt": ["MutationcreateConnectionpostAuth0FunctionMutationcreateConnectionpostAuth0FunctionAppSyncFunctionBBE6C4B6", "FunctionId"]}, {"Fn::GetAtt": ["MutationCreateConnectionDataResolverFnMutationCreateConnectionDataResolverFnAppSyncFunctionB09DB127", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Mutation\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"createConnection\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Mutation"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "MutationupdateConnectioninit0Function": {"id": "MutationupdateConnectioninit0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectioninit0Function", "children": {"Templateresolvers--Mutation.updateConnection.init.1.req.vtl": {"id": "Templateresolvers--Mutation.updateConnection.init.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectioninit0Function/Templateresolvers--Mutation.updateConnection.init.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectioninit0Function/Templateresolvers--Mutation.updateConnection.init.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectioninit0Function/Templateresolvers--Mutation.updateConnection.init.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationupdateConnectioninit0Function.AppSyncFunction": {"id": "MutationupdateConnectioninit0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectioninit0Function/MutationupdateConnectioninit0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "MutationupdateConnectioninit0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/06db846fd14e6fc371f22b12b5545ba8e2dbfeda85d8c8d586c71c282166657b.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationupdateConnectionauth0Function": {"id": "MutationupdateConnectionauth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectionauth0Function", "children": {"Templateresolvers--Mutation.updateConnection.auth.1.req.vtl": {"id": "Templateresolvers--Mutation.updateConnection.auth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectionauth0Function/Templateresolvers--Mutation.updateConnection.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectionauth0Function/Templateresolvers--Mutation.updateConnection.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectionauth0Function/Templateresolvers--Mutation.updateConnection.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Mutation.updateConnection.auth.1.res.vtl": {"id": "Templateresolvers--Mutation.updateConnection.auth.1.res.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectionauth0Function/Templateresolvers--Mutation.updateConnection.auth.1.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-<PERSON><PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectionauth0Function/Templateresolvers--Mutation.updateConnection.auth.1.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-<PERSON><PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectionauth0Function/Templateresolvers--Mutation.updateConnection.auth.1.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationupdateConnectionauth0Function.AppSyncFunction": {"id": "MutationupdateConnectionauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectionauth0Function/MutationupdateConnectionauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Fn::GetAtt": ["ConnectionDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "MutationupdateConnectionauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/1f5fed297da9c32ae3af922bf3a38ccf23b956078887d16891ec06c20c64722c.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/fe139862c8f0faccbaea00d381f2ee946ff166b4a3ff39527a46c14858c9626f.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationupdateConnectionpostAuth0Function": {"id": "MutationupdateConnectionpostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectionpostAuth0Function", "children": {"Templateresolvers--Mutation.updateConnection.postAuth.1.req.vtl": {"id": "Templateresolvers--Mutation.updateConnection.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectionpostAuth0Function/Templateresolvers--Mutation.updateConnection.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectionpostAuth0Function/Templateresolvers--Mutation.updateConnection.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectionpostAuth0Function/Templateresolvers--Mutation.updateConnection.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationupdateConnectionpostAuth0Function.AppSyncFunction": {"id": "MutationupdateConnectionpostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectionpostAuth0Function/MutationupdateConnectionpostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "MutationupdateConnectionpostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationUpdateConnectionDataResolverFn": {"id": "MutationUpdateConnectionDataResolverFn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationUpdateConnectionDataResolverFn", "children": {"Templateresolvers--Mutation.updateConnection.req.vtl": {"id": "Templateresolvers--Mutation.updateConnection.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationUpdateConnectionDataResolverFn/Templateresolvers--Mutation.updateConnection.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationUpdateConnectionDataResolverFn/Templateresolvers--Mutation.updateConnection.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationUpdateConnectionDataResolverFn/Templateresolvers--Mutation.updateConnection.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Mutation.updateConnection.res.vtl": {"id": "Templateresolvers--Mutation.updateConnection.res.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationUpdateConnectionDataResolverFn/Templateresolvers--Mutation.updateConnection.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationUpdateConnectionDataResolverFn/Templateresolvers--Mutation.updateConnection.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationUpdateConnectionDataResolverFn/Templateresolvers--Mutation.updateConnection.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationUpdateConnectionDataResolverFn.AppSyncFunction": {"id": "MutationUpdateConnectionDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationUpdateConnectionDataResolverFn/MutationUpdateConnectionDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Fn::GetAtt": ["ConnectionDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "MutationUpdateConnectionDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/474bf0776ec2164a13191d1a0a9e057154931e4918fea5086f49850d02a5371b.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a52b72209a9dfa197b5e7367a5c378c5bb86de6e29ddd9e48b49a3fe54b249.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "mutationUpdateConnectionResolver": {"id": "mutationUpdateConnectionResolver", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/mutationUpdateConnectionResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "updateConnection", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["MutationupdateConnectioninit0FunctionMutationupdateConnectioninit0FunctionAppSyncFunction8D243403", "FunctionId"]}, {"Fn::GetAtt": ["MutationupdateConnectionauth0FunctionMutationupdateConnectionauth0FunctionAppSyncFunction981E4BF8", "FunctionId"]}, {"Fn::GetAtt": ["MutationupdateConnectionpostAuth0FunctionMutationupdateConnectionpostAuth0FunctionAppSyncFunctionF984A8D8", "FunctionId"]}, {"Fn::GetAtt": ["MutationUpdateConnectionDataResolverFnMutationUpdateConnectionDataResolverFnAppSyncFunction8C462233", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Mutation\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"updateConnection\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Mutation"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "MutationdeleteConnectionauth0Function": {"id": "MutationdeleteConnectionauth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationdeleteConnectionauth0Function", "children": {"Templateresolvers--Mutation.deleteConnection.auth.1.req.vtl": {"id": "Templateresolvers--Mutation.deleteConnection.auth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationdeleteConnectionauth0Function/Templateresolvers--Mutation.deleteConnection.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationdeleteConnectionauth0Function/Templateresolvers--Mutation.deleteConnection.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationdeleteConnectionauth0Function/Templateresolvers--Mutation.deleteConnection.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Mutation.deleteConnection.auth.1.res.vtl": {"id": "Templateresolvers--Mutation.deleteConnection.auth.1.res.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationdeleteConnectionauth0Function/Templateresolvers--Mutation.deleteConnection.auth.1.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationdeleteConnectionauth0Function/Templateresolvers--Mutation.deleteConnection.auth.1.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationdeleteConnectionauth0Function/Templateresolvers--Mutation.deleteConnection.auth.1.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationdeleteConnectionauth0Function.AppSyncFunction": {"id": "MutationdeleteConnectionauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationdeleteConnectionauth0Function/MutationdeleteConnectionauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Fn::GetAtt": ["ConnectionDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "MutationdeleteConnectionauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/1f5fed297da9c32ae3af922bf3a38ccf23b956078887d16891ec06c20c64722c.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/05de9eab8d843ef7ef353a7792e3969abe58aae4a1016c0995c6ebddc6f9068f.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationdeleteConnectionpostAuth0Function": {"id": "MutationdeleteConnectionpostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationdeleteConnectionpostAuth0Function", "children": {"Templateresolvers--Mutation.deleteConnection.postAuth.1.req.vtl": {"id": "Templateresolvers--Mutation.deleteConnection.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationdeleteConnectionpostAuth0Function/Templateresolvers--Mutation.deleteConnection.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationdeleteConnectionpostAuth0Function/Templateresolvers--Mutation.deleteConnection.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationdeleteConnectionpostAuth0Function/Templateresolvers--Mutation.deleteConnection.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationdeleteConnectionpostAuth0Function.AppSyncFunction": {"id": "MutationdeleteConnectionpostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationdeleteConnectionpostAuth0Function/MutationdeleteConnectionpostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "MutationdeleteConnectionpostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "MutationDeleteConnectionDataResolverFn": {"id": "MutationDeleteConnectionDataResolverFn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationDeleteConnectionDataResolverFn", "children": {"Templateresolvers--Mutation.deleteConnection.req.vtl": {"id": "Templateresolvers--Mutation.deleteConnection.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationDeleteConnectionDataResolverFn/Templateresolvers--Mutation.deleteConnection.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationDeleteConnectionDataResolverFn/Templateresolvers--Mutation.deleteConnection.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationDeleteConnectionDataResolverFn/Templateresolvers--Mutation.deleteConnection.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Mutation.deleteConnection.res.vtl": {"id": "Templateresolvers--Mutation.deleteConnection.res.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationDeleteConnectionDataResolverFn/Templateresolvers--Mutation.deleteConnection.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationDeleteConnectionDataResolverFn/Templateresolvers--Mutation.deleteConnection.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationDeleteConnectionDataResolverFn/Templateresolvers--Mutation.deleteConnection.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "MutationDeleteConnectionDataResolverFn.AppSyncFunction": {"id": "MutationDeleteConnectionDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationDeleteConnectionDataResolverFn/MutationDeleteConnectionDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Fn::GetAtt": ["ConnectionDataSource", "Name"]}, "functionVersion": "2018-05-29", "name": "MutationDeleteConnectionDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/4f7907d1209a2c9953a0c053df402c634e359546d70c7cc5c2e8e21ea734880f.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a52b72209a9dfa197b5e7367a5c378c5bb86de6e29ddd9e48b49a3fe54b249.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "mutationDeleteConnectionResolver": {"id": "mutationDeleteConnectionResolver", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/mutationDeleteConnectionResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "deleteConnection", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["MutationdeleteConnectionauth0FunctionMutationdeleteConnectionauth0FunctionAppSyncFunction70D5026B", "FunctionId"]}, {"Fn::GetAtt": ["MutationdeleteConnectionpostAuth0FunctionMutationdeleteConnectionpostAuth0FunctionAppSyncFunction78F7B2E6", "FunctionId"]}, {"Fn::GetAtt": ["MutationDeleteConnectionDataResolverFnMutationDeleteConnectionDataResolverFnAppSyncFunction6BDC0894", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Mutation\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"deleteConnection\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Mutation"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "SubscriptiononCreateConnectionauth0Function": {"id": "SubscriptiononCreateConnectionauth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononCreateConnectionauth0Function", "children": {"Templateresolvers--Subscription.onCreateConnection.auth.1.req.vtl": {"id": "Templateresolvers--Subscription.onCreateConnection.auth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononCreateConnectionauth0Function/Templateresolvers--Subscription.onCreateConnection.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononCreateConnectionauth0Function/Templateresolvers--Subscription.onCreateConnection.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononCreateConnectionauth0Function/Templateresolvers--Subscription.onCreateConnection.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononCreateConnectionauth0Function.AppSyncFunction": {"id": "SubscriptiononCreateConnectionauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononCreateConnectionauth0Function/SubscriptiononCreateConnectionauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptiononCreateConnectionauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/4843bc77043eb1b2460e9b1e3df9218ebee2b5c86adc9e9f789a60b4f3a02049.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptiononCreateConnectionpostAuth0Function": {"id": "SubscriptiononCreateConnectionpostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononCreateConnectionpostAuth0Function", "children": {"Templateresolvers--Subscription.onCreateConnection.postAuth.1.req.vtl": {"id": "Templateresolvers--Subscription.onCreateConnection.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononCreateConnectionpostAuth0Function/Templateresolvers--Subscription.onCreateConnection.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononCreateConnectionpostAuth0Function/Templateresolvers--Subscription.onCreateConnection.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononCreateConnectionpostAuth0Function/Templateresolvers--Subscription.onCreateConnection.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononCreateConnectionpostAuth0Function.AppSyncFunction": {"id": "SubscriptiononCreateConnectionpostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononCreateConnectionpostAuth0Function/SubscriptiononCreateConnectionpostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptiononCreateConnectionpostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptionOnCreateConnectionDataResolverFn": {"id": "SubscriptionOnCreateConnectionDataResolverFn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnCreateConnectionDataResolverFn", "children": {"Templateresolvers--Subscription.onCreateConnection.req.vtl": {"id": "Templateresolvers--Subscription.onCreateConnection.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnCreateConnectionDataResolverFn/Templateresolvers--Subscription.onCreateConnection.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnCreateConnectionDataResolverFn/Templateresolvers--Subscription.onCreateConnection.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnCreateConnectionDataResolverFn/Templateresolvers--Subscription.onCreateConnection.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Subscription.onCreateConnection.res.vtl": {"id": "Templateresolvers--Subscription.onCreateConnection.res.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnCreateConnectionDataResolverFn/Templateresolvers--Subscription.onCreateConnection.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnCreateConnectionDataResolverFn/Templateresolvers--Subscription.onCreateConnection.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnCreateConnectionDataResolverFn/Templateresolvers--Subscription.onCreateConnection.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptionOnCreateConnectionDataResolverFn.AppSyncFunction": {"id": "SubscriptionOnCreateConnectionDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnCreateConnectionDataResolverFn/SubscriptionOnCreateConnectionDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptionOnCreateConnectionDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/fe3c43ada4b9d681a5e2312663ef7a73386424d73b73e51f8e2e9d4b50f7c502.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/e0cff47fb007f0bbf2a4e43ca256d6aa7ec109821769fd79fa7c5e83f0e7f9fc.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "subscriptionOnCreateConnectionResolver": {"id": "subscriptionOnCreateConnectionResolver", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/subscriptionOnCreateConnectionResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "onCreateConnection", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["SubscriptiononCreateConnectionauth0FunctionSubscriptiononCreateConnectionauth0FunctionAppSyncFunctionF1660BDA", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptiononCreateConnectionpostAuth0FunctionSubscriptiononCreateConnectionpostAuth0FunctionAppSyncFunction374552A2", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptionOnCreateConnectionDataResolverFnSubscriptionOnCreateConnectionDataResolverFnAppSyncFunction49D15552", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Subscription\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"onCreateConnection\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"NONE\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Subscription"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "SubscriptiononUpdateConnectionauth0Function": {"id": "SubscriptiononUpdateConnectionauth0Function", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononUpdateConnectionauth0Function", "children": {"Templateresolvers--Subscription.onUpdateConnection.auth.1.req.vtl": {"id": "Templateresolvers--Subscription.onUpdateConnection.auth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononUpdateConnectionauth0Function/Templateresolvers--Subscription.onUpdateConnection.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononUpdateConnectionauth0Function/Templateresolvers--Subscription.onUpdateConnection.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononUpdateConnectionauth0Function/Templateresolvers--Subscription.onUpdateConnection.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononUpdateConnectionauth0Function.AppSyncFunction": {"id": "SubscriptiononUpdateConnectionauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononUpdateConnectionauth0Function/SubscriptiononUpdateConnectionauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptiononUpdateConnectionauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/4843bc77043eb1b2460e9b1e3df9218ebee2b5c86adc9e9f789a60b4f3a02049.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptiononUpdateConnectionpostAuth0Function": {"id": "SubscriptiononUpdateConnectionpostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononUpdateConnectionpostAuth0Function", "children": {"Templateresolvers--Subscription.onUpdateConnection.postAuth.1.req.vtl": {"id": "Templateresolvers--Subscription.onUpdateConnection.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononUpdateConnectionpostAuth0Function/Templateresolvers--Subscription.onUpdateConnection.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononUpdateConnectionpostAuth0Function/Templateresolvers--Subscription.onUpdateConnection.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononUpdateConnectionpostAuth0Function/Templateresolvers--Subscription.onUpdateConnection.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononUpdateConnectionpostAuth0Function.AppSyncFunction": {"id": "SubscriptiononUpdateConnectionpostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononUpdateConnectionpostAuth0Function/SubscriptiononUpdateConnectionpostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptiononUpdateConnectionpostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptionOnUpdateConnectionDataResolverFn": {"id": "SubscriptionOnUpdateConnectionDataResolverFn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnUpdateConnectionDataResolverFn", "children": {"Templateresolvers--Subscription.onUpdateConnection.req.vtl": {"id": "Templateresolvers--Subscription.onUpdateConnection.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnUpdateConnectionDataResolverFn/Templateresolvers--Subscription.onUpdateConnection.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnUpdateConnectionDataResolverFn/Templateresolvers--Subscription.onUpdateConnection.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnUpdateConnectionDataResolverFn/Templateresolvers--Subscription.onUpdateConnection.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Subscription.onUpdateConnection.res.vtl": {"id": "Templateresolvers--Subscription.onUpdateConnection.res.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnUpdateConnectionDataResolverFn/Templateresolvers--Subscription.onUpdateConnection.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnUpdateConnectionDataResolverFn/Templateresolvers--Subscription.onUpdateConnection.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnUpdateConnectionDataResolverFn/Templateresolvers--Subscription.onUpdateConnection.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptionOnUpdateConnectionDataResolverFn.AppSyncFunction": {"id": "SubscriptionOnUpdateConnectionDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnUpdateConnectionDataResolverFn/SubscriptionOnUpdateConnectionDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptionOnUpdateConnectionDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/fe3c43ada4b9d681a5e2312663ef7a73386424d73b73e51f8e2e9d4b50f7c502.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/e0cff47fb007f0bbf2a4e43ca256d6aa7ec109821769fd79fa7c5e83f0e7f9fc.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "subscriptionOnUpdateConnectionResolver": {"id": "subscriptionOnUpdateConnectionResolver", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/subscriptionOnUpdateConnectionResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "onUpdateConnection", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["SubscriptiononUpdateConnectionauth0FunctionSubscriptiononUpdateConnectionauth0FunctionAppSyncFunction72B83498", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptiononUpdateConnectionpostAuth0FunctionSubscriptiononUpdateConnectionpostAuth0FunctionAppSyncFunction9A86377C", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptionOnUpdateConnectionDataResolverFnSubscriptionOnUpdateConnectionDataResolverFnAppSyncFunction454D368C", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Subscription\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"onUpdateConnection\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"NONE\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Subscription"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "SubscriptiononDeleteConnectionauth0Function": {"id": "SubscriptiononDeleteConnectionauth0Function", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononDeleteConnectionauth0Function", "children": {"Templateresolvers--Subscription.onDeleteConnection.auth.1.req.vtl": {"id": "Templateresolvers--Subscription.onDeleteConnection.auth.1.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononDeleteConnectionauth0Function/Templateresolvers--Subscription.onDeleteConnection.auth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononDeleteConnectionauth0Function/Templateresolvers--Subscription.onDeleteConnection.auth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononDeleteConnectionauth0Function/Templateresolvers--Subscription.onDeleteConnection.auth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononDeleteConnectionauth0Function.AppSyncFunction": {"id": "SubscriptiononDeleteConnectionauth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononDeleteConnectionauth0Function/SubscriptiononDeleteConnectionauth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptiononDeleteConnectionauth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/4843bc77043eb1b2460e9b1e3df9218ebee2b5c86adc9e9f789a60b4f3a02049.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptiononDeleteConnectionpostAuth0Function": {"id": "SubscriptiononDeleteConnectionpostAuth0Function", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononDeleteConnectionpostAuth0Function", "children": {"Templateresolvers--Subscription.onDeleteConnection.postAuth.1.req.vtl": {"id": "Templateresolvers--Subscription.onDeleteConnection.postAuth.1.req.vtl", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononDeleteConnectionpostAuth0Function/Templateresolvers--Subscription.onDeleteConnection.postAuth.1.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononDeleteConnectionpostAuth0Function/Templateresolvers--Subscription.onDeleteConnection.postAuth.1.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononDeleteConnectionpostAuth0Function/Templateresolvers--Subscription.onDeleteConnection.postAuth.1.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptiononDeleteConnectionpostAuth0Function.AppSyncFunction": {"id": "SubscriptiononDeleteConnectionpostAuth0Function.AppSyncFunction", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononDeleteConnectionpostAuth0Function/SubscriptiononDeleteConnectionpostAuth0Function.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptiononDeleteConnectionpostAuth0Function", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "responseMappingTemplate": "$util.toJson({})"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "SubscriptionOnDeleteConnectionDataResolverFn": {"id": "SubscriptionOnDeleteConnectionDataResolverFn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnDeleteConnectionDataResolverFn", "children": {"Templateresolvers--Subscription.onDeleteConnection.req.vtl": {"id": "Templateresolvers--Subscription.onDeleteConnection.req.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnDeleteConnectionDataResolverFn/Templateresolvers--Subscription.onDeleteConnection.req.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnDeleteConnectionDataResolverFn/Templateresolvers--Subscription.onDeleteConnection.req.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnDeleteConnectionDataResolverFn/Templateresolvers--Subscription.onDeleteConnection.req.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Templateresolvers--Subscription.onDeleteConnection.res.vtl": {"id": "Templateresolvers--Subscription.onDeleteConnection.res.vtl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnDeleteConnectionDataResolverFn/Templateresolvers--Subscription.onDeleteConnection.res.vtl", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnDeleteConnectionDataResolverFn/Templateresolvers--Subscription.onDeleteConnection.res.vtl/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnDeleteConnectionDataResolverFn/Templateresolvers--Subscription.onDeleteConnection.res.vtl/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "SubscriptionOnDeleteConnectionDataResolverFn.AppSyncFunction": {"id": "SubscriptionOnDeleteConnectionDataResolverFn.AppSyncFunction", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnDeleteConnectionDataResolverFn/SubscriptionOnDeleteConnectionDataResolverFn.AppSyncFunction", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::FunctionConfiguration", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "dataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "functionVersion": "2018-05-29", "name": "SubscriptionOnDeleteConnectionDataResolverFn", "requestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/fe3c43ada4b9d681a5e2312663ef7a73386424d73b73e51f8e2e9d4b50f7c502.vtl"}, "responseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/e0cff47fb007f0bbf2a4e43ca256d6aa7ec109821769fd79fa7c5e83f0e7f9fc.vtl"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnFunctionConfiguration", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "subscriptionOnDeleteConnectionResolver": {"id": "subscriptionOnDeleteConnectionResolver", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/subscriptionOnDeleteConnectionResolver", "attributes": {"aws:cdk:cloudformation:type": "AWS::AppSync::Resolver", "aws:cdk:cloudformation:props": {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "fieldName": "onDeleteConnection", "kind": "PIPELINE", "pipelineConfig": {"functions": [{"Fn::GetAtt": ["SubscriptiononDeleteConnectionauth0FunctionSubscriptiononDeleteConnectionauth0FunctionAppSyncFunction5B18C89C", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptiononDeleteConnectionpostAuth0FunctionSubscriptiononDeleteConnectionpostAuth0FunctionAppSyncFunction10BDC99C", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptionOnDeleteConnectionDataResolverFnSubscriptionOnDeleteConnectionDataResolverFnAppSyncFunctionE824ED99", "FunctionId"]}]}, "requestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Subscription\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"onDeleteConnection\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"NONE\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "responseMappingTemplate": "$util.toJson($ctx.prev.result)", "typeName": "Subscription"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.CfnResolver", "version": "2.189.1"}}, "CDKMetadata": {"id": "CDKMetadata", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/CDKMetadata", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}, "Condition": {"id": "Condition", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/CDKMetadata/Condition", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.NestedStack", "version": "2.189.1"}}, "Connection.NestedStack": {"id": "Connection.NestedStack", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection.NestedStack", "children": {"Connection.NestedStackResource": {"id": "Connection.NestedStackResource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection.NestedStack/Connection.NestedStackResource", "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFormation::Stack", "aws:cdk:cloudformation:props": {"parameters": {"DynamoDBModelTableReadIOPS": {"Ref": "DynamoDBModelTableReadIOPS"}, "DynamoDBModelTableWriteIOPS": {"Ref": "DynamoDBModelTableWriteIOPS"}, "DynamoDBBillingMode": {"Ref": "DynamoDBBillingMode"}, "DynamoDBEnablePointInTimeRecovery": {"Ref": "DynamoDBEnablePointInTimeRecovery"}, "DynamoDBEnableServerSideEncryption": {"Ref": "DynamoDBEnableServerSideEncryption"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916OutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableCA8BFB67": {"Fn::GetAtt": ["amplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResource86290833", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName": {"Fn::GetAtt": ["amplifyDataGraphQLAPINONEDS684BF699", "Name"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}}, "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "created-by", "value": "amplify"}], "templateUrl": {"Fn::Join": ["", ["https://s3.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/f289c354e1762737c14d2fdbe5a20884282376df74be46dd29d0c77a01d50c63.json"]]}}}, "constructInfo": {"fqn": "aws-cdk-lib.CfnStack", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "AmplifyCodegenAssets": {"id": "AmplifyCodegenAssets", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets", "children": {"AmplifyCodegenAssetsBucket": {"id": "AmplifyCodegenAssetsBucket", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket", "children": {"Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::S3::<PERSON><PERSON>", "aws:cdk:cloudformation:props": {"corsConfiguration": {"corsRules": [{"allowedHeaders": ["*"], "allowedMethods": ["GET", "HEAD"], "allowedOrigins": [{"Fn::Join": ["", ["https://", {"Ref": "AWS::Region"}, ".console.aws.amazon.com/amplify"]]}]}]}, "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "amplify:friendly-name", "value": "amplifyData"}, {"key": "aws-cdk:auto-delete-objects", "value": "true"}, {"key": "aws-cdk:cr-owned:912d1fbd", "value": "true"}, {"key": "created-by", "value": "amplify"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.CfnBucket", "version": "2.189.1"}}, "Policy": {"id": "Policy", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/Policy", "children": {"Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/Policy/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::S3::BucketPolicy", "aws:cdk:cloudformation:props": {"bucket": {"Ref": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA"}, "policyDocument": {"Statement": [{"Action": ["s3:PutBucketPolicy", "s3:GetBucket*", "s3:List*", "s3:DeleteObject*"], "Effect": "Allow", "Principal": {"AWS": {"Fn::GetAtt": ["CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092", "<PERSON><PERSON>"]}}, "Resource": [{"Fn::GetAtt": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA", "<PERSON><PERSON>"]}, "/*"]]}]}], "Version": "2012-10-17"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.CfnBucketPolicy", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketPolicy", "version": "2.189.1", "metadata": []}}, "AutoDeleteObjectsCustomResource": {"id": "AutoDeleteObjectsCustomResource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/AutoDeleteObjectsCustomResource", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/AutoDeleteObjectsCustomResource/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.CustomResource", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.Bucket", "version": "2.189.1", "metadata": []}}, "AmplifyCodegenAssetsDeployment": {"id": "AmplifyCodegenAssetsDeployment", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment", "children": {"AwsCliLayer": {"id": "AwsCli<PERSON>ayer", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/AwsCliLayer", "children": {"Code": {"id": "Code", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/AwsCliLayer/Code", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/AwsCliLayer/Code/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/AwsCliLayer/Code/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/AwsCliLayer/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::LayerVersion", "aws:cdk:cloudformation:props": {"content": {"s3Bucket": {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "s3Key": "88697bc081d326ba958c89eda97ef89dbab19e586716d7cb9401f041d1b93269.zip"}, "description": "/opt/awscli/aws"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnLayerVersion", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.lambda_layer_awscli.AwsCliLayer", "version": "2.189.1", "metadata": []}}, "CustomResourceHandler": {"id": "CustomResourceHandler", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/CustomResourceHandler", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.SingletonFunction", "version": "2.189.1", "metadata": []}}, "Asset1": {"id": "Asset1", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/Asset1", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/Asset1/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/Asset1/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "CustomResource-1536MiB": {"id": "CustomResource-1536MiB", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/CustomResource-1536MiB", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/CustomResource-1536MiB/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.CustomResource", "version": "2.189.1", "metadata": []}}, "DestinationBucket": {"id": "DestinationBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/DestinationBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_deployment.BucketDeployment", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "L2GraphqlApi": {"id": "L2GraphqlApi", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/L2GraphqlApi", "constructInfo": {"fqn": "aws-cdk-lib.aws_appsync.GraphqlApiBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "@aws-amplify/graphql-api-construct.AmplifyGraphqlApi", "version": "1.20.3"}}, "DynamoDBModelTableReadIOPS": {"id": "DynamoDBModelTableReadIOPS", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/DynamoDBModelTableReadIOPS", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBModelTableWriteIOPS": {"id": "DynamoDBModelTableWriteIOPS", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/DynamoDBModelTableWriteIOPS", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBBillingMode": {"id": "DynamoDBBillingMode", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/DynamoDBBillingMode", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBEnablePointInTimeRecovery": {"id": "DynamoDBEnablePointInTimeRecovery", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/DynamoDBEnablePointInTimeRecovery", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "DynamoDBEnableServerSideEncryption": {"id": "DynamoDBEnableServerSideEncryption", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/DynamoDBEnableServerSideEncryption", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "LatestNodeRuntimeMap": {"id": "LatestNodeRuntimeMap", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/LatestNodeRuntimeMap", "constructInfo": {"fqn": "aws-cdk-lib.CfnMapping", "version": "2.189.1"}}, "Custom::S3AutoDeleteObjectsCustomResourceProvider": {"id": "Custom::S3AutoDeleteObjectsCustomResourceProvider", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/Custom::S3AutoDeleteObjectsCustomResourceProvider", "children": {"Staging": {"id": "Staging", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/Custom::S3AutoDeleteObjectsCustomResourceProvider/Staging", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "Role": {"id": "Role", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/Custom::S3AutoDeleteObjectsCustomResourceProvider/Role", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}, "Handler": {"id": "Handler", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/Custom::S3AutoDeleteObjectsCustomResourceProvider/Handler", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.CustomResourceProviderBase", "version": "2.189.1"}}, "Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB": {"id": "Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB", "children": {"ServiceRole": {"id": "ServiceRole", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/ServiceRole", "children": {"ImportServiceRole": {"id": "ImportServiceRole", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/ServiceRole/ImportServiceRole", "constructInfo": {"fqn": "aws-cdk-lib.Resource", "version": "2.189.1", "metadata": []}}, "Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/ServiceRole/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Role", "aws:cdk:cloudformation:props": {"assumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "managedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}], "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "created-by", "value": "amplify"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnRole", "version": "2.189.1"}}, "DefaultPolicy": {"id": "DefaultPolicy", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/ServiceRole/DefaultPolicy", "children": {"Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/ServiceRole/DefaultPolicy/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::IAM::Policy", "aws:cdk:cloudformation:props": {"policyDocument": {"Statement": [{"Action": ["s3:GetObject*", "s3:GetBucket*", "s3:List*"], "Effect": "Allow", "Resource": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":s3:::", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}]]}, {"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":s3:::", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/*"]]}]}, {"Action": ["s3:GetObject*", "s3:GetBucket*", "s3:List*", "s3:DeleteObject*", "s3:PutObject", "s3:PutObjectLegalHold", "s3:PutObjectRetention", "s3:PutObjectTagging", "s3:PutObjectVersionTagging", "s3:Abort*"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA", "<PERSON><PERSON>"]}, "/*"]]}]}, {"Action": ["s3:GetObject*", "s3:GetBucket*", "s3:List*", "s3:DeleteObject*", "s3:PutObject", "s3:PutObjectLegalHold", "s3:PutObjectRetention", "s3:PutObjectTagging", "s3:PutObjectVersionTagging", "s3:Abort*"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, "/*"]]}]}], "Version": "2012-10-17"}, "policyName": "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleDefaultPolicyFF1C635B", "roles": [{"Ref": "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleA41FC8C2"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.CfnPolicy", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Policy", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_iam.Role", "version": "2.189.1", "metadata": []}}, "Code": {"id": "Code", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/Code", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/Code/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/Code/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::Function", "aws:cdk:cloudformation:props": {"code": {"s3Bucket": {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "s3Key": "4fe0aba5e672b596d0f72505a9eec502f98d46906bb30fae2511fbdc1df4956f.zip"}, "environment": {"variables": {"AWS_CA_BUNDLE": "/etc/pki/ca-trust/extracted/pem/tls-ca-bundle.pem"}}, "handler": "index.handler", "layers": [{"Ref": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentAwsCliLayerE322F905"}], "memorySize": 1536, "role": {"Fn::GetAtt": ["CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleA41FC8C2", "<PERSON><PERSON>"]}, "runtime": "python3.11", "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "created-by", "value": "amplify"}], "timeout": 900}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnFunction", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.Function", "version": "2.189.1", "metadata": []}}, "modelIntrospectionSchemaBucket": {"id": "modelIntrospectionSchemaBucket", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucket", "children": {"Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucket/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::S3::<PERSON><PERSON>", "aws:cdk:cloudformation:props": {"tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "aws-cdk:auto-delete-objects", "value": "true"}, {"key": "aws-cdk:cr-owned:3d1d070e", "value": "true"}, {"key": "created-by", "value": "amplify"}]}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.CfnBucket", "version": "2.189.1"}}, "Policy": {"id": "Policy", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucket/Policy", "children": {"Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucket/Policy/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::S3::BucketPolicy", "aws:cdk:cloudformation:props": {"bucket": {"Ref": "modelIntrospectionSchemaBucketF566B665"}, "policyDocument": {"Statement": [{"Action": "s3:*", "Condition": {"Bool": {"aws:SecureTransport": "false"}}, "Effect": "<PERSON><PERSON>", "Principal": {"AWS": "*"}, "Resource": [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, "/*"]]}]}, {"Action": ["s3:PutBucketPolicy", "s3:GetBucket*", "s3:List*", "s3:DeleteObject*"], "Effect": "Allow", "Principal": {"AWS": {"Fn::GetAtt": ["CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092", "<PERSON><PERSON>"]}}, "Resource": [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, "/*"]]}]}], "Version": "2012-10-17"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.CfnBucketPolicy", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketPolicy", "version": "2.189.1", "metadata": []}}, "AutoDeleteObjectsCustomResource": {"id": "AutoDeleteObjectsCustomResource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucket/AutoDeleteObjectsCustomResource", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucket/AutoDeleteObjectsCustomResource/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.CustomResource", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.Bucket", "version": "2.189.1", "metadata": []}}, "modelIntrospectionSchemaBucketDeployment": {"id": "modelIntrospectionSchemaBucketDeployment", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucketDeployment", "children": {"AwsCliLayer": {"id": "AwsCli<PERSON>ayer", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucketDeployment/AwsCliLayer", "children": {"Code": {"id": "Code", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucketDeployment/AwsCliLayer/Code", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucketDeployment/AwsCliLayer/Code/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucketDeployment/AwsCliLayer/Code/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucketDeployment/AwsCliLayer/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::Lambda::LayerVersion", "aws:cdk:cloudformation:props": {"content": {"s3Bucket": {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "s3Key": "88697bc081d326ba958c89eda97ef89dbab19e586716d7cb9401f041d1b93269.zip"}, "description": "/opt/awscli/aws"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.CfnLayerVersion", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.lambda_layer_awscli.AwsCliLayer", "version": "2.189.1", "metadata": []}}, "CustomResourceHandler": {"id": "CustomResourceHandler", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucketDeployment/CustomResourceHandler", "constructInfo": {"fqn": "aws-cdk-lib.aws_lambda.SingletonFunction", "version": "2.189.1", "metadata": []}}, "Asset1": {"id": "Asset1", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucketDeployment/Asset1", "children": {"Stage": {"id": "Stage", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucketDeployment/Asset1/Stage", "constructInfo": {"fqn": "aws-cdk-lib.AssetStaging", "version": "2.189.1"}}, "AssetBucket": {"id": "AssetBucket", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucketDeployment/Asset1/AssetBucket", "constructInfo": {"fqn": "aws-cdk-lib.aws_s3.BucketBase", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_assets.Asset", "version": "2.189.1"}}, "CustomResource-1536MiB": {"id": "CustomResource-1536MiB", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucketDeployment/CustomResource-1536MiB", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucketDeployment/CustomResource-1536MiB/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.CustomResource", "version": "2.189.1", "metadata": []}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_s3_deployment.BucketDeployment", "version": "2.189.1"}}, "AMPLIFY_DATA_GRAPHQL_ENDPOINTParameter": {"id": "AMPLIFY_DATA_GRAPHQL_ENDPOINTParameter", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/AMPLIFY_DATA_GRAPHQL_ENDPOINTParameter", "children": {"Resource": {"id": "Resource", "path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/AMPLIFY_DATA_GRAPHQL_ENDPOINTParameter/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::SSM::Parameter", "aws:cdk:cloudformation:props": {"name": "/amplify/resource_reference/RootsConnect/nkannaiyan-sandbox-183ad0d0d2/AMPLIFY_DATA_GRAPHQL_ENDPOINT", "tags": {"amplify:deployment-type": "sandbox", "created-by": "amplify"}, "type": "String", "value": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "GraphQLUrl"]}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ssm.CfnParameter", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ssm.StringParameter", "version": "2.189.1", "metadata": []}}, "AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_BUCKET_NAMEParameter": {"id": "AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_BUCKET_NAMEParameter", "path": "amplify-RootsConnect-<PERSON><PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_BUCKET_NAMEParameter", "children": {"Resource": {"id": "Resource", "path": "amplify-RootsConnect-<PERSON><PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_BUCKET_NAMEParameter/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::SSM::Parameter", "aws:cdk:cloudformation:props": {"name": "/amplify/resource_reference/RootsConnect/nkan<PERSON><PERSON>-sandbox-183ad0d0d2/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_BUCKET_NAME", "tags": {"amplify:deployment-type": "sandbox", "created-by": "amplify"}, "type": "String", "value": {"Ref": "modelIntrospectionSchemaBucketF566B665"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ssm.CfnParameter", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ssm.StringParameter", "version": "2.189.1", "metadata": []}}, "AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_KEYParameter": {"id": "AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_KEYParameter", "path": "amplify-RootsConnect-<PERSON><PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_KEYParameter", "children": {"Resource": {"id": "Resource", "path": "amplify-RootsConnect-<PERSON><PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_KEYParameter/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::SSM::Parameter", "aws:cdk:cloudformation:props": {"name": "/amplify/resource_reference/RootsConnect/nkan<PERSON><PERSON>-sandbox-183ad0d0d2/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_KEY", "tags": {"amplify:deployment-type": "sandbox", "created-by": "amplify"}, "type": "String", "value": "modelIntrospectionSchema.json"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ssm.CfnParameter", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ssm.StringParameter", "version": "2.189.1", "metadata": []}}, "AMPLIFY_DATA_DEFAULT_NAMEParameter": {"id": "AMPLIFY_DATA_DEFAULT_NAMEParameter", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/AMPLIFY_DATA_DEFAULT_NAMEParameter", "children": {"Resource": {"id": "Resource", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/AMPLIFY_DATA_DEFAULT_NAMEParameter/Resource", "attributes": {"aws:cdk:cloudformation:type": "AWS::SSM::Parameter", "aws:cdk:cloudformation:props": {"name": "/amplify/resource_reference/RootsConnect/nkannaiyan-sandbox-183ad0d0d2/AMPLIFY_DATA_DEFAULT_NAME", "tags": {"amplify:deployment-type": "sandbox", "created-by": "amplify"}, "type": "String", "value": "amplifyData"}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ssm.CfnParameter", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.aws_ssm.StringParameter", "version": "2.189.1", "metadata": []}}, "CDKMetadata": {"id": "CDKMetadata", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/CDKMetadata", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}, "Condition": {"id": "Condition", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/CDKMetadata/Condition", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolA19BA9C3Ref": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolA19BA9C3Ref", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolA19BA9C3Ref", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef": {"id": "reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/reference-to-amplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": {"id": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2GraphQLUrl": {"id": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2GraphQLUrl", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2GraphQLUrl", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentCustomResource1536MiB60279CB2DestinationBucketArn": {"id": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentCustomResource1536MiB60279CB2DestinationBucketArn", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentCustomResource1536MiB60279CB2DestinationBucketArn", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPIDefaultApiKey9D3276BBApiKey": {"id": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPIDefaultApiKey9D3276BBApiKey", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPIDefaultApiKey9D3276BBApiKey", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.NestedStack", "version": "2.189.1"}}, "data.NestedStack": {"id": "data.NestedStack", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data.NestedStack", "children": {"data.NestedStackResource": {"id": "data.NestedStackResource", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data.NestedStack/data.NestedStackResource", "attributes": {"aws:cdk:cloudformation:type": "AWS::CloudFormation::Stack", "aws:cdk:cloudformation:props": {"parameters": {"referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolA19BA9C3Ref": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolA19BA9C3Ref"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"]}}, "tags": [{"key": "amplify:deployment-type", "value": "sandbox"}, {"key": "created-by", "value": "amplify"}], "templateUrl": {"Fn::Join": ["", ["https://s3.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/c2ca336958b66a2a0753208aa10e2d92bfba90a54a9a11e43669c9f644bcd708.json"]]}}}, "constructInfo": {"fqn": "aws-cdk-lib.CfnStack", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "awsAppsyncApiId": {"id": "awsAppsyncApiId", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/awsAppsyncApiId", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "awsAppsyncApiEndpoint": {"id": "awsAppsyncApiEndpoint", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/awsAppsyncApiEndpoint", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "awsAppsyncAuthenticationType": {"id": "awsAppsyncAuthenticationType", "path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/awsAppsyncAuthenticationType", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "awsAppsyncRegion": {"id": "awsAppsyncRegion", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/awsAppsyncRegion", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "amplifyApiModelSchemaS3Uri": {"id": "amplifyApiModelSchemaS3Uri", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/amplifyApiModelSchemaS3Uri", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "awsAppsyncApiKey": {"id": "awsAppsyncApiKey", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/awsAppsyncApiKey", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "awsAppsyncAdditionalAuthenticationTypes": {"id": "awsAppsyncAdditionalAuthenticationTypes", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/awsAppsyncAdditionalAuthenticationTypes", "constructInfo": {"fqn": "aws-cdk-lib.CfnOutput", "version": "2.189.1"}}, "CDKMetadata": {"id": "CDKMetadata", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/CDKMetadata", "children": {"Default": {"id": "<PERSON><PERSON><PERSON>", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/CDKMetadata/Default", "constructInfo": {"fqn": "aws-cdk-lib.CfnResource", "version": "2.189.1"}}, "Condition": {"id": "Condition", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/CDKMetadata/Condition", "constructInfo": {"fqn": "aws-cdk-lib.CfnCondition", "version": "2.189.1"}}}, "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}, "BootstrapVersion": {"id": "BootstrapVersion", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/BootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnParameter", "version": "2.189.1"}}, "CheckBootstrapVersion": {"id": "CheckBootstrapVersion", "path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/CheckBootstrapVersion", "constructInfo": {"fqn": "aws-cdk-lib.CfnRule", "version": "2.189.1"}}}, "constructInfo": {"fqn": "aws-cdk-lib.<PERSON><PERSON>", "version": "2.189.1"}}, "Tree": {"id": "Tree", "path": "Tree", "constructInfo": {"fqn": "constructs.Construct", "version": "10.4.2"}}}, "constructInfo": {"fqn": "aws-cdk-lib.App", "version": "2.189.1"}}}