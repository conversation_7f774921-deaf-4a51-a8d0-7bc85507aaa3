{"version": "41.0.0", "files": {"23df4a10f7743e90a710888ea87205f23d214aad2b34e617221bec49c1d24ebc": {"displayName": "data/amplifyData/AmplifyTableManager/TableManagerCustomProvider/framework-onEvent/Code", "source": {"path": "asset.23df4a10f7743e90a710888ea87205f23d214aad2b34e617221bec49c1d24ebc", "packaging": "zip"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "23df4a10f7743e90a710888ea87205f23d214aad2b34e617221bec49c1d24ebc.zip", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "6f1b01c4b2882f213b91241f489e2393bc199b215b8b1695ea62da2156719905": {"displayName": "data/amplifyData/UserProfile/QuerygetUserProfileauth0Function/Templateresolvers--Query.getUserProfile.auth.1.req.vtl", "source": {"path": "asset.6f1b01c4b2882f213b91241f489e2393bc199b215b8b1695ea62da2156719905.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "6f1b01c4b2882f213b91241f489e2393bc199b215b8b1695ea62da2156719905.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77": {"displayName": "data/amplifyData/UserProfile/QuerygetUserProfilepostAuth0Function/Templateresolvers--Query.getUserProfile.postAuth.1.req.vtl", "source": {"path": "asset.c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "08f4d557693d96c1a4efba0f9dc91330e4b19772fd5477c156468843e3d9cb5e": {"displayName": "data/amplifyData/UserProfile/QueryGetUserProfileDataResolverFn/Templateresolvers--Query.getUserProfile.req.vtl", "source": {"path": "asset.08f4d557693d96c1a4efba0f9dc91330e4b19772fd5477c156468843e3d9cb5e.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "08f4d557693d96c1a4efba0f9dc91330e4b19772fd5477c156468843e3d9cb5e.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "4c6a2d29f01c6091bd1d9afe16e5849d456c96f17c3b215938c8067399532719": {"displayName": "data/amplifyData/UserProfile/QueryGetUserProfileDataResolverFn/Templateresolvers--Query.getUserProfile.res.vtl", "source": {"path": "asset.4c6a2d29f01c6091bd1d9afe16e5849d456c96f17c3b215938c8067399532719.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "4c6a2d29f01c6091bd1d9afe16e5849d456c96f17c3b215938c8067399532719.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "9fcbe070ecd3023c5bf5b966fa9584757db9762eef123bad0820bd87591b2174": {"displayName": "data/amplifyData/UserProfile/QueryListUserProfilesDataResolverFn/Templateresolvers--Query.listUserProfiles.req.vtl", "source": {"path": "asset.9fcbe070ecd3023c5bf5b966fa9584757db9762eef123bad0820bd87591b2174.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "9fcbe070ecd3023c5bf5b966fa9584757db9762eef123bad0820bd87591b2174.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "cc01911d0269d4080ea57505dc445dfc315ef7ad85d3d9d4ea1357858bff451d": {"displayName": "data/amplifyData/UserProfile/QueryListUserProfilesDataResolverFn/Templateresolvers--Query.listUserProfiles.res.vtl", "source": {"path": "asset.cc01911d0269d4080ea57505dc445dfc315ef7ad85d3d9d4ea1357858bff451d.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "cc01911d0269d4080ea57505dc445dfc315ef7ad85d3d9d4ea1357858bff451d.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "a183ddccbd956316c38ef97177b8f088ef0826f62023323f5ae6053d348ccffc": {"displayName": "data/amplifyData/UserProfile/MutationcreateUserProfileinit0Function/Templateresolvers--Mutation.createUserProfile.init.1.req.vtl", "source": {"path": "asset.a183ddccbd956316c38ef97177b8f088ef0826f62023323f5ae6053d348ccffc.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "a183ddccbd956316c38ef97177b8f088ef0826f62023323f5ae6053d348ccffc.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "0fd7df0cf2df9d019fe04dcacd94e8367f5308ad7b02bdbd92d91ed5ac30a6a0": {"displayName": "data/amplifyData/UserProfile/MutationcreateUserProfileinit1Function/Templateresolvers--Mutation.createUserProfile.init.2.req.vtl", "source": {"path": "asset.0fd7df0cf2df9d019fe04dcacd94e8367f5308ad7b02bdbd92d91ed5ac30a6a0.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "0fd7df0cf2df9d019fe04dcacd94e8367f5308ad7b02bdbd92d91ed5ac30a6a0.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "9e2e2c2ea03e23aab3194d48982c3069731b170aca59702ffdc812cd2aed7521": {"displayName": "data/amplifyData/UserProfile/MutationcreateUserProfileauth0Function/Templateresolvers--Mutation.createUserProfile.auth.1.req.vtl", "source": {"path": "asset.9e2e2c2ea03e23aab3194d48982c3069731b170aca59702ffdc812cd2aed7521.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "9e2e2c2ea03e23aab3194d48982c3069731b170aca59702ffdc812cd2aed7521.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "5591194755d6c82901ab61fc77e043768b6b515f63c3a032cf0b8d285671c1ea": {"displayName": "data/amplifyData/UserProfile/MutationCreateUserProfileDataResolverFn/Templateresolvers--Mutation.createUserProfile.req.vtl", "source": {"path": "asset.5591194755d6c82901ab61fc77e043768b6b515f63c3a032cf0b8d285671c1ea.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "5591194755d6c82901ab61fc77e043768b6b515f63c3a032cf0b8d285671c1ea.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "f4a52b72209a9dfa197b5e7367a5c378c5bb86de6e29ddd9e48b49a3fe54b249": {"displayName": "data/amplifyData/UserProfile/MutationCreateUserProfileDataResolverFn/Templateresolvers--Mutation.createUserProfile.res.vtl", "source": {"path": "asset.f4a52b72209a9dfa197b5e7367a5c378c5bb86de6e29ddd9e48b49a3fe54b249.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "f4a52b72209a9dfa197b5e7367a5c378c5bb86de6e29ddd9e48b49a3fe54b249.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "06db846fd14e6fc371f22b12b5545ba8e2dbfeda85d8c8d586c71c282166657b": {"displayName": "data/amplifyData/UserProfile/MutationupdateUserProfileinit0Function/Templateresolvers--Mutation.updateUserProfile.init.1.req.vtl", "source": {"path": "asset.06db846fd14e6fc371f22b12b5545ba8e2dbfeda85d8c8d586c71c282166657b.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "06db846fd14e6fc371f22b12b5545ba8e2dbfeda85d8c8d586c71c282166657b.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "1f5fed297da9c32ae3af922bf3a38ccf23b956078887d16891ec06c20c64722c": {"displayName": "data/amplifyData/UserProfile/MutationupdateUserProfileauth0Function/Templateresolvers--Mutation.updateUserProfile.auth.1.req.vtl", "source": {"path": "asset.1f5fed297da9c32ae3af922bf3a38ccf23b956078887d16891ec06c20c64722c.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "1f5fed297da9c32ae3af922bf3a38ccf23b956078887d16891ec06c20c64722c.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "80680c06297d8e4f4943fe8215d637144ea24bf53ec05404e841459b81fc3063": {"displayName": "data/amplifyData/UserProfile/MutationupdateUserProfileauth0Function/Templateresolvers--Mutation.updateUserProfile.auth.1.res.vtl", "source": {"path": "asset.80680c06297d8e4f4943fe8215d637144ea24bf53ec05404e841459b81fc3063.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "80680c06297d8e4f4943fe8215d637144ea24bf53ec05404e841459b81fc3063.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "474bf0776ec2164a13191d1a0a9e057154931e4918fea5086f49850d02a5371b": {"displayName": "data/amplifyData/UserProfile/MutationUpdateUserProfileDataResolverFn/Templateresolvers--Mutation.updateUserProfile.req.vtl", "source": {"path": "asset.474bf0776ec2164a13191d1a0a9e057154931e4918fea5086f49850d02a5371b.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "474bf0776ec2164a13191d1a0a9e057154931e4918fea5086f49850d02a5371b.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "6257bfd1ef2992bd01df135516c0df15c5ff692f426e0c71c93960be8f8c81df": {"displayName": "data/amplifyData/UserProfile/MutationdeleteUserProfileauth0Function/Templateresolvers--Mutation.deleteUserProfile.auth.1.res.vtl", "source": {"path": "asset.6257bfd1ef2992bd01df135516c0df15c5ff692f426e0c71c93960be8f8c81df.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "6257bfd1ef2992bd01df135516c0df15c5ff692f426e0c71c93960be8f8c81df.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "4f7907d1209a2c9953a0c053df402c634e359546d70c7cc5c2e8e21ea734880f": {"displayName": "data/amplifyData/UserProfile/MutationDeleteUserProfileDataResolverFn/Templateresolvers--Mutation.deleteUserProfile.req.vtl", "source": {"path": "asset.4f7907d1209a2c9953a0c053df402c634e359546d70c7cc5c2e8e21ea734880f.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "4f7907d1209a2c9953a0c053df402c634e359546d70c7cc5c2e8e21ea734880f.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "f4a6f34d8595215fbaec56ec853de13cd014504c4abe3ff7457af53316ba2779": {"displayName": "data/amplifyData/UserProfile/SubscriptiononCreateUserProfileauth0Function/Templateresolvers--Subscription.onCreateUserProfile.auth.1.req.vtl", "source": {"path": "asset.f4a6f34d8595215fbaec56ec853de13cd014504c4abe3ff7457af53316ba2779.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "f4a6f34d8595215fbaec56ec853de13cd014504c4abe3ff7457af53316ba2779.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "fe3c43ada4b9d681a5e2312663ef7a73386424d73b73e51f8e2e9d4b50f7c502": {"displayName": "data/amplifyData/UserProfile/SubscriptionOnCreateUserProfileDataResolverFn/Templateresolvers--Subscription.onCreateUserProfile.req.vtl", "source": {"path": "asset.fe3c43ada4b9d681a5e2312663ef7a73386424d73b73e51f8e2e9d4b50f7c502.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "fe3c43ada4b9d681a5e2312663ef7a73386424d73b73e51f8e2e9d4b50f7c502.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "e0cff47fb007f0bbf2a4e43ca256d6aa7ec109821769fd79fa7c5e83f0e7f9fc": {"displayName": "data/amplifyData/UserProfile/SubscriptionOnCreateUserProfileDataResolverFn/Templateresolvers--Subscription.onCreateUserProfile.res.vtl", "source": {"path": "asset.e0cff47fb007f0bbf2a4e43ca256d6aa7ec109821769fd79fa7c5e83f0e7f9fc.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "e0cff47fb007f0bbf2a4e43ca256d6aa7ec109821769fd79fa7c5e83f0e7f9fc.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "6160f4b79b6a93016faacf67febf2c5ed13e1085b161db7c2d9ec18efa09197a": {"displayName": "data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestauth0Function/Templateresolvers--Mutation.createConnectionRequest.auth.1.req.vtl", "source": {"path": "asset.6160f4b79b6a93016faacf67febf2c5ed13e1085b161db7c2d9ec18efa09197a.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "6160f4b79b6a93016faacf67febf2c5ed13e1085b161db7c2d9ec18efa09197a.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "06d7f74c3fda50f2346af03e3b3fc281f9cee655ea01b997d3e8b240b0eefd5d": {"displayName": "data/amplifyData/ConnectionRequest/MutationCreateConnectionRequestDataResolverFn/Templateresolvers--Mutation.createConnectionRequest.req.vtl", "source": {"path": "asset.06d7f74c3fda50f2346af03e3b3fc281f9cee655ea01b997d3e8b240b0eefd5d.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "06d7f74c3fda50f2346af03e3b3fc281f9cee655ea01b997d3e8b240b0eefd5d.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "48ab3887a29703dea320f36be3db1a3e35f7f87be239a424744d3fd05edd9e0d": {"displayName": "data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestauth0Function/Templateresolvers--Mutation.updateConnectionRequest.auth.1.res.vtl", "source": {"path": "asset.48ab3887a29703dea320f36be3db1a3e35f7f87be239a424744d3fd05edd9e0d.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "48ab3887a29703dea320f36be3db1a3e35f7f87be239a424744d3fd05edd9e0d.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "05de9eab8d843ef7ef353a7792e3969abe58aae4a1016c0995c6ebddc6f9068f": {"displayName": "data/amplifyData/ConnectionRequest/MutationdeleteConnectionRequestauth0Function/Templateresolvers--Mutation.deleteConnectionRequest.auth.1.res.vtl", "source": {"path": "asset.05de9eab8d843ef7ef353a7792e3969abe58aae4a1016c0995c6ebddc6f9068f.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "05de9eab8d843ef7ef353a7792e3969abe58aae4a1016c0995c6ebddc6f9068f.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "6677b1986afebbf1f4026c57099890e32ab4e450e422c5e3c930b085a73b6b60": {"displayName": "data/amplifyData/Connection/QuerygetConnectionauth0Function/Templateresolvers--Query.getConnection.auth.1.req.vtl", "source": {"path": "asset.6677b1986afebbf1f4026c57099890e32ab4e450e422c5e3c930b085a73b6b60.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "6677b1986afebbf1f4026c57099890e32ab4e450e422c5e3c930b085a73b6b60.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "747896db78e02d287e1575351ce928b81bd0fd0aab966b2ffc55a576f37cf80e": {"displayName": "data/amplifyData/Connection/MutationcreateConnectioninit1Function/Templateresolvers--Mutation.createConnection.init.2.req.vtl", "source": {"path": "asset.747896db78e02d287e1575351ce928b81bd0fd0aab966b2ffc55a576f37cf80e.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "747896db78e02d287e1575351ce928b81bd0fd0aab966b2ffc55a576f37cf80e.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "eb2c75f91fb7c102ca64406094e96480b71004b54804dc990a5fdb8903ef2bca": {"displayName": "data/amplifyData/Connection/MutationcreateConnectionauth0Function/Templateresolvers--Mutation.createConnection.auth.1.req.vtl", "source": {"path": "asset.eb2c75f91fb7c102ca64406094e96480b71004b54804dc990a5fdb8903ef2bca.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "eb2c75f91fb7c102ca64406094e96480b71004b54804dc990a5fdb8903ef2bca.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "94c06debde92fed2e2c6ad051e85a3e024e4712d18d6ee6fb1cd56cda8155001": {"displayName": "data/amplifyData/Connection/MutationCreateConnectionDataResolverFn/Templateresolvers--Mutation.createConnection.req.vtl", "source": {"path": "asset.94c06debde92fed2e2c6ad051e85a3e024e4712d18d6ee6fb1cd56cda8155001.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "94c06debde92fed2e2c6ad051e85a3e024e4712d18d6ee6fb1cd56cda8155001.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "fe139862c8f0faccbaea00d381f2ee946ff166b4a3ff39527a46c14858c9626f": {"displayName": "data/amplifyData/Connection/MutationupdateConnectionauth0Function/Templateresolvers--Mutation.updateConnection.auth.1.res.vtl", "source": {"path": "asset.fe139862c8f0faccbaea00d381f2ee946ff166b4a3ff39527a46c14858c9626f.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "fe139862c8f0faccbaea00d381f2ee946ff166b4a3ff39527a46c14858c9626f.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "4843bc77043eb1b2460e9b1e3df9218ebee2b5c86adc9e9f789a60b4f3a02049": {"displayName": "data/amplifyData/Connection/SubscriptiononCreateConnectionauth0Function/Templateresolvers--Subscription.onCreateConnection.auth.1.req.vtl", "source": {"path": "asset.4843bc77043eb1b2460e9b1e3df9218ebee2b5c86adc9e9f789a60b4f3a02049.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "4843bc77043eb1b2460e9b1e3df9218ebee2b5c86adc9e9f789a60b4f3a02049.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "041534e5fd916595f752318f161512d7c7f83b9f2cf32d0f0be381c12253ff68": {"displayName": "data/amplifyData/UserProfile/UserProfileOwnerDataResolverFn/Templateresolvers--UserProfile.owner.req.vtl", "source": {"path": "asset.041534e5fd916595f752318f161512d7c7f83b9f2cf32d0f0be381c12253ff68.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "041534e5fd916595f752318f161512d7c7f83b9f2cf32d0f0be381c12253ff68.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "064303962e481067b44300212516363b99aaee539b6bafaf756fdd83ff0b60f0": {"displayName": "data/amplifyData/UserProfile/UserProfileOwnerDataResolverFn/Templateresolvers--UserProfile.owner.res.vtl", "source": {"path": "asset.064303962e481067b44300212516363b99aaee539b6bafaf756fdd83ff0b60f0.vtl", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "064303962e481067b44300212516363b99aaee539b6bafaf756fdd83ff0b60f0.vtl", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6": {"displayName": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/Custom::S3AutoDeleteObjectsCustomResourceProvider Code", "source": {"path": "asset.faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6", "packaging": "zip"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6.zip", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "88697bc081d326ba958c89eda97ef89dbab19e586716d7cb9401f041d1b93269": {"displayName": "data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/AwsCliLayer/Code", "source": {"path": "asset.88697bc081d326ba958c89eda97ef89dbab19e586716d7cb9401f041d1b93269.zip", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "88697bc081d326ba958c89eda97ef89dbab19e586716d7cb9401f041d1b93269.zip", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "4fe0aba5e672b596d0f72505a9eec502f98d46906bb30fae2511fbdc1df4956f": {"displayName": "data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/Code", "source": {"path": "asset.4fe0aba5e672b596d0f72505a9eec502f98d46906bb30fae2511fbdc1df4956f", "packaging": "zip"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "4fe0aba5e672b596d0f72505a9eec502f98d46906bb30fae2511fbdc1df4956f.zip", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "c85cfebe588b3c5c552ff4b95814b8112db97e42ba3b8da09f1fda5c1752bbd6": {"displayName": "data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/Asset1", "source": {"path": "asset.c85cfebe588b3c5c552ff4b95814b8112db97e42ba3b8da09f1fda5c1752bbd6", "packaging": "zip"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "c85cfebe588b3c5c552ff4b95814b8112db97e42ba3b8da09f1fda5c1752bbd6.zip", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "87fdbda2f9b5fbad8a8f0ee6c140fac9e53bda05220c7b8fc3db0af5eabebf94": {"displayName": "data/modelIntrospectionSchemaBucketDeployment/Asset1", "source": {"path": "asset.87fdbda2f9b5fbad8a8f0ee6c140fac9e53bda05220c7b8fc3db0af5eabebf94", "packaging": "zip"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "87fdbda2f9b5fbad8a8f0ee6c140fac9e53bda05220c7b8fc3db0af5eabebf94.zip", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "6614acf21da877481fe2119444d7f87a06f93eb4fb0b938caaa77f109f816e43": {"displayName": "data/amplifyData/GraphQLAPI/schema", "source": {"path": "asset.6614acf21da877481fe2119444d7f87a06f93eb4fb0b938caaa77f109f816e43.graphql", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "6614acf21da877481fe2119444d7f87a06f93eb4fb0b938caaa77f109f816e43.graphql", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "c66d0248273a35b9d9d4fae4883e6c8f7c0ab44e0096721ecde31e426d93d530": {"displayName": "auth Nested Stack Template", "source": {"path": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2authE93823C0.nested.template.json", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "c66d0248273a35b9d9d4fae4883e6c8f7c0ab44e0096721ecde31e426d93d530.json", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "dd481eb31c5caa20140d9d422348dbdab98d913903c1e918eafdb4531b770f5b": {"displayName": "data/amplifyData/AmplifyTableManager Nested Stack Template", "source": {"path": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManager64DE7FE5.nested.template.json", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "dd481eb31c5caa20140d9d422348dbdab98d913903c1e918eafdb4531b770f5b.json", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "ad5b215a6c21dd88a6acdc93b3d118a21f09feba2cf9df993ce9e4b1a429aad4": {"displayName": "data/amplifyData/UserProfile Nested Stack Template", "source": {"path": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataUserProfile8FBF43E3.nested.template.json", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "ad5b215a6c21dd88a6acdc93b3d118a21f09feba2cf9df993ce9e4b1a429aad4.json", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "efec2b1b3967e7146f0451460b9881ff86048864fe6b8861196a490317bade5c": {"displayName": "data/amplifyData/ConnectionRequest Nested Stack Template", "source": {"path": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataConnectionRequest74E4C2AA.nested.template.json", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "efec2b1b3967e7146f0451460b9881ff86048864fe6b8861196a490317bade5c.json", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "f289c354e1762737c14d2fdbe5a20884282376df74be46dd29d0c77a01d50c63": {"displayName": "data/amplifyData/Connection Nested Stack Template", "source": {"path": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataConnection840F0D33.nested.template.json", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "f289c354e1762737c14d2fdbe5a20884282376df74be46dd29d0c77a01d50c63.json", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "2caf29a80348df64d03c3b87d414374c7b83f40949cea1694242d8edee91fed4": {"displayName": "data Nested Stack Template", "source": {"path": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2data9B47D0E3.nested.template.json", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "2caf29a80348df64d03c3b87d414374c7b83f40949cea1694242d8edee91fed4.json", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}, "61bc3308ebd4bebfe97eac41ab7b36eb45abdc351574c25f6fcf480c69ce0c97": {"displayName": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2 Template", "source": {"path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2.template.json", "packaging": "file"}, "destinations": {"current_account-current_region": {"bucketName": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}", "objectKey": "61bc3308ebd4bebfe97eac41ab7b36eb45abdc351574c25f6fcf480c69ce0c97.json", "assumeRoleArn": "arn:${AWS::Partition}:iam::${AWS::AccountId}:role/cdk-hnb659fds-file-publishing-role-${AWS::AccountId}-${AWS::Region}"}}}}, "dockerImages": {}}