{"version": 1, "models": {"UserProfile": {"name": "UserProfile", "fields": {"id": {"name": "id", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "userId": {"name": "userId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "email": {"name": "email", "isArray": false, "type": "AWSEmail", "isRequired": true, "attributes": []}, "firstName": {"name": "firstName", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "lastName": {"name": "lastName", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "displayName": {"name": "displayName", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "profilePicture": {"name": "profilePicture", "isArray": false, "type": "AWSURL", "isRequired": false, "attributes": []}, "latitude": {"name": "latitude", "isArray": false, "type": "Float", "isRequired": false, "attributes": []}, "longitude": {"name": "longitude", "isArray": false, "type": "Float", "isRequired": false, "attributes": []}, "city": {"name": "city", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "state": {"name": "state", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "country": {"name": "country", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "isLocationVisible": {"name": "isLocationVisible", "isArray": false, "type": "Boolean", "isRequired": false, "attributes": []}, "phoneNumbers": {"name": "phoneNumbers", "isArray": true, "type": "String", "isRequired": false, "attributes": [], "isArrayNullable": true}, "whatsappNumber": {"name": "whatsappNumber", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "facebookProfile": {"name": "facebookProfile", "isArray": false, "type": "AWSURL", "isRequired": false, "attributes": []}, "telegramHandle": {"name": "telegramHandle", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "additionalEmails": {"name": "additionalEmails", "isArray": true, "type": "AWSEmail", "isRequired": false, "attributes": [], "isArrayNullable": true}, "isProfilePublic": {"name": "isProfilePublic", "isArray": false, "type": "Boolean", "isRequired": false, "attributes": []}, "shareContactInfo": {"name": "shareContactInfo", "isArray": false, "type": "Boolean", "isRequired": false, "attributes": []}, "allowDirectContact": {"name": "allowDirectContact", "isArray": false, "type": "Boolean", "isRequired": false, "attributes": []}, "interests": {"name": "interests", "isArray": true, "type": "String", "isRequired": false, "attributes": [], "isArrayNullable": true}, "profession": {"name": "profession", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "bio": {"name": "bio", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "lastActiveAt": {"name": "lastActiveAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}}, "syncable": true, "pluralName": "UserProfiles", "attributes": [{"type": "model", "properties": {}}, {"type": "auth", "properties": {"rules": [{"provider": "userPools", "ownerField": "owner", "allow": "owner", "identityClaim": "cognito:username", "operations": ["create", "update", "delete", "read"]}, {"allow": "private", "operations": ["read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": false, "primaryKeyFieldName": "id", "sortKeyFieldNames": []}}, "ConnectionRequest": {"name": "ConnectionRequest", "fields": {"id": {"name": "id", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "fromUserId": {"name": "fromUserId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "toUserId": {"name": "toUserId", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "status": {"name": "status", "isArray": false, "type": {"enum": "ConnectionRequestStatus"}, "isRequired": false, "attributes": []}, "message": {"name": "message", "isArray": false, "type": "String", "isRequired": false, "attributes": []}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}}, "syncable": true, "pluralName": "ConnectionRequests", "attributes": [{"type": "model", "properties": {}}, {"type": "auth", "properties": {"rules": [{"provider": "userPools", "ownerField": "owner", "allow": "owner", "operations": ["create", "read", "update"], "identityClaim": "cognito:username"}, {"allow": "private", "operations": ["read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": false, "primaryKeyFieldName": "id", "sortKeyFieldNames": []}}, "Connection": {"name": "Connection", "fields": {"id": {"name": "id", "isArray": false, "type": "ID", "isRequired": true, "attributes": []}, "user1Id": {"name": "user1Id", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "user2Id": {"name": "user2Id", "isArray": false, "type": "String", "isRequired": true, "attributes": []}, "connectedAt": {"name": "connectedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": []}, "sharedContactInfo": {"name": "sharedContactInfo", "isArray": false, "type": "Boolean", "isRequired": false, "attributes": []}, "createdAt": {"name": "createdAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}, "updatedAt": {"name": "updatedAt", "isArray": false, "type": "AWSDateTime", "isRequired": false, "attributes": [], "isReadOnly": true}}, "syncable": true, "pluralName": "Connections", "attributes": [{"type": "model", "properties": {}}, {"type": "auth", "properties": {"rules": [{"allow": "private", "operations": ["read"]}]}}], "primaryKeyInfo": {"isCustomPrimaryKey": false, "primaryKeyFieldName": "id", "sortKeyFieldNames": []}}}, "enums": {"ConnectionRequestStatus": {"name": "ConnectionRequestStatus", "values": ["pending", "accepted", "declined"]}}, "nonModels": {}}