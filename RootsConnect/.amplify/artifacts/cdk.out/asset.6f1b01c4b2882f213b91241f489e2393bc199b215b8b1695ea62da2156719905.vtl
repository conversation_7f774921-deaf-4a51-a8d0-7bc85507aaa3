## [Start] Authorization Steps. **
$util.qr($ctx.stash.put("hasAuth", true))
#set( $isAuthorized = false )
#set( $primaryFieldMap = {} )
#if( $util.authType() == "API Key Authorization" )

#end
#if( $util.authType() == "IAM Authorization" )
  #if( $util.authType() == "IAM Authorization" && $util.isNull($ctx.identity.cognitoIdentityPoolId) && $util.isNull($ctx.identity.cognitoIdentityId) )
    $util.qr($ctx.stash.put("hasAuth", true))
    #set( $isAuthorized = true )
  #else
$util.unauthorized()
  #end
#end
#if( $util.authType() == "User Pool Authorization" )
  #set( $isAuthorized = true )
  #if( !$isAuthorized )
    #set( $authFilter = [] )
    #set( $ownerClaim0 = $util.defaultIfNull($ctx.identity.claims.get("sub"), null) )
    #set( $currentClaim1 = $util.defaultIfNull($ctx.identity.claims.get("username"), $util.defaultIfNull($ctx.identity.claims.get("cognito:username"), null)) )
    #if( !$util.isNull($ownerClaim0) && !$util.isNull($currentClaim1) )
      #set( $ownerClaim0 = "$ownerClaim0::$currentClaim1" )
      #if( !$util.isNull($ownerClaim0) )
        $util.qr($authFilter.add({"owner": { "eq": $ownerClaim0 }}))
      #end
    #end
    #set( $role0_0 = $util.defaultIfNull($ctx.identity.claims.get("sub"), null) )
    #if( !$util.isNull($role0_0) )
      $util.qr($authFilter.add({"owner": { "eq": $role0_0 }}))
    #end
    #set( $role0_1 = $util.defaultIfNull($ctx.identity.claims.get("username"), $util.defaultIfNull($ctx.identity.claims.get("cognito:username"), null)) )
    #if( !$util.isNull($role0_1) )
      $util.qr($authFilter.add({"owner": { "eq": $role0_1 }}))
    #end
    #if( !$authFilter.isEmpty() )
      $util.qr($ctx.stash.put("authFilter", { "or": $authFilter }))
    #end
  #end
#end
#if( !$isAuthorized && $util.isNull($ctx.stash.authFilter) )
$util.unauthorized()
#end
$util.toJson({"version":"2018-05-29","payload":{}})
## [End] Authorization Steps. **