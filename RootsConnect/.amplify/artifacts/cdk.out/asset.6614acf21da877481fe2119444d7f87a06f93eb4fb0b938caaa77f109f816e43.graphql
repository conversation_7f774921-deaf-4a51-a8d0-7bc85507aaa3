type UserProfile @aws_iam @aws_cognito_user_pools {
  userId: String!
  email: AWSEmail!
  firstName: String
  lastName: String
  displayName: String
  profilePicture: AWSURL
  latitude: Float
  longitude: Float
  city: String
  state: String
  country: String
  isLocationVisible: Boolean
  phoneNumbers: [String]
  whatsappNumber: String
  facebookProfile: AWSURL
  telegramHandle: String
  additionalEmails: [AWSEmail]
  isProfilePublic: Boolean
  shareContactInfo: Boolean
  allowDirectContact: Boolean
  interests: [String]
  profession: String
  bio: String
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
  lastActiveAt: AWSDateTime
  id: ID!
  owner: String
}

type ConnectionRequest @aws_iam @aws_cognito_user_pools {
  fromUserId: String!
  toUserId: String!
  status: ConnectionRequestStatus
  message: String
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
  id: ID!
  owner: String
}

type Connection @aws_iam @aws_cognito_user_pools {
  user1Id: String!
  user2Id: String!
  connectedAt: AWSDateTime
  sharedContactInfo: Boolean
  id: ID!
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

enum ConnectionRequestStatus {
  pending
  accepted
  declined
}

input AmplifyAIConversationTurnErrorInput {
  errorType: String!
  message: String!
}

input ModelStringInput {
  ne: String
  eq: String
  le: String
  lt: String
  ge: String
  gt: String
  contains: String
  notContains: String
  between: [String]
  beginsWith: String
  attributeExists: Boolean
  attributeType: ModelAttributeTypes
  size: ModelSizeInput
}

input ModelIntInput {
  ne: Int
  eq: Int
  le: Int
  lt: Int
  ge: Int
  gt: Int
  between: [Int]
  attributeExists: Boolean
  attributeType: ModelAttributeTypes
}

input ModelFloatInput {
  ne: Float
  eq: Float
  le: Float
  lt: Float
  ge: Float
  gt: Float
  between: [Float]
  attributeExists: Boolean
  attributeType: ModelAttributeTypes
}

input ModelBooleanInput {
  ne: Boolean
  eq: Boolean
  attributeExists: Boolean
  attributeType: ModelAttributeTypes
}

input ModelIDInput {
  ne: ID
  eq: ID
  le: ID
  lt: ID
  ge: ID
  gt: ID
  contains: ID
  notContains: ID
  between: [ID]
  beginsWith: ID
  attributeExists: Boolean
  attributeType: ModelAttributeTypes
  size: ModelSizeInput
}

input ModelSubscriptionStringInput {
  ne: String
  eq: String
  le: String
  lt: String
  ge: String
  gt: String
  contains: String
  notContains: String
  between: [String]
  beginsWith: String
  in: [String]
  notIn: [String]
}

input ModelSubscriptionIntInput {
  ne: Int
  eq: Int
  le: Int
  lt: Int
  ge: Int
  gt: Int
  between: [Int]
  in: [Int]
  notIn: [Int]
}

input ModelSubscriptionFloatInput {
  ne: Float
  eq: Float
  le: Float
  lt: Float
  ge: Float
  gt: Float
  between: [Float]
  in: [Float]
  notIn: [Float]
}

input ModelSubscriptionBooleanInput {
  ne: Boolean
  eq: Boolean
}

input ModelSubscriptionIDInput {
  ne: ID
  eq: ID
  le: ID
  lt: ID
  ge: ID
  gt: ID
  contains: ID
  notContains: ID
  between: [ID]
  beginsWith: ID
  in: [ID]
  notIn: [ID]
}

enum ModelAttributeTypes {
  binary
  binarySet
  bool
  list
  map
  number
  numberSet
  string
  stringSet
  _null
}

input ModelSizeInput {
  ne: Int
  eq: Int
  le: Int
  lt: Int
  ge: Int
  gt: Int
  between: [Int]
}

enum ModelSortDirection {
  ASC
  DESC
}

type ModelUserProfileConnection @aws_iam @aws_cognito_user_pools {
  items: [UserProfile]!
  nextToken: String
}

input ModelUserProfileFilterInput {
  userId: ModelStringInput
  email: ModelStringInput
  firstName: ModelStringInput
  lastName: ModelStringInput
  displayName: ModelStringInput
  profilePicture: ModelStringInput
  latitude: ModelFloatInput
  longitude: ModelFloatInput
  city: ModelStringInput
  state: ModelStringInput
  country: ModelStringInput
  isLocationVisible: ModelBooleanInput
  phoneNumbers: ModelStringInput
  whatsappNumber: ModelStringInput
  facebookProfile: ModelStringInput
  telegramHandle: ModelStringInput
  additionalEmails: ModelStringInput
  isProfilePublic: ModelBooleanInput
  shareContactInfo: ModelBooleanInput
  allowDirectContact: ModelBooleanInput
  interests: ModelStringInput
  profession: ModelStringInput
  bio: ModelStringInput
  createdAt: ModelStringInput
  updatedAt: ModelStringInput
  lastActiveAt: ModelStringInput
  id: ModelIDInput
  and: [ModelUserProfileFilterInput]
  or: [ModelUserProfileFilterInput]
  not: ModelUserProfileFilterInput
  owner: ModelStringInput
}

type Query {
  getUserProfile(id: ID!): UserProfile @aws_iam @aws_cognito_user_pools
  listUserProfiles(filter: ModelUserProfileFilterInput, limit: Int, nextToken: String): ModelUserProfileConnection @aws_iam @aws_cognito_user_pools
  getConnectionRequest(id: ID!): ConnectionRequest @aws_iam @aws_cognito_user_pools
  listConnectionRequests(filter: ModelConnectionRequestFilterInput, limit: Int, nextToken: String): ModelConnectionRequestConnection @aws_iam @aws_cognito_user_pools
  getConnection(id: ID!): Connection @aws_iam @aws_cognito_user_pools
  listConnections(filter: ModelConnectionFilterInput, limit: Int, nextToken: String): ModelConnectionConnection @aws_iam @aws_cognito_user_pools
}

input ModelUserProfileConditionInput {
  userId: ModelStringInput
  email: ModelStringInput
  firstName: ModelStringInput
  lastName: ModelStringInput
  displayName: ModelStringInput
  profilePicture: ModelStringInput
  latitude: ModelFloatInput
  longitude: ModelFloatInput
  city: ModelStringInput
  state: ModelStringInput
  country: ModelStringInput
  isLocationVisible: ModelBooleanInput
  phoneNumbers: ModelStringInput
  whatsappNumber: ModelStringInput
  facebookProfile: ModelStringInput
  telegramHandle: ModelStringInput
  additionalEmails: ModelStringInput
  isProfilePublic: ModelBooleanInput
  shareContactInfo: ModelBooleanInput
  allowDirectContact: ModelBooleanInput
  interests: ModelStringInput
  profession: ModelStringInput
  bio: ModelStringInput
  createdAt: ModelStringInput
  updatedAt: ModelStringInput
  lastActiveAt: ModelStringInput
  and: [ModelUserProfileConditionInput]
  or: [ModelUserProfileConditionInput]
  not: ModelUserProfileConditionInput
  owner: ModelStringInput
}

input CreateUserProfileInput {
  userId: String!
  email: AWSEmail!
  firstName: String
  lastName: String
  displayName: String
  profilePicture: AWSURL
  latitude: Float
  longitude: Float
  city: String
  state: String
  country: String
  isLocationVisible: Boolean
  phoneNumbers: [String]
  whatsappNumber: String
  facebookProfile: AWSURL
  telegramHandle: String
  additionalEmails: [AWSEmail]
  isProfilePublic: Boolean
  shareContactInfo: Boolean
  allowDirectContact: Boolean
  interests: [String]
  profession: String
  bio: String
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
  lastActiveAt: AWSDateTime
  id: ID
}

input UpdateUserProfileInput {
  userId: String
  email: AWSEmail
  firstName: String
  lastName: String
  displayName: String
  profilePicture: AWSURL
  latitude: Float
  longitude: Float
  city: String
  state: String
  country: String
  isLocationVisible: Boolean
  phoneNumbers: [String]
  whatsappNumber: String
  facebookProfile: AWSURL
  telegramHandle: String
  additionalEmails: [AWSEmail]
  isProfilePublic: Boolean
  shareContactInfo: Boolean
  allowDirectContact: Boolean
  interests: [String]
  profession: String
  bio: String
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
  lastActiveAt: AWSDateTime
  id: ID!
}

input DeleteUserProfileInput {
  id: ID!
}

type Mutation {
  createUserProfile(input: CreateUserProfileInput!, condition: ModelUserProfileConditionInput): UserProfile @aws_iam @aws_cognito_user_pools
  updateUserProfile(input: UpdateUserProfileInput!, condition: ModelUserProfileConditionInput): UserProfile @aws_iam @aws_cognito_user_pools
  deleteUserProfile(input: DeleteUserProfileInput!, condition: ModelUserProfileConditionInput): UserProfile @aws_iam @aws_cognito_user_pools
  createConnectionRequest(input: CreateConnectionRequestInput!, condition: ModelConnectionRequestConditionInput): ConnectionRequest @aws_iam @aws_cognito_user_pools
  updateConnectionRequest(input: UpdateConnectionRequestInput!, condition: ModelConnectionRequestConditionInput): ConnectionRequest @aws_iam @aws_cognito_user_pools
  deleteConnectionRequest(input: DeleteConnectionRequestInput!, condition: ModelConnectionRequestConditionInput): ConnectionRequest @aws_iam @aws_cognito_user_pools
  createConnection(input: CreateConnectionInput!, condition: ModelConnectionConditionInput): Connection @aws_iam @aws_cognito_user_pools
  updateConnection(input: UpdateConnectionInput!, condition: ModelConnectionConditionInput): Connection @aws_iam @aws_cognito_user_pools
  deleteConnection(input: DeleteConnectionInput!, condition: ModelConnectionConditionInput): Connection @aws_iam @aws_cognito_user_pools
}

input ModelSubscriptionUserProfileFilterInput {
  userId: ModelSubscriptionStringInput
  email: ModelSubscriptionStringInput
  firstName: ModelSubscriptionStringInput
  lastName: ModelSubscriptionStringInput
  displayName: ModelSubscriptionStringInput
  profilePicture: ModelSubscriptionStringInput
  latitude: ModelSubscriptionFloatInput
  longitude: ModelSubscriptionFloatInput
  city: ModelSubscriptionStringInput
  state: ModelSubscriptionStringInput
  country: ModelSubscriptionStringInput
  isLocationVisible: ModelSubscriptionBooleanInput
  phoneNumbers: ModelSubscriptionStringInput
  whatsappNumber: ModelSubscriptionStringInput
  facebookProfile: ModelSubscriptionStringInput
  telegramHandle: ModelSubscriptionStringInput
  additionalEmails: ModelSubscriptionStringInput
  isProfilePublic: ModelSubscriptionBooleanInput
  shareContactInfo: ModelSubscriptionBooleanInput
  allowDirectContact: ModelSubscriptionBooleanInput
  interests: ModelSubscriptionStringInput
  profession: ModelSubscriptionStringInput
  bio: ModelSubscriptionStringInput
  createdAt: ModelSubscriptionStringInput
  updatedAt: ModelSubscriptionStringInput
  lastActiveAt: ModelSubscriptionStringInput
  id: ModelSubscriptionIDInput
  and: [ModelSubscriptionUserProfileFilterInput]
  or: [ModelSubscriptionUserProfileFilterInput]
  owner: ModelStringInput
}

type Subscription {
  onCreateUserProfile(filter: ModelSubscriptionUserProfileFilterInput, owner: String): UserProfile @aws_subscribe(mutations: ["createUserProfile"]) @aws_iam @aws_cognito_user_pools
  onUpdateUserProfile(filter: ModelSubscriptionUserProfileFilterInput, owner: String): UserProfile @aws_subscribe(mutations: ["updateUserProfile"]) @aws_iam @aws_cognito_user_pools
  onDeleteUserProfile(filter: ModelSubscriptionUserProfileFilterInput, owner: String): UserProfile @aws_subscribe(mutations: ["deleteUserProfile"]) @aws_iam @aws_cognito_user_pools
  onCreateConnectionRequest(filter: ModelSubscriptionConnectionRequestFilterInput, owner: String): ConnectionRequest @aws_subscribe(mutations: ["createConnectionRequest"]) @aws_iam @aws_cognito_user_pools
  onUpdateConnectionRequest(filter: ModelSubscriptionConnectionRequestFilterInput, owner: String): ConnectionRequest @aws_subscribe(mutations: ["updateConnectionRequest"]) @aws_iam @aws_cognito_user_pools
  onDeleteConnectionRequest(filter: ModelSubscriptionConnectionRequestFilterInput, owner: String): ConnectionRequest @aws_subscribe(mutations: ["deleteConnectionRequest"]) @aws_iam @aws_cognito_user_pools
  onCreateConnection(filter: ModelSubscriptionConnectionFilterInput): Connection @aws_subscribe(mutations: ["createConnection"]) @aws_iam @aws_cognito_user_pools
  onUpdateConnection(filter: ModelSubscriptionConnectionFilterInput): Connection @aws_subscribe(mutations: ["updateConnection"]) @aws_iam @aws_cognito_user_pools
  onDeleteConnection(filter: ModelSubscriptionConnectionFilterInput): Connection @aws_subscribe(mutations: ["deleteConnection"]) @aws_iam @aws_cognito_user_pools
}

type ModelConnectionRequestConnection @aws_iam @aws_cognito_user_pools {
  items: [ConnectionRequest]!
  nextToken: String
}

input ModelConnectionRequestStatusInput {
  eq: ConnectionRequestStatus
  ne: ConnectionRequestStatus
}

input ModelConnectionRequestFilterInput {
  fromUserId: ModelStringInput
  toUserId: ModelStringInput
  status: ModelConnectionRequestStatusInput
  message: ModelStringInput
  createdAt: ModelStringInput
  updatedAt: ModelStringInput
  id: ModelIDInput
  and: [ModelConnectionRequestFilterInput]
  or: [ModelConnectionRequestFilterInput]
  not: ModelConnectionRequestFilterInput
  owner: ModelStringInput
}

input ModelConnectionRequestConditionInput {
  fromUserId: ModelStringInput
  toUserId: ModelStringInput
  status: ModelConnectionRequestStatusInput
  message: ModelStringInput
  createdAt: ModelStringInput
  updatedAt: ModelStringInput
  and: [ModelConnectionRequestConditionInput]
  or: [ModelConnectionRequestConditionInput]
  not: ModelConnectionRequestConditionInput
  owner: ModelStringInput
}

input CreateConnectionRequestInput {
  fromUserId: String!
  toUserId: String!
  status: ConnectionRequestStatus
  message: String
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
  id: ID
}

input UpdateConnectionRequestInput {
  fromUserId: String
  toUserId: String
  status: ConnectionRequestStatus
  message: String
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
  id: ID!
}

input DeleteConnectionRequestInput {
  id: ID!
}

input ModelSubscriptionConnectionRequestFilterInput {
  fromUserId: ModelSubscriptionStringInput
  toUserId: ModelSubscriptionStringInput
  status: ModelSubscriptionStringInput
  message: ModelSubscriptionStringInput
  createdAt: ModelSubscriptionStringInput
  updatedAt: ModelSubscriptionStringInput
  id: ModelSubscriptionIDInput
  and: [ModelSubscriptionConnectionRequestFilterInput]
  or: [ModelSubscriptionConnectionRequestFilterInput]
  owner: ModelStringInput
}

type ModelConnectionConnection @aws_iam @aws_cognito_user_pools {
  items: [Connection]!
  nextToken: String
}

input ModelConnectionFilterInput {
  user1Id: ModelStringInput
  user2Id: ModelStringInput
  connectedAt: ModelStringInput
  sharedContactInfo: ModelBooleanInput
  id: ModelIDInput
  createdAt: ModelStringInput
  updatedAt: ModelStringInput
  and: [ModelConnectionFilterInput]
  or: [ModelConnectionFilterInput]
  not: ModelConnectionFilterInput
}

input ModelConnectionConditionInput {
  user1Id: ModelStringInput
  user2Id: ModelStringInput
  connectedAt: ModelStringInput
  sharedContactInfo: ModelBooleanInput
  and: [ModelConnectionConditionInput]
  or: [ModelConnectionConditionInput]
  not: ModelConnectionConditionInput
  createdAt: ModelStringInput
  updatedAt: ModelStringInput
}

input CreateConnectionInput {
  user1Id: String!
  user2Id: String!
  connectedAt: AWSDateTime
  sharedContactInfo: Boolean
  id: ID
}

input UpdateConnectionInput {
  user1Id: String
  user2Id: String
  connectedAt: AWSDateTime
  sharedContactInfo: Boolean
  id: ID!
}

input DeleteConnectionInput {
  id: ID!
}

input ModelSubscriptionConnectionFilterInput {
  user1Id: ModelSubscriptionStringInput
  user2Id: ModelSubscriptionStringInput
  connectedAt: ModelSubscriptionStringInput
  sharedContactInfo: ModelSubscriptionBooleanInput
  id: ModelSubscriptionIDInput
  createdAt: ModelSubscriptionStringInput
  updatedAt: ModelSubscriptionStringInput
  and: [ModelSubscriptionConnectionFilterInput]
  or: [ModelSubscriptionConnectionFilterInput]
}
