## [Start] Authorization Steps. **
$util.qr($ctx.stash.put("hasAuth", true))
#set( $inputFields = $util.parseJson($util.toJson($ctx.args.input.keySet())) )
#set( $isAuthorized = false )
#set( $allowedFields = [] )
#if( $util.authType() == "API Key Authorization" )
$util.unauthorized()
#end
#if( $util.authType() == "IAM Authorization" )
  #if( $util.authType() == "IAM Authorization" && $util.isNull($ctx.identity.cognitoIdentityPoolId) && $util.isNull($ctx.identity.cognitoIdentityId) )
    $util.qr($ctx.stash.put("hasAuth", true))
    #set( $isAuthorized = true )
  #else
$util.unauthorized()
  #end
#end
#if( $util.authType() == "User Pool Authorization" )
  #set( $ownerEntity0 = $util.defaultIfNull($ctx.args.input.owner, null) )
  #set( $ownerClaim0 = $util.defaultIfNull($ctx.identity.claims.get("sub"), null) )
  #set( $currentClaim1 = $util.defaultIfNull($ctx.identity.claims.get("username"), $util.defaultIfNull($ctx.identity.claims.get("cognito:username"), null)) )
  #if( !$util.isNull($ownerClaim0) && !$util.isNull($currentClaim1) )
    #set( $ownerClaim0 = "$ownerClaim0::$currentClaim1" )
    #if( $isAuthorized && $util.isNull($ownerEntity0) && !$ctx.args.input.containsKey("owner") )
      $util.qr($ctx.args.input.put("owner", $ownerClaim0))
    #end
    #if( !$isAuthorized )
      #set( $ownerClaimsList0 = [] )
      $util.qr($ownerClaimsList0.add($util.defaultIfNull($ctx.identity.claims.get("sub"), null)))
      $util.qr($ownerClaimsList0.add($util.defaultIfNull($ctx.identity.claims.get("username"), $util.defaultIfNull($ctx.identity.claims.get("cognito:username"), null))))
      #set( $ownerAllowedFields0 = ["fromUserId","toUserId","status","message","createdAt","updatedAt"] )
      #set( $isAuthorizedOnAllFields0 = true )
      #if( $ownerClaim0 == $ownerEntity0 || $ownerClaimsList0.contains($ownerEntity0) )
        #if( $isAuthorizedOnAllFields0 )
          #set( $isAuthorized = true )
        #else
          $util.qr($allowedFields.addAll($ownerAllowedFields0))
        #end
      #end
      #if( $util.isNull($ownerEntity0) && !$ctx.args.input.containsKey("owner") )
        $util.qr($ctx.args.input.put("owner", $ownerClaim0))
        #if( $isAuthorizedOnAllFields0 )
          #set( $isAuthorized = true )
        #else
          $util.qr($allowedFields.addAll($ownerAllowedFields0))
        #end
      #end
    #end
  #end
#end
#if( !$isAuthorized && $allowedFields.isEmpty() )
$util.unauthorized()
#end
#if( !$isAuthorized )
  #set( $deniedFields = $util.list.copyAndRemoveAll($inputFields, $allowedFields) )
  #if( $deniedFields.size() > 0 )
    $util.error("Unauthorized on ${deniedFields}", "Unauthorized")
  #end
#end
$util.toJson({"version":"2018-05-29","payload":{}})
## [End] Authorization Steps. **