{"Parameters": {"DynamoDBModelTableReadIOPS": {"Type": "Number", "Default": 5, "Description": "The number of read IOPS the table should support."}, "DynamoDBModelTableWriteIOPS": {"Type": "Number", "Default": 5, "Description": "The number of write IOPS the table should support."}, "DynamoDBBillingMode": {"Type": "String", "Default": "PAY_PER_REQUEST", "AllowedValues": ["PAY_PER_REQUEST", "PROVISIONED"], "Description": "Configure @model types to create DynamoDB tables with PAY_PER_REQUEST or PROVISIONED billing modes."}, "DynamoDBEnablePointInTimeRecovery": {"Type": "String", "Default": "false", "AllowedValues": ["true", "false"], "Description": "Whether to enable Point in Time Recovery on the table."}, "DynamoDBEnableServerSideEncryption": {"Type": "String", "Default": "true", "AllowedValues": ["true", "false"], "Description": "Enable server side encryption powered by KMS."}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916OutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableCA8BFB67": {"Type": "String"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": {"Type": "String"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName": {"Type": "String"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref": {"Type": "String"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef": {"Type": "String"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef": {"Type": "String"}}, "Conditions": {"HasEnvironmentParameter": {"Fn::Not": [{"Fn::Equals": ["NONE", "NONE"]}]}, "ShouldUsePayPerRequestBilling": {"Fn::Equals": [{"Ref": "DynamoDBBillingMode"}, "PAY_PER_REQUEST"]}, "ShouldUsePointInTimeRecovery": {"Fn::Equals": [{"Ref": "DynamoDBEnablePointInTimeRecovery"}, "true"]}, "CDKMetadataAvailable": {"Fn::Or": [{"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "af-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-3"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-south-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-3"]}]}, {"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-4"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ca-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ca-west-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "cn-north-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "cn-northwest-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-central-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-north-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-south-2"]}]}, {"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-3"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "il-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "me-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "me-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "sa-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-east-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-west-1"]}]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-west-2"]}]}}, "Resources": {"ConnectionTable": {"Type": "Custom::AmplifyDynamoDBTable", "Properties": {"ServiceToken": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916OutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableCA8BFB67"}, "tableName": {"Fn::Join": ["", ["Connection-", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "-NONE"]]}, "attributeDefinitions": [{"attributeName": "id", "attributeType": "S"}], "keySchema": [{"attributeName": "id", "keyType": "HASH"}], "provisionedThroughput": {"Fn::If": ["ShouldUsePayPerRequestBilling", {"Ref": "AWS::NoValue"}, {"ReadCapacityUnits": {"Ref": "DynamoDBModelTableReadIOPS"}, "WriteCapacityUnits": {"Ref": "DynamoDBModelTableWriteIOPS"}}]}, "sseSpecification": {"sseEnabled": false}, "streamSpecification": {"streamViewType": "NEW_AND_OLD_IMAGES"}, "deletionProtectionEnabled": false, "allowDestructiveGraphqlSchemaUpdates": true, "replaceTableUponGsiUpdate": true, "pointInTimeRecoverySpecification": {"Fn::If": ["ShouldUsePointInTimeRecovery", {"PointInTimeRecoveryEnabled": true}, {"Ref": "AWS::NoValue"}]}, "billingMode": {"Fn::If": ["ShouldUsePayPerRequestBilling", "PAY_PER_REQUEST", {"Ref": "AWS::NoValue"}]}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/ConnectionTable/Default/Default"}}, "ConnectionIAMRole812E1EC8": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "appsync.amazonaws.com"}}], "Version": "2012-10-17"}, "Policies": [{"PolicyDocument": {"Statement": [{"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:PutItem", "dynamodb:DeleteItem", "dynamodb:GetItem", "dynamodb:<PERSON><PERSON>", "dynamodb:Query", "dynamodb:UpdateItem", "dynamodb:ConditionCheckItem", "dynamodb:DescribeTable", "dynamodb:GetRecords", "dynamodb:GetShardIterator"], "Effect": "Allow", "Resource": [{"Fn::Sub": ["arn:${AWS::Partition}:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${tablename}", {"tablename": {"Fn::Join": ["", ["Connection-", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "-NONE"]]}}]}, {"Fn::Sub": ["arn:${AWS::Partition}:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${tablename}/*", {"tablename": {"Fn::Join": ["", ["Connection-", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "-NONE"]]}}]}]}], "Version": "2012-10-17"}, "PolicyName": "DynamoDBAccess"}], "RoleName": {"Fn::Join": ["", ["ConnectionIAMRole1fac14-", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "-NONE"]]}, "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "amplify:friendly-name", "Value": "amplifyData"}, {"Key": "created-by", "Value": "amplify"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/ConnectionIAMRole/Resource"}}, "ConnectionDataSource": {"Type": "AWS::AppSync::DataSource", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DynamoDBConfig": {"AwsRegion": {"Ref": "AWS::Region"}, "TableName": {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionTable", "TableArn"]}]}]}]}]}}, "Name": "ConnectionTable", "ServiceRoleArn": {"Fn::GetAtt": ["ConnectionIAMRole812E1EC8", "<PERSON><PERSON>"]}, "Type": "AMAZON_DYNAMODB"}, "DependsOn": ["ConnectionIAMRole812E1EC8"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/ConnectionDataSource/Resource"}}, "QuerygetConnectionauth0FunctionQuerygetConnectionauth0FunctionAppSyncFunctionFCA57863": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "QuerygetConnectionauth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/6677b1986afebbf1f4026c57099890e32ab4e450e422c5e3c930b085a73b6b60.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerygetConnectionauth0Function/QuerygetConnectionauth0Function.AppSyncFunction"}}, "QuerygetConnectionpostAuth0FunctionQuerygetConnectionpostAuth0FunctionAppSyncFunction50FB355D": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "QuerygetConnectionpostAuth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerygetConnectionpostAuth0Function/QuerygetConnectionpostAuth0Function.AppSyncFunction"}}, "QueryGetConnectionDataResolverFnQueryGetConnectionDataResolverFnAppSyncFunction7F579500": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Fn::GetAtt": ["ConnectionDataSource", "Name"]}, "FunctionVersion": "2018-05-29", "Name": "QueryGetConnectionDataResolverFn", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/08f4d557693d96c1a4efba0f9dc91330e4b19772fd5477c156468843e3d9cb5e.vtl"}, "ResponseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/4c6a2d29f01c6091bd1d9afe16e5849d456c96f17c3b215938c8067399532719.vtl"}}, "DependsOn": ["ConnectionDataSource"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/QueryGetConnectionDataResolverFn/QueryGetConnectionDataResolverFn.AppSyncFunction"}}, "GetConnectionResolver": {"Type": "AWS::AppSync::Resolver", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "FieldName": "getConnection", "Kind": "PIPELINE", "PipelineConfig": {"Functions": [{"Fn::GetAtt": ["QuerygetConnectionauth0FunctionQuerygetConnectionauth0FunctionAppSyncFunctionFCA57863", "FunctionId"]}, {"Fn::GetAtt": ["QuerygetConnectionpostAuth0FunctionQuerygetConnectionpostAuth0FunctionAppSyncFunction50FB355D", "FunctionId"]}, {"Fn::GetAtt": ["QueryGetConnectionDataResolverFnQueryGetConnectionDataResolverFnAppSyncFunction7F579500", "FunctionId"]}]}, "RequestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Query\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"getConnection\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "ResponseMappingTemplate": "$util.toJson($ctx.prev.result)", "TypeName": "Query"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/queryGetConnectionResolver"}}, "QuerylistConnectionsauth0FunctionQuerylistConnectionsauth0FunctionAppSyncFunction35EE0264": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "QuerylistConnectionsauth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/6677b1986afebbf1f4026c57099890e32ab4e450e422c5e3c930b085a73b6b60.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerylistConnectionsauth0Function/QuerylistConnectionsauth0Function.AppSyncFunction"}}, "QuerylistConnectionspostAuth0FunctionQuerylistConnectionspostAuth0FunctionAppSyncFunction8F17C74E": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "QuerylistConnectionspostAuth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/QuerylistConnectionspostAuth0Function/QuerylistConnectionspostAuth0Function.AppSyncFunction"}}, "QueryListConnectionsDataResolverFnQueryListConnectionsDataResolverFnAppSyncFunction904DCC89": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Fn::GetAtt": ["ConnectionDataSource", "Name"]}, "FunctionVersion": "2018-05-29", "Name": "QueryListConnectionsDataResolverFn", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/9fcbe070ecd3023c5bf5b966fa9584757db9762eef123bad0820bd87591b2174.vtl"}, "ResponseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/cc01911d0269d4080ea57505dc445dfc315ef7ad85d3d9d4ea1357858bff451d.vtl"}}, "DependsOn": ["ConnectionDataSource"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/QueryListConnectionsDataResolverFn/QueryListConnectionsDataResolverFn.AppSyncFunction"}}, "ListConnectionResolver": {"Type": "AWS::AppSync::Resolver", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "FieldName": "listConnections", "Kind": "PIPELINE", "PipelineConfig": {"Functions": [{"Fn::GetAtt": ["QuerylistConnectionsauth0FunctionQuerylistConnectionsauth0FunctionAppSyncFunction35EE0264", "FunctionId"]}, {"Fn::GetAtt": ["QuerylistConnectionspostAuth0FunctionQuerylistConnectionspostAuth0FunctionAppSyncFunction8F17C74E", "FunctionId"]}, {"Fn::GetAtt": ["QueryListConnectionsDataResolverFnQueryListConnectionsDataResolverFnAppSyncFunction904DCC89", "FunctionId"]}]}, "RequestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Query\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"listConnections\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "ResponseMappingTemplate": "$util.toJson($ctx.prev.result)", "TypeName": "Query"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/queryListConnectionsResolver"}}, "MutationcreateConnectioninit0FunctionMutationcreateConnectioninit0FunctionAppSyncFunctionA6FE7EFA": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "MutationcreateConnectioninit0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/a183ddccbd956316c38ef97177b8f088ef0826f62023323f5ae6053d348ccffc.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectioninit0Function/MutationcreateConnectioninit0Function.AppSyncFunction"}}, "MutationcreateConnectioninit1FunctionMutationcreateConnectioninit1FunctionAppSyncFunction078CC4EA": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "MutationcreateConnectioninit1Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/747896db78e02d287e1575351ce928b81bd0fd0aab966b2ffc55a576f37cf80e.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectioninit1Function/MutationcreateConnectioninit1Function.AppSyncFunction"}}, "MutationcreateConnectionauth0FunctionMutationcreateConnectionauth0FunctionAppSyncFunction1149FE23": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "MutationcreateConnectionauth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/eb2c75f91fb7c102ca64406094e96480b71004b54804dc990a5fdb8903ef2bca.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectionauth0Function/MutationcreateConnectionauth0Function.AppSyncFunction"}}, "MutationcreateConnectionpostAuth0FunctionMutationcreateConnectionpostAuth0FunctionAppSyncFunctionBBE6C4B6": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "MutationcreateConnectionpostAuth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationcreateConnectionpostAuth0Function/MutationcreateConnectionpostAuth0Function.AppSyncFunction"}}, "MutationCreateConnectionDataResolverFnMutationCreateConnectionDataResolverFnAppSyncFunctionB09DB127": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Fn::GetAtt": ["ConnectionDataSource", "Name"]}, "FunctionVersion": "2018-05-29", "Name": "MutationCreateConnectionDataResolverFn", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/94c06debde92fed2e2c6ad051e85a3e024e4712d18d6ee6fb1cd56cda8155001.vtl"}, "ResponseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a52b72209a9dfa197b5e7367a5c378c5bb86de6e29ddd9e48b49a3fe54b249.vtl"}}, "DependsOn": ["ConnectionDataSource"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationCreateConnectionDataResolverFn/MutationCreateConnectionDataResolverFn.AppSyncFunction"}}, "CreateConnectionResolver": {"Type": "AWS::AppSync::Resolver", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "FieldName": "createConnection", "Kind": "PIPELINE", "PipelineConfig": {"Functions": [{"Fn::GetAtt": ["MutationcreateConnectioninit0FunctionMutationcreateConnectioninit0FunctionAppSyncFunctionA6FE7EFA", "FunctionId"]}, {"Fn::GetAtt": ["MutationcreateConnectioninit1FunctionMutationcreateConnectioninit1FunctionAppSyncFunction078CC4EA", "FunctionId"]}, {"Fn::GetAtt": ["MutationcreateConnectionauth0FunctionMutationcreateConnectionauth0FunctionAppSyncFunction1149FE23", "FunctionId"]}, {"Fn::GetAtt": ["MutationcreateConnectionpostAuth0FunctionMutationcreateConnectionpostAuth0FunctionAppSyncFunctionBBE6C4B6", "FunctionId"]}, {"Fn::GetAtt": ["MutationCreateConnectionDataResolverFnMutationCreateConnectionDataResolverFnAppSyncFunctionB09DB127", "FunctionId"]}]}, "RequestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Mutation\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"createConnection\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "ResponseMappingTemplate": "$util.toJson($ctx.prev.result)", "TypeName": "Mutation"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/mutationCreateConnectionResolver"}}, "MutationupdateConnectioninit0FunctionMutationupdateConnectioninit0FunctionAppSyncFunction8D243403": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "MutationupdateConnectioninit0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/06db846fd14e6fc371f22b12b5545ba8e2dbfeda85d8c8d586c71c282166657b.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectioninit0Function/MutationupdateConnectioninit0Function.AppSyncFunction"}}, "MutationupdateConnectionauth0FunctionMutationupdateConnectionauth0FunctionAppSyncFunction981E4BF8": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Fn::GetAtt": ["ConnectionDataSource", "Name"]}, "FunctionVersion": "2018-05-29", "Name": "MutationupdateConnectionauth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/1f5fed297da9c32ae3af922bf3a38ccf23b956078887d16891ec06c20c64722c.vtl"}, "ResponseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/fe139862c8f0faccbaea00d381f2ee946ff166b4a3ff39527a46c14858c9626f.vtl"}}, "DependsOn": ["ConnectionDataSource"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectionauth0Function/MutationupdateConnectionauth0Function.AppSyncFunction"}}, "MutationupdateConnectionpostAuth0FunctionMutationupdateConnectionpostAuth0FunctionAppSyncFunctionF984A8D8": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "MutationupdateConnectionpostAuth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationupdateConnectionpostAuth0Function/MutationupdateConnectionpostAuth0Function.AppSyncFunction"}}, "MutationUpdateConnectionDataResolverFnMutationUpdateConnectionDataResolverFnAppSyncFunction8C462233": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Fn::GetAtt": ["ConnectionDataSource", "Name"]}, "FunctionVersion": "2018-05-29", "Name": "MutationUpdateConnectionDataResolverFn", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/474bf0776ec2164a13191d1a0a9e057154931e4918fea5086f49850d02a5371b.vtl"}, "ResponseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a52b72209a9dfa197b5e7367a5c378c5bb86de6e29ddd9e48b49a3fe54b249.vtl"}}, "DependsOn": ["ConnectionDataSource"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationUpdateConnectionDataResolverFn/MutationUpdateConnectionDataResolverFn.AppSyncFunction"}}, "UpdateConnectionResolver": {"Type": "AWS::AppSync::Resolver", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "FieldName": "updateConnection", "Kind": "PIPELINE", "PipelineConfig": {"Functions": [{"Fn::GetAtt": ["MutationupdateConnectioninit0FunctionMutationupdateConnectioninit0FunctionAppSyncFunction8D243403", "FunctionId"]}, {"Fn::GetAtt": ["MutationupdateConnectionauth0FunctionMutationupdateConnectionauth0FunctionAppSyncFunction981E4BF8", "FunctionId"]}, {"Fn::GetAtt": ["MutationupdateConnectionpostAuth0FunctionMutationupdateConnectionpostAuth0FunctionAppSyncFunctionF984A8D8", "FunctionId"]}, {"Fn::GetAtt": ["MutationUpdateConnectionDataResolverFnMutationUpdateConnectionDataResolverFnAppSyncFunction8C462233", "FunctionId"]}]}, "RequestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Mutation\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"updateConnection\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "ResponseMappingTemplate": "$util.toJson($ctx.prev.result)", "TypeName": "Mutation"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/mutationUpdateConnectionResolver"}}, "MutationdeleteConnectionauth0FunctionMutationdeleteConnectionauth0FunctionAppSyncFunction70D5026B": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Fn::GetAtt": ["ConnectionDataSource", "Name"]}, "FunctionVersion": "2018-05-29", "Name": "MutationdeleteConnectionauth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/1f5fed297da9c32ae3af922bf3a38ccf23b956078887d16891ec06c20c64722c.vtl"}, "ResponseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/05de9eab8d843ef7ef353a7792e3969abe58aae4a1016c0995c6ebddc6f9068f.vtl"}}, "DependsOn": ["ConnectionDataSource"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationdeleteConnectionauth0Function/MutationdeleteConnectionauth0Function.AppSyncFunction"}}, "MutationdeleteConnectionpostAuth0FunctionMutationdeleteConnectionpostAuth0FunctionAppSyncFunction78F7B2E6": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "MutationdeleteConnectionpostAuth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationdeleteConnectionpostAuth0Function/MutationdeleteConnectionpostAuth0Function.AppSyncFunction"}}, "MutationDeleteConnectionDataResolverFnMutationDeleteConnectionDataResolverFnAppSyncFunction6BDC0894": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Fn::GetAtt": ["ConnectionDataSource", "Name"]}, "FunctionVersion": "2018-05-29", "Name": "MutationDeleteConnectionDataResolverFn", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/4f7907d1209a2c9953a0c053df402c634e359546d70c7cc5c2e8e21ea734880f.vtl"}, "ResponseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a52b72209a9dfa197b5e7367a5c378c5bb86de6e29ddd9e48b49a3fe54b249.vtl"}}, "DependsOn": ["ConnectionDataSource"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/MutationDeleteConnectionDataResolverFn/MutationDeleteConnectionDataResolverFn.AppSyncFunction"}}, "DeleteConnectionResolver": {"Type": "AWS::AppSync::Resolver", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "FieldName": "deleteConnection", "Kind": "PIPELINE", "PipelineConfig": {"Functions": [{"Fn::GetAtt": ["MutationdeleteConnectionauth0FunctionMutationdeleteConnectionauth0FunctionAppSyncFunction70D5026B", "FunctionId"]}, {"Fn::GetAtt": ["MutationdeleteConnectionpostAuth0FunctionMutationdeleteConnectionpostAuth0FunctionAppSyncFunction78F7B2E6", "FunctionId"]}, {"Fn::GetAtt": ["MutationDeleteConnectionDataResolverFnMutationDeleteConnectionDataResolverFnAppSyncFunction6BDC0894", "FunctionId"]}]}, "RequestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Mutation\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"deleteConnection\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "ResponseMappingTemplate": "$util.toJson($ctx.prev.result)", "TypeName": "Mutation"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/mutationDeleteConnectionResolver"}}, "SubscriptiononCreateConnectionauth0FunctionSubscriptiononCreateConnectionauth0FunctionAppSyncFunctionF1660BDA": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "SubscriptiononCreateConnectionauth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/4843bc77043eb1b2460e9b1e3df9218ebee2b5c86adc9e9f789a60b4f3a02049.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononCreateConnectionauth0Function/SubscriptiononCreateConnectionauth0Function.AppSyncFunction"}}, "SubscriptiononCreateConnectionpostAuth0FunctionSubscriptiononCreateConnectionpostAuth0FunctionAppSyncFunction374552A2": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "SubscriptiononCreateConnectionpostAuth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononCreateConnectionpostAuth0Function/SubscriptiononCreateConnectionpostAuth0Function.AppSyncFunction"}}, "SubscriptionOnCreateConnectionDataResolverFnSubscriptionOnCreateConnectionDataResolverFnAppSyncFunction49D15552": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "SubscriptionOnCreateConnectionDataResolverFn", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/fe3c43ada4b9d681a5e2312663ef7a73386424d73b73e51f8e2e9d4b50f7c502.vtl"}, "ResponseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/e0cff47fb007f0bbf2a4e43ca256d6aa7ec109821769fd79fa7c5e83f0e7f9fc.vtl"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnCreateConnectionDataResolverFn/SubscriptionOnCreateConnectionDataResolverFn.AppSyncFunction"}}, "SubscriptiononCreateConnectionResolver": {"Type": "AWS::AppSync::Resolver", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "FieldName": "onCreateConnection", "Kind": "PIPELINE", "PipelineConfig": {"Functions": [{"Fn::GetAtt": ["SubscriptiononCreateConnectionauth0FunctionSubscriptiononCreateConnectionauth0FunctionAppSyncFunctionF1660BDA", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptiononCreateConnectionpostAuth0FunctionSubscriptiononCreateConnectionpostAuth0FunctionAppSyncFunction374552A2", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptionOnCreateConnectionDataResolverFnSubscriptionOnCreateConnectionDataResolverFnAppSyncFunction49D15552", "FunctionId"]}]}, "RequestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Subscription\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"onCreateConnection\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"NONE\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "ResponseMappingTemplate": "$util.toJson($ctx.prev.result)", "TypeName": "Subscription"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/subscriptionOnCreateConnectionResolver"}}, "SubscriptiononUpdateConnectionauth0FunctionSubscriptiononUpdateConnectionauth0FunctionAppSyncFunction72B83498": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "SubscriptiononUpdateConnectionauth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/4843bc77043eb1b2460e9b1e3df9218ebee2b5c86adc9e9f789a60b4f3a02049.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononUpdateConnectionauth0Function/SubscriptiononUpdateConnectionauth0Function.AppSyncFunction"}}, "SubscriptiononUpdateConnectionpostAuth0FunctionSubscriptiononUpdateConnectionpostAuth0FunctionAppSyncFunction9A86377C": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "SubscriptiononUpdateConnectionpostAuth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononUpdateConnectionpostAuth0Function/SubscriptiononUpdateConnectionpostAuth0Function.AppSyncFunction"}}, "SubscriptionOnUpdateConnectionDataResolverFnSubscriptionOnUpdateConnectionDataResolverFnAppSyncFunction454D368C": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "SubscriptionOnUpdateConnectionDataResolverFn", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/fe3c43ada4b9d681a5e2312663ef7a73386424d73b73e51f8e2e9d4b50f7c502.vtl"}, "ResponseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/e0cff47fb007f0bbf2a4e43ca256d6aa7ec109821769fd79fa7c5e83f0e7f9fc.vtl"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnUpdateConnectionDataResolverFn/SubscriptionOnUpdateConnectionDataResolverFn.AppSyncFunction"}}, "SubscriptiononUpdateConnectionResolver": {"Type": "AWS::AppSync::Resolver", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "FieldName": "onUpdateConnection", "Kind": "PIPELINE", "PipelineConfig": {"Functions": [{"Fn::GetAtt": ["SubscriptiononUpdateConnectionauth0FunctionSubscriptiononUpdateConnectionauth0FunctionAppSyncFunction72B83498", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptiononUpdateConnectionpostAuth0FunctionSubscriptiononUpdateConnectionpostAuth0FunctionAppSyncFunction9A86377C", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptionOnUpdateConnectionDataResolverFnSubscriptionOnUpdateConnectionDataResolverFnAppSyncFunction454D368C", "FunctionId"]}]}, "RequestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Subscription\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"onUpdateConnection\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"NONE\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "ResponseMappingTemplate": "$util.toJson($ctx.prev.result)", "TypeName": "Subscription"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/subscriptionOnUpdateConnectionResolver"}}, "SubscriptiononDeleteConnectionauth0FunctionSubscriptiononDeleteConnectionauth0FunctionAppSyncFunction5B18C89C": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "SubscriptiononDeleteConnectionauth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/4843bc77043eb1b2460e9b1e3df9218ebee2b5c86adc9e9f789a60b4f3a02049.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononDeleteConnectionauth0Function/SubscriptiononDeleteConnectionauth0Function.AppSyncFunction"}}, "SubscriptiononDeleteConnectionpostAuth0FunctionSubscriptiononDeleteConnectionpostAuth0FunctionAppSyncFunction10BDC99C": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "SubscriptiononDeleteConnectionpostAuth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptiononDeleteConnectionpostAuth0Function/SubscriptiononDeleteConnectionpostAuth0Function.AppSyncFunction"}}, "SubscriptionOnDeleteConnectionDataResolverFnSubscriptionOnDeleteConnectionDataResolverFnAppSyncFunctionE824ED99": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "SubscriptionOnDeleteConnectionDataResolverFn", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/fe3c43ada4b9d681a5e2312663ef7a73386424d73b73e51f8e2e9d4b50f7c502.vtl"}, "ResponseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/e0cff47fb007f0bbf2a4e43ca256d6aa7ec109821769fd79fa7c5e83f0e7f9fc.vtl"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection/SubscriptionOnDeleteConnectionDataResolverFn/SubscriptionOnDeleteConnectionDataResolverFn.AppSyncFunction"}}, "SubscriptiononDeleteConnectionResolver": {"Type": "AWS::AppSync::Resolver", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "FieldName": "onDeleteConnection", "Kind": "PIPELINE", "PipelineConfig": {"Functions": [{"Fn::GetAtt": ["SubscriptiononDeleteConnectionauth0FunctionSubscriptiononDeleteConnectionauth0FunctionAppSyncFunction5B18C89C", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptiononDeleteConnectionpostAuth0FunctionSubscriptiononDeleteConnectionpostAuth0FunctionAppSyncFunction10BDC99C", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptionOnDeleteConnectionDataResolverFnSubscriptionOnDeleteConnectionDataResolverFnAppSyncFunctionE824ED99", "FunctionId"]}]}, "RequestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Subscription\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"onDeleteConnection\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"NONE\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "ResponseMappingTemplate": "$util.toJson($ctx.prev.result)", "TypeName": "Subscription"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/subscriptionOnDeleteConnectionResolver"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/1WPQW+DMAyFf0vvwRvtpTuuoB63iu5emcSgFJKg2OlUIf77gE6ddnp+8tPz5y3k+zfIN/jNmTZd1tsaxg9iIXMW1J0qGn/CiI6E4mKK4I0VG7yqiEOKmlSRWIL7s41/znPtZTR3jy6YGr6w7umATMqig7EK/SM966RwGPjuNYzlGi/rEgXPz8r/7pi8XiBmmsa2KeJK9Hu5v1GcFO8uyEzC8L7I7OGQdEeyAExL9jPJkESt6/nZ1vp2Uj4Ygiu/3LY7yHN43VzZ2iwmL9YRVA/9AaifxWc2AQAA"}, "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/Connection/CDKMetadata/Default"}, "Condition": "CDKMetadataAvailable"}}, "Outputs": {"GetAttConnectionTableStreamArn": {"Description": "Your DynamoDB table StreamArn.", "Value": {"Fn::GetAtt": ["ConnectionTable", "TableStreamArn"]}, "Export": {"Name": {"Fn::Join": [":", [{"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "GetAtt:ConnectionTable:StreamArn"]]}}}, "GetAttConnectionTableName": {"Description": "Your DynamoDB table name.", "Value": {"Fn::Join": ["", ["Connection-", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "-NONE"]]}, "Export": {"Name": {"Fn::Join": [":", [{"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "GetAtt:ConnectionTable:Name"]]}}}}}