{"Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"AmplifySandbox\",\"createdWith\":\"1.16.1\",\"stackType\":\"root\",\"metadata\":{}}", "Metadata": {"AWS::Amplify::Platform": {"version": "1", "stackOutputs": ["deploymentType", "region"]}, "AWS::Amplify::Auth": {"version": "1", "stackOutputs": ["userPoolId", "webClientId", "identityPoolId", "authRegion", "allowUnauthenticatedIdentities", "signupAttributes", "usernameAttributes", "verificationMechanisms", "passwordPolicyMinLength", "passwordPolicyRequirements", "mfaConfiguration", "mfaTypes", "socialProviders", "oauthCognitoDomain", "oauthScope", "oauthRedirectSignIn", "oauthRedirectSignOut", "oauthResponseType", "oauthClientId", "groups"]}, "AWS::Amplify::GraphQL": {"version": "1", "stackOutputs": ["awsAppsyncApiId", "awsAppsyncApiEndpoint", "awsAppsyncAuthenticationType", "awsAppsyncRegion", "amplifyApiModelSchemaS3Uri", "awsAppsyncApiKey", "awsAppsyncAdditionalAuthenticationTypes"]}}, "Outputs": {"deploymentType": {"Value": "sandbox"}, "region": {"Value": {"Ref": "AWS::Region"}}, "userPoolId": {"Value": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolA19BA9C3Ref"]}}, "webClientId": {"Value": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolAppClient7D0F701BRef"]}}, "identityPoolId": {"Value": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"]}}, "authRegion": {"Value": {"Ref": "AWS::Region"}}, "allowUnauthenticatedIdentities": {"Value": "true"}, "signupAttributes": {"Value": "[\"email\"]"}, "usernameAttributes": {"Value": "[\"email\"]"}, "verificationMechanisms": {"Value": "[\"email\"]"}, "passwordPolicyMinLength": {"Value": "8"}, "passwordPolicyRequirements": {"Value": "[\"REQUIRES_NUMBERS\",\"REQUIRES_LOWERCASE\",\"REQUIRES_UPPERCASE\",\"REQUIRES_SYMBOLS\"]"}, "mfaConfiguration": {"Value": "OFF"}, "mfaTypes": {"Value": "[]"}, "socialProviders": {"Value": ""}, "oauthCognitoDomain": {"Value": ""}, "oauthScope": {"Value": "[\"profile\",\"phone\",\"email\",\"openid\",\"aws.cognito.signin.user.admin\"]"}, "oauthRedirectSignIn": {"Value": "https://example.com"}, "oauthRedirectSignOut": {"Value": ""}, "oauthResponseType": {"Value": "code"}, "oauthClientId": {"Value": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolAppClient7D0F701BRef"]}}, "groups": {"Value": "[]"}, "awsAppsyncApiId": {"Value": {"Fn::GetAtt": ["data7552DF31", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"]}}, "awsAppsyncApiEndpoint": {"Value": {"Fn::GetAtt": ["data7552DF31", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2GraphQLUrl"]}}, "awsAppsyncAuthenticationType": {"Value": "AMAZON_COGNITO_USER_POOLS"}, "awsAppsyncRegion": {"Value": {"Ref": "AWS::Region"}}, "amplifyApiModelSchemaS3Uri": {"Value": {"Fn::Join": ["", ["s3://", {"Fn::Select": [0, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["data7552DF31", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentCustomResource1536MiB60279CB2DestinationBucketArn"]}]}]}]}]}, "/model-schema.graphql"]]}}, "awsAppsyncApiKey": {"Value": {"Fn::GetAtt": ["data7552DF31", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPIDefaultApiKey9D3276BBApiKey"]}}, "awsAppsyncAdditionalAuthenticationTypes": {"Value": "API_KEY,AWS_IAM"}}, "Resources": {"auth179371D7": {"Type": "AWS::CloudFormation::Stack", "Properties": {"Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "created-by", "Value": "amplify"}], "TemplateURL": {"Fn::Join": ["", ["https://s3.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/c66d0248273a35b9d9d4fae4883e6c8f7c0ab44e0096721ecde31e426d93d530.json"]]}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/auth.NestedStack/auth.NestedStackResource", "aws:asset:path": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2authE93823C0.nested.template.json", "aws:asset:property": "TemplateURL"}}, "data7552DF31": {"Type": "AWS::CloudFormation::Stack", "Properties": {"Parameters": {"referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolA19BA9C3Ref": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolA19BA9C3Ref"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef": {"Fn::GetAtt": ["auth179371D7", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"]}}, "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "created-by", "Value": "amplify"}], "TemplateURL": {"Fn::Join": ["", ["https://s3.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/2caf29a80348df64d03c3b87d414374c7b83f40949cea1694242d8edee91fed4.json"]]}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data.NestedStack/data.NestedStackResource", "aws:asset:path": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2data9B47D0E3.nested.template.json", "aws:asset:property": "TemplateURL"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/zPSM7Sw1DNUTCwv1k1OydbNyUzSqw4uSUzO1nFOy/MvLSkoLQGxwEK1Onn5Kal6WcX6ZUbGeoaGegaKWcWZmbpFpXklmbmpekEQGgDC4uzIVAAAAA=="}, "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/CDKMetadata/Default"}, "Condition": "CDKMetadataAvailable"}}, "Conditions": {"CDKMetadataAvailable": {"Fn::Or": [{"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "af-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-3"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-south-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-3"]}]}, {"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-4"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ca-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ca-west-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "cn-north-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "cn-northwest-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-central-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-north-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-south-2"]}]}, {"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-3"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "il-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "me-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "me-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "sa-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-east-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-west-1"]}]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-west-2"]}]}}, "Parameters": {"BootstrapVersion": {"Type": "AWS::SSM::Parameter::Value<String>", "Default": "/cdk-bootstrap/hnb659fds/version", "Description": "Version of the CDK Bootstrap resources in this environment, automatically retrieved from SSM Parameter Store. [cdk:skip]"}}, "Rules": {"CheckBootstrapVersion": {"Assertions": [{"Assert": {"Fn::Not": [{"Fn::Contains": [["1", "2", "3", "4", "5"], {"Ref": "BootstrapVersion"}]}]}, "AssertDescription": "CDK bootstrap stack version 6 required. Please run 'cdk bootstrap' with a recent version of the CDK CLI."}]}}}