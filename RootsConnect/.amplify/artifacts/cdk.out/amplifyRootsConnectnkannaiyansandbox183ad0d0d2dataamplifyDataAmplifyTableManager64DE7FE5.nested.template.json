{"Resources": {"AmplifyManagedTableIsCompleteRoleF825222C": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}], "Policies": [{"PolicyDocument": {"Statement": [{"Action": ["dynamodb:CreateTable", "dynamodb:UpdateTable", "dynamodb:DeleteTable", "dynamodb:DescribeTable", "dynamodb:DescribeContinuousBackups", "dynamodb:DescribeTimeToLive", "dynamodb:UpdateContinuousBackups", "dynamodb:UpdateTimeToLive", "dynamodb:TagResource", "dynamodb:UntagResource", "dynamodb:ListTagsOfResource"], "Effect": "Allow", "Resource": {"Fn::Sub": ["arn:${AWS::Partition}:dynamodb:${AWS::Region}:${AWS::AccountId}:table/*-${apiId}-${envName}", {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "envName": "NONE"}]}}, {"Action": "lambda:ListTags", "Effect": "Allow", "Resource": {"Fn::Sub": ["arn:${AWS::Partition}:lambda:${AWS::Region}:${AWS::AccountId}:function:*TableManager*", {}]}}], "Version": "2012-10-17"}, "PolicyName": "CreateUpdateDeleteTablesPolicy"}], "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "amplify:friendly-name", "Value": "amplifyData"}, {"Key": "created-by", "Value": "amplify"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyManagedTableIsCompleteRole/Resource"}}, "AmplifyManagedTableOnEventRoleB4E71DEA": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}], "Policies": [{"PolicyDocument": {"Statement": [{"Action": ["dynamodb:CreateTable", "dynamodb:UpdateTable", "dynamodb:DeleteTable", "dynamodb:DescribeTable", "dynamodb:DescribeContinuousBackups", "dynamodb:DescribeTimeToLive", "dynamodb:UpdateContinuousBackups", "dynamodb:UpdateTimeToLive", "dynamodb:TagResource", "dynamodb:UntagResource", "dynamodb:ListTagsOfResource"], "Effect": "Allow", "Resource": {"Fn::Sub": ["arn:${AWS::Partition}:dynamodb:${AWS::Region}:${AWS::AccountId}:table/*-${apiId}-${envName}", {"apiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "envName": "NONE"}]}}, {"Action": "lambda:ListTags", "Effect": "Allow", "Resource": {"Fn::Sub": ["arn:${AWS::Partition}:lambda:${AWS::Region}:${AWS::AccountId}:function:*TableManager*", {}]}}], "Version": "2012-10-17"}, "PolicyName": "CreateUpdateDeleteTablesPolicy"}], "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "amplify:friendly-name", "Value": "amplifyData"}, {"Key": "created-by", "Value": "amplify"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyManagedTableOnEventRole/Resource"}}, "AmplifyManagedTableOnEventRoleDefaultPolicyF6DABCB6": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": "states:StartExecution", "Effect": "Allow", "Resource": {"Ref": "AmplifyTableWaiterStateMachine060600BC"}}], "Version": "2012-10-17"}, "PolicyName": "AmplifyManagedTableOnEventRoleDefaultPolicyF6DABCB6", "Roles": [{"Ref": "AmplifyManagedTableOnEventRoleB4E71DEA"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyManagedTableOnEventRole/DefaultPolicy/Resource"}}, "TableManagerCustomProviderframeworkonEvent1DFC2ECC": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "S3Key": "23df4a10f7743e90a710888ea87205f23d214aad2b34e617221bec49c1d24ebc.zip"}, "Description": "AmplifyManagedTable - onEvent (amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider)", "Environment": {"Variables": {"WAITER_STATE_MACHINE_ARN": {"Ref": "AmplifyTableWaiterStateMachine060600BC"}}}, "Handler": "amplify-table-manager-handler.onEvent", "Role": {"Fn::GetAtt": ["AmplifyManagedTableOnEventRoleB4E71DEA", "<PERSON><PERSON>"]}, "Runtime": "nodejs20.x", "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "amplify:friendly-name", "Value": "amplifyData"}, {"Key": "created-by", "Value": "amplify"}], "Timeout": 840}, "DependsOn": ["AmplifyManagedTableOnEventRoleDefaultPolicyF6DABCB6", "AmplifyManagedTableOnEventRoleB4E71DEA"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider/framework-onEvent/Resource", "aws:asset:path": "asset.23df4a10f7743e90a710888ea87205f23d214aad2b34e617221bec49c1d24ebc", "aws:asset:is-bundled": false, "aws:asset:property": "Code"}}, "TableManagerCustomProviderframeworkisComplete2E51021B": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "S3Key": "23df4a10f7743e90a710888ea87205f23d214aad2b34e617221bec49c1d24ebc.zip"}, "Description": "AmplifyManagedTable - isComplete (amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider)", "Handler": "amplify-table-manager-handler.isComplete", "Role": {"Fn::GetAtt": ["AmplifyManagedTableIsCompleteRoleF825222C", "<PERSON><PERSON>"]}, "Runtime": "nodejs20.x", "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "amplify:friendly-name", "Value": "amplifyData"}, {"Key": "created-by", "Value": "amplify"}], "Timeout": 840}, "DependsOn": ["AmplifyManagedTableIsCompleteRoleF825222C"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/TableManagerCustomProvider/framework-isComplete/Resource", "aws:asset:path": "asset.23df4a10f7743e90a710888ea87205f23d214aad2b34e617221bec49c1d24ebc", "aws:asset:is-bundled": false, "aws:asset:property": "Code"}}, "AmplifyTableWaiterStateMachineRole470BE899": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "states.amazonaws.com"}}], "Version": "2012-10-17"}, "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "amplify:friendly-name", "Value": "amplifyData"}, {"Key": "created-by", "Value": "amplify"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine/Role/Resource"}}, "AmplifyTableWaiterStateMachineRoleDefaultPolicy89F3836A": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": "lambda:InvokeFunction", "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["TableManagerCustomProviderframeworkisComplete2E51021B", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["TableManagerCustomProviderframeworkisComplete2E51021B", "<PERSON><PERSON>"]}, ":*"]]}]}], "Version": "2012-10-17"}, "PolicyName": "AmplifyTableWaiterStateMachineRoleDefaultPolicy89F3836A", "Roles": [{"Ref": "AmplifyTableWaiterStateMachineRole470BE899"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine/Role/DefaultPolicy/Resource"}}, "AmplifyTableWaiterStateMachine060600BC": {"Type": "AWS::StepFunctions::StateMachine", "Properties": {"DefinitionString": {"Fn::Join": ["", ["{\"StartAt\":\"framework-isComplete-task\",\"States\":{\"framework-isComplete-task\":{\"End\":true,\"Retry\":[{\"ErrorEquals\":[\"States.ALL\"],\"IntervalSeconds\":10,\"MaxAttempts\":360,\"BackoffRate\":1}],\"Type\":\"Task\",\"Resource\":\"", {"Fn::GetAtt": ["TableManagerCustomProviderframeworkisComplete2E51021B", "<PERSON><PERSON>"]}, "\"}}}"]]}, "RoleArn": {"Fn::GetAtt": ["AmplifyTableWaiterStateMachineRole470BE899", "<PERSON><PERSON>"]}}, "DependsOn": ["AmplifyTableWaiterStateMachineRoleDefaultPolicy89F3836A", "AmplifyTableWaiterStateMachineRole470BE899"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/AmplifyTableWaiterStateMachine/Resource"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/zWNOw7CMBAFz0LvLDhpoCRIlAiFA0SOvUSbjy1lbRCyfHfygWrmaYqXgzyeQO7UmzNt+mygBuIN2aN5eKV7MYc6khohVm5AcXnalXc3kP4sc7MkBjU2RkG8Bqs9Obu0vyfBRa2Y0TOcF8wbyqB79KViTKJCdmHSKNY6P7dk2/XsF5KwziB0vH/lBUgJh13HRNkUrKcRodr4BRqy3gvPAAAA"}, "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager/CDKMetadata/Default"}, "Condition": "CDKMetadataAvailable"}}, "Conditions": {"CDKMetadataAvailable": {"Fn::Or": [{"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "af-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-3"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-south-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-3"]}]}, {"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-4"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ca-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ca-west-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "cn-north-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "cn-northwest-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-central-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-north-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-south-2"]}]}, {"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-3"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "il-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "me-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "me-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "sa-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-east-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-west-1"]}]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-west-2"]}]}}, "Parameters": {"referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": {"Type": "String"}}, "Outputs": {"amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn": {"Value": {"Fn::GetAtt": ["TableManagerCustomProviderframeworkonEvent1DFC2ECC", "<PERSON><PERSON>"]}}}}