## [Start] Authorization Steps. **
$util.qr($ctx.stash.put("hasAuth", true))
#if( $ctx.error )
  $util.error($ctx.error.message, $ctx.error.type)
#end
#set( $inputFields = $util.parseJson($util.toJson($ctx.args.input.keySet())) )
#set( $isAuthorized = false )
#set( $allowedFields = [] )
#set( $nullAllowedFields = [] )
#set( $deniedFields = {} )
#if( $util.authType() == "API Key Authorization" )
$util.unauthorized()
#end
#if( $util.authType() == "IAM Authorization" )
  #if( $util.authType() == "IAM Authorization" && $util.isNull($ctx.identity.cognitoIdentityPoolId) && $util.isNull($ctx.identity.cognitoIdentityId) )
    $util.qr($ctx.stash.put("hasAuth", true))
    #set( $isAuthorized = true )
  #else
$util.unauthorized()
  #end
#end
#if( $util.authType() == "User Pool Authorization" )
  #if( !$isAuthorized )
    #set( $ownerEntity0 = $util.defaultIfNull($ctx.result.owner, null) )
    #set( $ownerClaim0 = $util.defaultIfNull($ctx.identity.claims.get("sub"), null) )
    #set( $currentClaim1 = $util.defaultIfNull($ctx.identity.claims.get("username"), $util.defaultIfNull($ctx.identity.claims.get("cognito:username"), null)) )
    #if( !$util.isNull($ownerClaim0) && !$util.isNull($currentClaim1) )
      #set( $ownerClaim0 = "$ownerClaim0::$currentClaim1" )
      #set( $ownerClaimsList0 = [] )
      $util.qr($ownerClaimsList0.add($util.defaultIfNull($ctx.identity.claims.get("sub"), null)))
      $util.qr($ownerClaimsList0.add($util.defaultIfNull($ctx.identity.claims.get("username"), $util.defaultIfNull($ctx.identity.claims.get("cognito:username"), null))))
      #set( $ownerAllowedFields0 = ["userId","email","firstName","lastName","displayName","profilePicture","latitude","longitude","city","state","country","isLocationVisible","phoneNumbers","whatsappNumber","facebookProfile","telegramHandle","additionalEmails","isProfilePublic","shareContactInfo","allowDirectContact","interests","profession","bio","createdAt","updatedAt","lastActiveAt","id"] )
      #set( $ownerNullAllowedFields0 = ["userId","email","firstName","lastName","displayName","profilePicture","latitude","longitude","city","state","country","isLocationVisible","phoneNumbers","whatsappNumber","facebookProfile","telegramHandle","additionalEmails","isProfilePublic","shareContactInfo","allowDirectContact","interests","profession","bio","createdAt","updatedAt","lastActiveAt"] )
      #set( $isAuthorizedOnAllFields0 = true )
      #if( $ownerEntity0 == $ownerClaim0 || $ownerClaimsList0.contains($ownerEntity0) )
        #if( $isAuthorizedOnAllFields0 )
          #set( $isAuthorized = true )
        #else
          $util.qr($allowedFields.addAll($ownerAllowedFields0))
          $util.qr($nullAllowedFields.addAll($ownerNullAllowedFields0))
        #end
      #end
    #end
  #end
#end
#if( !$isAuthorized && $allowedFields.isEmpty() && $nullAllowedFields.isEmpty() )
$util.unauthorized()
#end
#if( !$isAuthorized )
  #foreach( $entry in $util.map.copyAndRetainAllKeys($ctx.args.input, $inputFields).entrySet() )
    #if( $util.isNull($entry.value) && !$nullAllowedFields.contains($entry.key) )
      $util.qr($deniedFields.put($entry.key, ""))
    #end
  #end
  #foreach( $deniedField in $util.list.copyAndRemoveAll($inputFields, $allowedFields) )
    $util.qr($deniedFields.put($deniedField, ""))
  #end
#end
#if( $deniedFields.keySet().size() > 0 )
  $util.error("Unauthorized on ${deniedFields.keySet()}", "Unauthorized")
#end
$util.toJson({})
## [End] Authorization Steps. **