type UserProfile @model @auth(rules: [{allow: owner, ownerField: "owner"},
  {allow: private, operations: [read]}])
{
  userId: String!
  email: AWSEmail!
  firstName: String
  lastName: String
  displayName: String
  profilePicture: AWSURL
  latitude: Float
  longitude: Float
  city: String
  state: String
  country: String
  isLocationVisible: <PERSON>olean @default(value: "false")
  phoneNumbers: [String]
  whatsappNumber: String
  facebookProfile: AWSURL
  telegramHandle: String
  additionalEmails: [AWSEmail]
  isProfilePublic: Boolean @default(value: "false")
  shareContactInfo: Boolean @default(value: "false")
  allowDirectContact: <PERSON>olean @default(value: "false")
  interests: [String]
  profession: String
  bio: String
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
  lastActiveAt: AWSDateTime
}

type ConnectionRequest @model @auth(rules: [{allow: owner, operations: [create, read, update], ownerField: "owner"},
  {allow: private, operations: [read]}])
{
  fromUserId: String!
  toUserId: String!
  status: ConnectionRequestStatus
  message: String
  createdAt: AWSDateTime
  updatedAt: AWSDateTime
}

type Connection @model @auth(rules: [{allow: private, operations: [read]}])
{
  user1Id: String!
  user2Id: String!
  connectedAt: AWSDateTime
  sharedContactInfo: Boolean @default(value: "false")
}

enum ConnectionRequestStatus {
  pending
  accepted
  declined
}