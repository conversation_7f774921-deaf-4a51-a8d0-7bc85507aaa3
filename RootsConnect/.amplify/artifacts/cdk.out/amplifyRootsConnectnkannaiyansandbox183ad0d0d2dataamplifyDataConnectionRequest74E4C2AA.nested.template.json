{"Parameters": {"DynamoDBModelTableReadIOPS": {"Type": "Number", "Default": 5, "Description": "The number of read IOPS the table should support."}, "DynamoDBModelTableWriteIOPS": {"Type": "Number", "Default": 5, "Description": "The number of write IOPS the table should support."}, "DynamoDBBillingMode": {"Type": "String", "Default": "PAY_PER_REQUEST", "AllowedValues": ["PAY_PER_REQUEST", "PROVISIONED"], "Description": "Configure @model types to create DynamoDB tables with PAY_PER_REQUEST or PROVISIONED billing modes."}, "DynamoDBEnablePointInTimeRecovery": {"Type": "String", "Default": "false", "AllowedValues": ["true", "false"], "Description": "Whether to enable Point in Time Recovery on the table."}, "DynamoDBEnableServerSideEncryption": {"Type": "String", "Default": "true", "AllowedValues": ["true", "false"], "Description": "Enable server side encryption powered by KMS."}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916OutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableCA8BFB67": {"Type": "String"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": {"Type": "String"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName": {"Type": "String"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref": {"Type": "String"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef": {"Type": "String"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef": {"Type": "String"}}, "Conditions": {"HasEnvironmentParameter": {"Fn::Not": [{"Fn::Equals": ["NONE", "NONE"]}]}, "ShouldUsePayPerRequestBilling": {"Fn::Equals": [{"Ref": "DynamoDBBillingMode"}, "PAY_PER_REQUEST"]}, "ShouldUsePointInTimeRecovery": {"Fn::Equals": [{"Ref": "DynamoDBEnablePointInTimeRecovery"}, "true"]}, "CDKMetadataAvailable": {"Fn::Or": [{"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "af-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-3"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-south-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-3"]}]}, {"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-4"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ca-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ca-west-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "cn-north-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "cn-northwest-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-central-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-north-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-south-2"]}]}, {"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-3"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "il-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "me-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "me-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "sa-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-east-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-west-1"]}]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-west-2"]}]}}, "Resources": {"ConnectionRequestTable": {"Type": "Custom::AmplifyDynamoDBTable", "Properties": {"ServiceToken": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916OutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableCA8BFB67"}, "tableName": {"Fn::Join": ["", ["ConnectionRequest-", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "-NONE"]]}, "attributeDefinitions": [{"attributeName": "id", "attributeType": "S"}], "keySchema": [{"attributeName": "id", "keyType": "HASH"}], "provisionedThroughput": {"Fn::If": ["ShouldUsePayPerRequestBilling", {"Ref": "AWS::NoValue"}, {"ReadCapacityUnits": {"Ref": "DynamoDBModelTableReadIOPS"}, "WriteCapacityUnits": {"Ref": "DynamoDBModelTableWriteIOPS"}}]}, "sseSpecification": {"sseEnabled": false}, "streamSpecification": {"streamViewType": "NEW_AND_OLD_IMAGES"}, "deletionProtectionEnabled": false, "allowDestructiveGraphqlSchemaUpdates": true, "replaceTableUponGsiUpdate": true, "pointInTimeRecoverySpecification": {"Fn::If": ["ShouldUsePointInTimeRecovery", {"PointInTimeRecoveryEnabled": true}, {"Ref": "AWS::NoValue"}]}, "billingMode": {"Fn::If": ["ShouldUsePayPerRequestBilling", "PAY_PER_REQUEST", {"Ref": "AWS::NoValue"}]}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestTable/Default/Default"}}, "ConnectionRequestIAMRoleDBB29E05": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "appsync.amazonaws.com"}}], "Version": "2012-10-17"}, "Policies": [{"PolicyDocument": {"Statement": [{"Action": ["dynamodb:BatchGetItem", "dynamodb:BatchWriteItem", "dynamodb:PutItem", "dynamodb:DeleteItem", "dynamodb:GetItem", "dynamodb:<PERSON><PERSON>", "dynamodb:Query", "dynamodb:UpdateItem", "dynamodb:ConditionCheckItem", "dynamodb:DescribeTable", "dynamodb:GetRecords", "dynamodb:GetShardIterator"], "Effect": "Allow", "Resource": [{"Fn::Sub": ["arn:${AWS::Partition}:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${tablename}", {"tablename": {"Fn::Join": ["", ["ConnectionRequest-", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "-NONE"]]}}]}, {"Fn::Sub": ["arn:${AWS::Partition}:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${tablename}/*", {"tablename": {"Fn::Join": ["", ["ConnectionRequest-", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "-NONE"]]}}]}]}], "Version": "2012-10-17"}, "PolicyName": "DynamoDBAccess"}], "RoleName": {"Fn::Join": ["", ["ConnectionRequestIAM90112b-", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "-NONE"]]}, "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "amplify:friendly-name", "Value": "amplifyData"}, {"Key": "created-by", "Value": "amplify"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestIAMRole/Resource"}}, "ConnectionRequestDataSource": {"Type": "AWS::AppSync::DataSource", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DynamoDBConfig": {"AwsRegion": {"Ref": "AWS::Region"}, "TableName": {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionRequestTable", "TableArn"]}]}]}]}]}}, "Name": "ConnectionRequestTable", "ServiceRoleArn": {"Fn::GetAtt": ["ConnectionRequestIAMRoleDBB29E05", "<PERSON><PERSON>"]}, "Type": "AMAZON_DYNAMODB"}, "DependsOn": ["ConnectionRequestIAMRoleDBB29E05"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestDataSource/Resource"}}, "QuerygetConnectionRequestauth0FunctionQuerygetConnectionRequestauth0FunctionAppSyncFunctionA41C0055": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "QuerygetConnectionRequestauth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/6f1b01c4b2882f213b91241f489e2393bc199b215b8b1695ea62da2156719905.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerygetConnectionRequestauth0Function/QuerygetConnectionRequestauth0Function.AppSyncFunction"}}, "QuerygetConnectionRequestpostAuth0FunctionQuerygetConnectionRequestpostAuth0FunctionAppSyncFunction4CD4281F": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "QuerygetConnectionRequestpostAuth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerygetConnectionRequestpostAuth0Function/QuerygetConnectionRequestpostAuth0Function.AppSyncFunction"}}, "QueryGetConnectionRequestDataResolverFnQueryGetConnectionRequestDataResolverFnAppSyncFunctionE41CF200": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Fn::GetAtt": ["ConnectionRequestDataSource", "Name"]}, "FunctionVersion": "2018-05-29", "Name": "QueryGetConnectionRequestDataResolverFn", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/08f4d557693d96c1a4efba0f9dc91330e4b19772fd5477c156468843e3d9cb5e.vtl"}, "ResponseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/4c6a2d29f01c6091bd1d9afe16e5849d456c96f17c3b215938c8067399532719.vtl"}}, "DependsOn": ["ConnectionRequestDataSource"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QueryGetConnectionRequestDataResolverFn/QueryGetConnectionRequestDataResolverFn.AppSyncFunction"}}, "GetConnectionRequestResolver": {"Type": "AWS::AppSync::Resolver", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "FieldName": "getConnectionRequest", "Kind": "PIPELINE", "PipelineConfig": {"Functions": [{"Fn::GetAtt": ["QuerygetConnectionRequestauth0FunctionQuerygetConnectionRequestauth0FunctionAppSyncFunctionA41C0055", "FunctionId"]}, {"Fn::GetAtt": ["QuerygetConnectionRequestpostAuth0FunctionQuerygetConnectionRequestpostAuth0FunctionAppSyncFunction4CD4281F", "FunctionId"]}, {"Fn::GetAtt": ["QueryGetConnectionRequestDataResolverFnQueryGetConnectionRequestDataResolverFnAppSyncFunctionE41CF200", "FunctionId"]}]}, "RequestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Query\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"getConnectionRequest\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionRequestTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "ResponseMappingTemplate": "$util.toJson($ctx.prev.result)", "TypeName": "Query"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/queryGetConnectionRequestResolver"}}, "QuerylistConnectionRequestsauth0FunctionQuerylistConnectionRequestsauth0FunctionAppSyncFunction3684FEED": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "QuerylistConnectionRequestsauth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/6f1b01c4b2882f213b91241f489e2393bc199b215b8b1695ea62da2156719905.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerylistConnectionRequestsauth0Function/QuerylistConnectionRequestsauth0Function.AppSyncFunction"}}, "QuerylistConnectionRequestspostAuth0FunctionQuerylistConnectionRequestspostAuth0FunctionAppSyncFunction9E8C9C49": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "QuerylistConnectionRequestspostAuth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QuerylistConnectionRequestspostAuth0Function/QuerylistConnectionRequestspostAuth0Function.AppSyncFunction"}}, "QueryListConnectionRequestsDataResolverFnQueryListConnectionRequestsDataResolverFnAppSyncFunction824D6927": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Fn::GetAtt": ["ConnectionRequestDataSource", "Name"]}, "FunctionVersion": "2018-05-29", "Name": "QueryListConnectionRequestsDataResolverFn", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/9fcbe070ecd3023c5bf5b966fa9584757db9762eef123bad0820bd87591b2174.vtl"}, "ResponseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/cc01911d0269d4080ea57505dc445dfc315ef7ad85d3d9d4ea1357858bff451d.vtl"}}, "DependsOn": ["ConnectionRequestDataSource"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/QueryListConnectionRequestsDataResolverFn/QueryListConnectionRequestsDataResolverFn.AppSyncFunction"}}, "ListConnectionRequestResolver": {"Type": "AWS::AppSync::Resolver", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "FieldName": "listConnectionRequests", "Kind": "PIPELINE", "PipelineConfig": {"Functions": [{"Fn::GetAtt": ["QuerylistConnectionRequestsauth0FunctionQuerylistConnectionRequestsauth0FunctionAppSyncFunction3684FEED", "FunctionId"]}, {"Fn::GetAtt": ["QuerylistConnectionRequestspostAuth0FunctionQuerylistConnectionRequestspostAuth0FunctionAppSyncFunction9E8C9C49", "FunctionId"]}, {"Fn::GetAtt": ["QueryListConnectionRequestsDataResolverFnQueryListConnectionRequestsDataResolverFnAppSyncFunction824D6927", "FunctionId"]}]}, "RequestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Query\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"listConnectionRequests\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionRequestTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "ResponseMappingTemplate": "$util.toJson($ctx.prev.result)", "TypeName": "Query"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/queryListConnectionRequestsResolver"}}, "MutationcreateConnectionRequestinit0FunctionMutationcreateConnectionRequestinit0FunctionAppSyncFunction247F59E8": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "MutationcreateConnectionRequestinit0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/a183ddccbd956316c38ef97177b8f088ef0826f62023323f5ae6053d348ccffc.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestinit0Function/MutationcreateConnectionRequestinit0Function.AppSyncFunction"}}, "MutationcreateConnectionRequestauth0FunctionMutationcreateConnectionRequestauth0FunctionAppSyncFunctionF8ADB385": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "MutationcreateConnectionRequestauth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/6160f4b79b6a93016faacf67febf2c5ed13e1085b161db7c2d9ec18efa09197a.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestauth0Function/MutationcreateConnectionRequestauth0Function.AppSyncFunction"}}, "MutationcreateConnectionRequestpostAuth0FunctionMutationcreateConnectionRequestpostAuth0FunctionAppSyncFunctionFC25477B": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "MutationcreateConnectionRequestpostAuth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationcreateConnectionRequestpostAuth0Function/MutationcreateConnectionRequestpostAuth0Function.AppSyncFunction"}}, "MutationCreateConnectionRequestDataResolverFnMutationCreateConnectionRequestDataResolverFnAppSyncFunction6BFA2576": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Fn::GetAtt": ["ConnectionRequestDataSource", "Name"]}, "FunctionVersion": "2018-05-29", "Name": "MutationCreateConnectionRequestDataResolverFn", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/06d7f74c3fda50f2346af03e3b3fc281f9cee655ea01b997d3e8b240b0eefd5d.vtl"}, "ResponseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a52b72209a9dfa197b5e7367a5c378c5bb86de6e29ddd9e48b49a3fe54b249.vtl"}}, "DependsOn": ["ConnectionRequestDataSource"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationCreateConnectionRequestDataResolverFn/MutationCreateConnectionRequestDataResolverFn.AppSyncFunction"}}, "CreateConnectionRequestResolver": {"Type": "AWS::AppSync::Resolver", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "FieldName": "createConnectionRequest", "Kind": "PIPELINE", "PipelineConfig": {"Functions": [{"Fn::GetAtt": ["MutationcreateConnectionRequestinit0FunctionMutationcreateConnectionRequestinit0FunctionAppSyncFunction247F59E8", "FunctionId"]}, {"Fn::GetAtt": ["MutationcreateConnectionRequestauth0FunctionMutationcreateConnectionRequestauth0FunctionAppSyncFunctionF8ADB385", "FunctionId"]}, {"Fn::GetAtt": ["MutationcreateConnectionRequestpostAuth0FunctionMutationcreateConnectionRequestpostAuth0FunctionAppSyncFunctionFC25477B", "FunctionId"]}, {"Fn::GetAtt": ["MutationCreateConnectionRequestDataResolverFnMutationCreateConnectionRequestDataResolverFnAppSyncFunction6BFA2576", "FunctionId"]}]}, "RequestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Mutation\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"createConnectionRequest\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionRequestTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "ResponseMappingTemplate": "$util.toJson($ctx.prev.result)", "TypeName": "Mutation"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/mutationCreateConnectionRequestResolver"}}, "MutationupdateConnectionRequestinit0FunctionMutationupdateConnectionRequestinit0FunctionAppSyncFunction11EFE1FC": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "MutationupdateConnectionRequestinit0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/06db846fd14e6fc371f22b12b5545ba8e2dbfeda85d8c8d586c71c282166657b.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestinit0Function/MutationupdateConnectionRequestinit0Function.AppSyncFunction"}}, "MutationupdateConnectionRequestauth0FunctionMutationupdateConnectionRequestauth0FunctionAppSyncFunctionC3E63FA3": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Fn::GetAtt": ["ConnectionRequestDataSource", "Name"]}, "FunctionVersion": "2018-05-29", "Name": "MutationupdateConnectionRequestauth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/1f5fed297da9c32ae3af922bf3a38ccf23b956078887d16891ec06c20c64722c.vtl"}, "ResponseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/48ab3887a29703dea320f36be3db1a3e35f7f87be239a424744d3fd05edd9e0d.vtl"}}, "DependsOn": ["ConnectionRequestDataSource"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestauth0Function/MutationupdateConnectionRequestauth0Function.AppSyncFunction"}}, "MutationupdateConnectionRequestpostAuth0FunctionMutationupdateConnectionRequestpostAuth0FunctionAppSyncFunction5D2372C9": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "MutationupdateConnectionRequestpostAuth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationupdateConnectionRequestpostAuth0Function/MutationupdateConnectionRequestpostAuth0Function.AppSyncFunction"}}, "MutationUpdateConnectionRequestDataResolverFnMutationUpdateConnectionRequestDataResolverFnAppSyncFunctionB5413E62": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Fn::GetAtt": ["ConnectionRequestDataSource", "Name"]}, "FunctionVersion": "2018-05-29", "Name": "MutationUpdateConnectionRequestDataResolverFn", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/474bf0776ec2164a13191d1a0a9e057154931e4918fea5086f49850d02a5371b.vtl"}, "ResponseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a52b72209a9dfa197b5e7367a5c378c5bb86de6e29ddd9e48b49a3fe54b249.vtl"}}, "DependsOn": ["ConnectionRequestDataSource"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationUpdateConnectionRequestDataResolverFn/MutationUpdateConnectionRequestDataResolverFn.AppSyncFunction"}}, "UpdateConnectionRequestResolver": {"Type": "AWS::AppSync::Resolver", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "FieldName": "updateConnectionRequest", "Kind": "PIPELINE", "PipelineConfig": {"Functions": [{"Fn::GetAtt": ["MutationupdateConnectionRequestinit0FunctionMutationupdateConnectionRequestinit0FunctionAppSyncFunction11EFE1FC", "FunctionId"]}, {"Fn::GetAtt": ["MutationupdateConnectionRequestauth0FunctionMutationupdateConnectionRequestauth0FunctionAppSyncFunctionC3E63FA3", "FunctionId"]}, {"Fn::GetAtt": ["MutationupdateConnectionRequestpostAuth0FunctionMutationupdateConnectionRequestpostAuth0FunctionAppSyncFunction5D2372C9", "FunctionId"]}, {"Fn::GetAtt": ["MutationUpdateConnectionRequestDataResolverFnMutationUpdateConnectionRequestDataResolverFnAppSyncFunctionB5413E62", "FunctionId"]}]}, "RequestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Mutation\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"updateConnectionRequest\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionRequestTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "ResponseMappingTemplate": "$util.toJson($ctx.prev.result)", "TypeName": "Mutation"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/mutationUpdateConnectionRequestResolver"}}, "MutationdeleteConnectionRequestauth0FunctionMutationdeleteConnectionRequestauth0FunctionAppSyncFunction12E9EEE6": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Fn::GetAtt": ["ConnectionRequestDataSource", "Name"]}, "FunctionVersion": "2018-05-29", "Name": "MutationdeleteConnectionRequestauth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/1f5fed297da9c32ae3af922bf3a38ccf23b956078887d16891ec06c20c64722c.vtl"}, "ResponseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/05de9eab8d843ef7ef353a7792e3969abe58aae4a1016c0995c6ebddc6f9068f.vtl"}}, "DependsOn": ["ConnectionRequestDataSource"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationdeleteConnectionRequestauth0Function/MutationdeleteConnectionRequestauth0Function.AppSyncFunction"}}, "MutationdeleteConnectionRequestpostAuth0FunctionMutationdeleteConnectionRequestpostAuth0FunctionAppSyncFunction3CA5FEC0": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "MutationdeleteConnectionRequestpostAuth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationdeleteConnectionRequestpostAuth0Function/MutationdeleteConnectionRequestpostAuth0Function.AppSyncFunction"}}, "MutationDeleteConnectionRequestDataResolverFnMutationDeleteConnectionRequestDataResolverFnAppSyncFunction6E38EBEC": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Fn::GetAtt": ["ConnectionRequestDataSource", "Name"]}, "FunctionVersion": "2018-05-29", "Name": "MutationDeleteConnectionRequestDataResolverFn", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/4f7907d1209a2c9953a0c053df402c634e359546d70c7cc5c2e8e21ea734880f.vtl"}, "ResponseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a52b72209a9dfa197b5e7367a5c378c5bb86de6e29ddd9e48b49a3fe54b249.vtl"}}, "DependsOn": ["ConnectionRequestDataSource"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/MutationDeleteConnectionRequestDataResolverFn/MutationDeleteConnectionRequestDataResolverFn.AppSyncFunction"}}, "DeleteConnectionRequestResolver": {"Type": "AWS::AppSync::Resolver", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "FieldName": "deleteConnectionRequest", "Kind": "PIPELINE", "PipelineConfig": {"Functions": [{"Fn::GetAtt": ["MutationdeleteConnectionRequestauth0FunctionMutationdeleteConnectionRequestauth0FunctionAppSyncFunction12E9EEE6", "FunctionId"]}, {"Fn::GetAtt": ["MutationdeleteConnectionRequestpostAuth0FunctionMutationdeleteConnectionRequestpostAuth0FunctionAppSyncFunction3CA5FEC0", "FunctionId"]}, {"Fn::GetAtt": ["MutationDeleteConnectionRequestDataResolverFnMutationDeleteConnectionRequestDataResolverFnAppSyncFunction6E38EBEC", "FunctionId"]}]}, "RequestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Mutation\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"deleteConnectionRequest\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"AMAZON_DYNAMODB\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n$util.qr($ctx.stash.put(\"tableName\", \"", {"Fn::Select": [1, {"Fn::Split": ["/", {"Fn::Select": [5, {"Fn::Split": [":", {"Fn::GetAtt": ["ConnectionRequestTable", "TableArn"]}]}]}]}]}, "\"))\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "ResponseMappingTemplate": "$util.toJson($ctx.prev.result)", "TypeName": "Mutation"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/mutationDeleteConnectionRequestResolver"}}, "SubscriptiononCreateConnectionRequestauth0FunctionSubscriptiononCreateConnectionRequestauth0FunctionAppSyncFunction76718EB6": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "SubscriptiononCreateConnectionRequestauth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a6f34d8595215fbaec56ec853de13cd014504c4abe3ff7457af53316ba2779.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononCreateConnectionRequestauth0Function/SubscriptiononCreateConnectionRequestauth0Function.AppSyncFunction"}}, "SubscriptiononCreateConnectionRequestpostAuth0FunctionSubscriptiononCreateConnectionRequestpostAuth0FunctionAppSyncFunction052BC134": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "SubscriptiononCreateConnectionRequestpostAuth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononCreateConnectionRequestpostAuth0Function/SubscriptiononCreateConnectionRequestpostAuth0Function.AppSyncFunction"}}, "SubscriptionOnCreateConnectionRequestDataResolverFnSubscriptionOnCreateConnectionRequestDataResolverFnAppSyncFunction36214076": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "SubscriptionOnCreateConnectionRequestDataResolverFn", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/fe3c43ada4b9d681a5e2312663ef7a73386424d73b73e51f8e2e9d4b50f7c502.vtl"}, "ResponseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/e0cff47fb007f0bbf2a4e43ca256d6aa7ec109821769fd79fa7c5e83f0e7f9fc.vtl"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnCreateConnectionRequestDataResolverFn/SubscriptionOnCreateConnectionRequestDataResolverFn.AppSyncFunction"}}, "SubscriptiononCreateConnectionRequestResolver": {"Type": "AWS::AppSync::Resolver", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "FieldName": "onCreateConnectionRequest", "Kind": "PIPELINE", "PipelineConfig": {"Functions": [{"Fn::GetAtt": ["SubscriptiononCreateConnectionRequestauth0FunctionSubscriptiononCreateConnectionRequestauth0FunctionAppSyncFunction76718EB6", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptiononCreateConnectionRequestpostAuth0FunctionSubscriptiononCreateConnectionRequestpostAuth0FunctionAppSyncFunction052BC134", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptionOnCreateConnectionRequestDataResolverFnSubscriptionOnCreateConnectionRequestDataResolverFnAppSyncFunction36214076", "FunctionId"]}]}, "RequestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Subscription\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"onCreateConnectionRequest\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"NONE\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "ResponseMappingTemplate": "$util.toJson($ctx.prev.result)", "TypeName": "Subscription"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/subscriptionOnCreateConnectionRequestResolver"}}, "SubscriptiononUpdateConnectionRequestauth0FunctionSubscriptiononUpdateConnectionRequestauth0FunctionAppSyncFunction12E84EB8": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "SubscriptiononUpdateConnectionRequestauth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a6f34d8595215fbaec56ec853de13cd014504c4abe3ff7457af53316ba2779.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononUpdateConnectionRequestauth0Function/SubscriptiononUpdateConnectionRequestauth0Function.AppSyncFunction"}}, "SubscriptiononUpdateConnectionRequestpostAuth0FunctionSubscriptiononUpdateConnectionRequestpostAuth0FunctionAppSyncFunction6109604D": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "SubscriptiononUpdateConnectionRequestpostAuth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononUpdateConnectionRequestpostAuth0Function/SubscriptiononUpdateConnectionRequestpostAuth0Function.AppSyncFunction"}}, "SubscriptionOnUpdateConnectionRequestDataResolverFnSubscriptionOnUpdateConnectionRequestDataResolverFnAppSyncFunction752B6F84": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "SubscriptionOnUpdateConnectionRequestDataResolverFn", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/fe3c43ada4b9d681a5e2312663ef7a73386424d73b73e51f8e2e9d4b50f7c502.vtl"}, "ResponseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/e0cff47fb007f0bbf2a4e43ca256d6aa7ec109821769fd79fa7c5e83f0e7f9fc.vtl"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnUpdateConnectionRequestDataResolverFn/SubscriptionOnUpdateConnectionRequestDataResolverFn.AppSyncFunction"}}, "SubscriptiononUpdateConnectionRequestResolver": {"Type": "AWS::AppSync::Resolver", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "FieldName": "onUpdateConnectionRequest", "Kind": "PIPELINE", "PipelineConfig": {"Functions": [{"Fn::GetAtt": ["SubscriptiononUpdateConnectionRequestauth0FunctionSubscriptiononUpdateConnectionRequestauth0FunctionAppSyncFunction12E84EB8", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptiononUpdateConnectionRequestpostAuth0FunctionSubscriptiononUpdateConnectionRequestpostAuth0FunctionAppSyncFunction6109604D", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptionOnUpdateConnectionRequestDataResolverFnSubscriptionOnUpdateConnectionRequestDataResolverFnAppSyncFunction752B6F84", "FunctionId"]}]}, "RequestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Subscription\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"onUpdateConnectionRequest\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"NONE\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "ResponseMappingTemplate": "$util.toJson($ctx.prev.result)", "TypeName": "Subscription"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/subscriptionOnUpdateConnectionRequestResolver"}}, "SubscriptiononDeleteConnectionRequestauth0FunctionSubscriptiononDeleteConnectionRequestauth0FunctionAppSyncFunction683C01A0": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "SubscriptiononDeleteConnectionRequestauth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/f4a6f34d8595215fbaec56ec853de13cd014504c4abe3ff7457af53316ba2779.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononDeleteConnectionRequestauth0Function/SubscriptiononDeleteConnectionRequestauth0Function.AppSyncFunction"}}, "SubscriptiononDeleteConnectionRequestpostAuth0FunctionSubscriptiononDeleteConnectionRequestpostAuth0FunctionAppSyncFunctionE8AB16B2": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "SubscriptiononDeleteConnectionRequestpostAuth0Function", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/c1721bcd774e27c514d3454b5be4f9bdd094c0161b57ddf053d618e3b0086a77.vtl"}, "ResponseMappingTemplate": "$util.toJson({})"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptiononDeleteConnectionRequestpostAuth0Function/SubscriptiononDeleteConnectionRequestpostAuth0Function.AppSyncFunction"}}, "SubscriptionOnDeleteConnectionRequestDataResolverFnSubscriptionOnDeleteConnectionRequestDataResolverFnAppSyncFunctionDD6B194B": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "SubscriptionOnDeleteConnectionRequestDataResolverFn", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/fe3c43ada4b9d681a5e2312663ef7a73386424d73b73e51f8e2e9d4b50f7c502.vtl"}, "ResponseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/e0cff47fb007f0bbf2a4e43ca256d6aa7ec109821769fd79fa7c5e83f0e7f9fc.vtl"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/SubscriptionOnDeleteConnectionRequestDataResolverFn/SubscriptionOnDeleteConnectionRequestDataResolverFn.AppSyncFunction"}}, "SubscriptiononDeleteConnectionRequestResolver": {"Type": "AWS::AppSync::Resolver", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "FieldName": "onDeleteConnectionRequest", "Kind": "PIPELINE", "PipelineConfig": {"Functions": [{"Fn::GetAtt": ["SubscriptiononDeleteConnectionRequestauth0FunctionSubscriptiononDeleteConnectionRequestauth0FunctionAppSyncFunction683C01A0", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptiononDeleteConnectionRequestpostAuth0FunctionSubscriptiononDeleteConnectionRequestpostAuth0FunctionAppSyncFunctionE8AB16B2", "FunctionId"]}, {"Fn::GetAtt": ["SubscriptionOnDeleteConnectionRequestDataResolverFnSubscriptionOnDeleteConnectionRequestDataResolverFnAppSyncFunctionDD6B194B", "FunctionId"]}]}, "RequestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"Subscription\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"onDeleteConnectionRequest\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"NONE\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "ResponseMappingTemplate": "$util.toJson($ctx.prev.result)", "TypeName": "Subscription"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/subscriptionOnDeleteConnectionRequestResolver"}}, "ConnectionRequestOwnerDataResolverFnConnectionRequestOwnerDataResolverFnAppSyncFunction2C2E4DA9": {"Type": "AWS::AppSync::FunctionConfiguration", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "DataSourceName": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName"}, "FunctionVersion": "2018-05-29", "Name": "ConnectionRequestOwnerDataResolverFn", "RequestMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/041534e5fd916595f752318f161512d7c7f83b9f2cf32d0f0be381c12253ff68.vtl"}, "ResponseMappingTemplateS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/064303962e481067b44300212516363b99aaee539b6bafaf756fdd83ff0b60f0.vtl"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/ConnectionRequestOwnerDataResolverFn/ConnectionRequestOwnerDataResolverFn.AppSyncFunction"}}, "ConnectionRequestownerResolver": {"Type": "AWS::AppSync::Resolver", "Properties": {"ApiId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "FieldName": "owner", "Kind": "PIPELINE", "PipelineConfig": {"Functions": [{"Fn::GetAtt": ["ConnectionRequestOwnerDataResolverFnConnectionRequestOwnerDataResolverFnAppSyncFunction2C2E4DA9", "FunctionId"]}]}, "RequestMappingTemplate": {"Fn::Join": ["", ["$util.qr($ctx.stash.put(\"typeName\", \"ConnectionRequest\"))\n$util.qr($ctx.stash.put(\"fieldName\", \"owner\"))\n$util.qr($ctx.stash.put(\"conditions\", []))\n$util.qr($ctx.stash.put(\"metadata\", {}))\n$util.qr($ctx.stash.metadata.put(\"dataSourceType\", \"NONE\"))\n$util.qr($ctx.stash.metadata.put(\"apiId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "\"))\n$util.qr($ctx.stash.put(\"connectionAttributes\", {}))\n\n$util.qr($ctx.stash.put(\"authRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"unauthRole\", \"arn:", {"Ref": "AWS::Partition"}, ":sts::", {"Ref": "AWS::AccountId"}, ":assumed-role/", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "/CognitoIdentityCredentials\"))\n$util.qr($ctx.stash.put(\"identityPoolId\", \"", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}, "\"))\n$util.qr($ctx.stash.put(\"adminRoles\", []))\n$util.toJson({})"]]}, "ResponseMappingTemplate": "$util.toJson($ctx.prev.result)", "TypeName": "ConnectionRequest"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/connectionRequestOwnerResolver"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/1WPQW+DMAyFf0vvwRvtpTuuoB63iu5emcSgFJKg2OlUIf77gE6ddnp+8tPz5y3k+zfIN/jNmTZd1tsaxg9iIXMW1J0qGn/CiI6E4mKK4I0VG7yqiEOKmlSRWIL7s41/znPtZTR3jy6YGr6w7umATMqig7EK/SM966RwGPjuNYzlGi/rEgXPz8r/7pi8XiBmmsa2KeJK9Hu5v1GcFO8uyEzC8L7I7OGQdEeyAExL9jPJkESt6/nZ1vp2Uj4Ygiu/3LY7yHN43VzZ2iwmL9YRVA/9AaifxWc2AQAA"}, "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest/CDKMetadata/Default"}, "Condition": "CDKMetadataAvailable"}}, "Outputs": {"GetAttConnectionRequestTableStreamArn": {"Description": "Your DynamoDB table StreamArn.", "Value": {"Fn::GetAtt": ["ConnectionRequestTable", "TableStreamArn"]}, "Export": {"Name": {"Fn::Join": [":", [{"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "GetAtt:ConnectionRequestTable:StreamArn"]]}}}, "GetAttConnectionRequestTableName": {"Description": "Your DynamoDB table name.", "Value": {"Fn::Join": ["", ["ConnectionRequest-", {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "-NONE"]]}, "Export": {"Name": {"Fn::Join": [":", [{"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId"}, "GetAtt:ConnectionRequestTable:Name"]]}}}}}