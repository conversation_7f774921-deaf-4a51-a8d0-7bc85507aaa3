{"Description": "{\"createdOn\":\"<PERSON>\",\"createdBy\":\"AmplifySandbox\",\"createdWith\":\"1.20.3\",\"stackType\":\"api-AppSync\",\"metadata\":{\"dataSources\":\"dynamodb\",\"authorizationModes\":\"amazon_cognito_identity_pools,amazon_cognito_user_pools,api_key,aws_iam\",\"customOperations\":\"\"}}", "Resources": {"amplifyDataGraphQLAPI42A6FA33": {"Type": "AWS::AppSync::GraphQLApi", "Properties": {"AdditionalAuthenticationProviders": [{"AuthenticationType": "API_KEY"}, {"AuthenticationType": "AWS_IAM"}], "AuthenticationType": "AMAZON_COGNITO_USER_POOLS", "Name": "amplifyData", "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "amplify:friendly-name", "Value": "amplifyData"}, {"Key": "created-by", "Value": "amplify"}], "UserPoolConfig": {"AwsRegion": {"Ref": "AWS::Region"}, "DefaultAction": "ALLOW", "UserPoolId": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolA19BA9C3Ref"}}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/GraphQLAPI/Resource"}}, "amplifyDataGraphQLAPITransformerSchemaFF50A789": {"Type": "AWS::AppSync::GraphQLSchema", "Properties": {"ApiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "DefinitionS3Location": {"Fn::Sub": "s3://cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}/6614acf21da877481fe2119444d7f87a06f93eb4fb0b938caaa77f109f816e43.graphql"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/GraphQLAPI/TransformerSchema"}}, "amplifyDataGraphQLAPIDefaultApiKey1C8ED374": {"Type": "AWS::AppSync::Api<PERSON>ey", "Properties": {"ApiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "Expires": **********}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/amplifyData/GraphQLAPI/DefaultApiKey"}}, "amplifyDataGraphQLAPINONEDS684BF699": {"Type": "AWS::AppSync::DataSource", "Properties": {"ApiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "Description": "None Data Source for Pipeline functions", "Name": "NONE_DS", "Type": "NONE"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/GraphQLAPI/NONE_DS/Resource"}}, "amplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResource86290833": {"Type": "AWS::CloudFormation::Stack", "Properties": {"Parameters": {"referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}}, "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "amplify:friendly-name", "Value": "amplifyData"}, {"Key": "created-by", "Value": "amplify"}], "TemplateURL": {"Fn::Join": ["", ["https://s3.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/dd481eb31c5caa20140d9d422348dbdab98d913903c1e918eafdb4531b770f5b.json"]]}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyTableManager.NestedStack/AmplifyTableManager.NestedStackResource", "aws:asset:path": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManager64DE7FE5.nested.template.json", "aws:asset:property": "TemplateURL"}}, "amplifyDataUserProfileNestedStackUserProfileNestedStackResource1B3713C5": {"Type": "AWS::CloudFormation::Stack", "Properties": {"Parameters": {"DynamoDBModelTableReadIOPS": {"Ref": "DynamoDBModelTableReadIOPS"}, "DynamoDBModelTableWriteIOPS": {"Ref": "DynamoDBModelTableWriteIOPS"}, "DynamoDBBillingMode": {"Ref": "DynamoDBBillingMode"}, "DynamoDBEnablePointInTimeRecovery": {"Ref": "DynamoDBEnablePointInTimeRecovery"}, "DynamoDBEnableServerSideEncryption": {"Ref": "DynamoDBEnableServerSideEncryption"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916OutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableCA8BFB67": {"Fn::GetAtt": ["amplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResource86290833", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName": {"Fn::GetAtt": ["amplifyDataGraphQLAPINONEDS684BF699", "Name"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}}, "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "amplify:friendly-name", "Value": "amplifyData"}, {"Key": "created-by", "Value": "amplify"}], "TemplateURL": {"Fn::Join": ["", ["https://s3.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/ad5b215a6c21dd88a6acdc93b3d118a21f09feba2cf9df993ce9e4b1a429aad4.json"]]}}, "DependsOn": ["amplifyDataGraphQLAPITransformerSchemaFF50A789"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/UserProfile.NestedStack/UserProfile.NestedStackResource", "aws:asset:path": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataUserProfile8FBF43E3.nested.template.json", "aws:asset:property": "TemplateURL"}}, "amplifyDataConnectionRequestNestedStackConnectionRequestNestedStackResource7B6F7DDC": {"Type": "AWS::CloudFormation::Stack", "Properties": {"Parameters": {"DynamoDBModelTableReadIOPS": {"Ref": "DynamoDBModelTableReadIOPS"}, "DynamoDBModelTableWriteIOPS": {"Ref": "DynamoDBModelTableWriteIOPS"}, "DynamoDBBillingMode": {"Ref": "DynamoDBBillingMode"}, "DynamoDBEnablePointInTimeRecovery": {"Ref": "DynamoDBEnablePointInTimeRecovery"}, "DynamoDBEnableServerSideEncryption": {"Ref": "DynamoDBEnableServerSideEncryption"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916OutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableCA8BFB67": {"Fn::GetAtt": ["amplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResource86290833", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName": {"Fn::GetAtt": ["amplifyDataGraphQLAPINONEDS684BF699", "Name"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}}, "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "amplify:friendly-name", "Value": "amplifyData"}, {"Key": "created-by", "Value": "amplify"}], "TemplateURL": {"Fn::Join": ["", ["https://s3.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/efec2b1b3967e7146f0451460b9881ff86048864fe6b8861196a490317bade5c.json"]]}}, "DependsOn": ["amplifyDataGraphQLAPITransformerSchemaFF50A789"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/ConnectionRequest.NestedStack/ConnectionRequest.NestedStackResource", "aws:asset:path": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataConnectionRequest74E4C2AA.nested.template.json", "aws:asset:property": "TemplateURL"}}, "amplifyDataConnectionNestedStackConnectionNestedStackResource0AD3F46E": {"Type": "AWS::CloudFormation::Stack", "Properties": {"Parameters": {"DynamoDBModelTableReadIOPS": {"Ref": "DynamoDBModelTableReadIOPS"}, "DynamoDBModelTableWriteIOPS": {"Ref": "DynamoDBModelTableWriteIOPS"}, "DynamoDBBillingMode": {"Ref": "DynamoDBBillingMode"}, "DynamoDBEnablePointInTimeRecovery": {"Ref": "DynamoDBEnablePointInTimeRecovery"}, "DynamoDBEnableServerSideEncryption": {"Ref": "DynamoDBEnableServerSideEncryption"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResourceD1BED916OutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableCA8BFB67": {"Fn::GetAtt": ["amplifyDataAmplifyTableManagerNestedStackAmplifyTableManagerNestedStackResource86290833", "Outputs.amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyTableManagerTableManagerCustomProviderframeworkonEventC3346269Arn"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPINONEDSE07947DEName": {"Fn::GetAtt": ["amplifyDataGraphQLAPINONEDS684BF699", "Name"]}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef": {"Ref": "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef"}}, "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "amplify:friendly-name", "Value": "amplifyData"}, {"Key": "created-by", "Value": "amplify"}], "TemplateURL": {"Fn::Join": ["", ["https://s3.", {"Ref": "AWS::Region"}, ".", {"Ref": "AWS::URLSuffix"}, "/", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/f289c354e1762737c14d2fdbe5a20884282376df74be46dd29d0c77a01d50c63.json"]]}}, "DependsOn": ["amplifyDataGraphQLAPITransformerSchemaFF50A789"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/Connection.NestedStack/Connection.NestedStackResource", "aws:asset:path": "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataConnection840F0D33.nested.template.json", "aws:asset:property": "TemplateURL"}}, "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA": {"Type": "AWS::S3::<PERSON><PERSON>", "Properties": {"CorsConfiguration": {"CorsRules": [{"AllowedHeaders": ["*"], "AllowedMethods": ["GET", "HEAD"], "AllowedOrigins": [{"Fn::Join": ["", ["https://", {"Ref": "AWS::Region"}, ".console.aws.amazon.com/amplify"]]}]}]}, "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "amplify:friendly-name", "Value": "amplifyData"}, {"Key": "aws-cdk:auto-delete-objects", "Value": "true"}, {"Key": "aws-cdk:cr-owned:912d1fbd", "Value": "true"}, {"Key": "created-by", "Value": "amplify"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/Resource"}}, "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucketPolicyF1C1C548": {"Type": "AWS::S3::BucketPolicy", "Properties": {"Bucket": {"Ref": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA"}, "PolicyDocument": {"Statement": [{"Action": ["s3:PutBucketPolicy", "s3:GetBucket*", "s3:List*", "s3:DeleteObject*"], "Effect": "Allow", "Principal": {"AWS": {"Fn::GetAtt": ["CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092", "<PERSON><PERSON>"]}}, "Resource": [{"Fn::GetAtt": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA", "<PERSON><PERSON>"]}, "/*"]]}]}], "Version": "2012-10-17"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/Policy/Resource"}}, "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucketAutoDeleteObjectsCustomResource437F26F5": {"Type": "Custom::S3AutoDeleteObjects", "Properties": {"ServiceToken": {"Fn::GetAtt": ["CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F", "<PERSON><PERSON>"]}, "BucketName": {"Ref": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA"}}, "DependsOn": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucketPolicyF1C1C548"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsBucket/AutoDeleteObjectsCustomResource/Default"}}, "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentAwsCliLayerE322F905": {"Type": "AWS::Lambda::LayerVersion", "Properties": {"Content": {"S3Bucket": {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "S3Key": "88697bc081d326ba958c89eda97ef89dbab19e586716d7cb9401f041d1b93269.zip"}, "Description": "/opt/awscli/aws"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/AwsCliLayer/Resource", "aws:asset:path": "asset.88697bc081d326ba958c89eda97ef89dbab19e586716d7cb9401f041d1b93269.zip", "aws:asset:is-bundled": false, "aws:asset:property": "Content"}}, "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentCustomResource1536MiB21775929": {"Type": "Custom::CDKBucketDeployment", "Properties": {"ServiceToken": {"Fn::GetAtt": ["CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBC5D8AB21", "<PERSON><PERSON>"]}, "SourceBucketNames": [{"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}], "SourceObjectKeys": ["c85cfebe588b3c5c552ff4b95814b8112db97e42ba3b8da09f1fda5c1752bbd6.zip"], "SourceMarkers": [{}], "DestinationBucketName": {"Ref": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA"}, "Prune": true, "OutputObjectKeys": true, "DestinationBucketArn": {"Fn::GetAtt": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA", "<PERSON><PERSON>"]}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/amplifyData/AmplifyCodegenAssets/AmplifyCodegenAssetsDeployment/CustomResource-1536MiB/Default"}}, "CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Version": "2012-10-17", "Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}]}, "ManagedPolicyArns": [{"Fn::Sub": "arn:${AWS::Partition}:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/Custom::S3AutoDeleteObjectsCustomResourceProvider/Role"}}, "CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "S3Key": "faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6.zip"}, "Timeout": 900, "MemorySize": 128, "Handler": "index.handler", "Role": {"Fn::GetAtt": ["CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092", "<PERSON><PERSON>"]}, "Runtime": {"Fn::FindInMap": ["LatestNodeRuntimeMap", {"Ref": "AWS::Region"}, "value"]}, "Description": {"Fn::Join": ["", ["Lambda function for auto-deleting objects in ", {"Ref": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA"}, " S3 bucket."]]}}, "DependsOn": ["CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/Custom::S3AutoDeleteObjectsCustomResourceProvider/Handler", "aws:asset:path": "asset.faa95a81ae7d7373f3e1f242268f904eb748d8d0fdd306e8a6fe515a1905a7d6", "aws:asset:property": "Code"}}, "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleA41FC8C2": {"Type": "AWS::IAM::Role", "Properties": {"AssumeRolePolicyDocument": {"Statement": [{"Action": "sts:<PERSON><PERSON>Role", "Effect": "Allow", "Principal": {"Service": "lambda.amazonaws.com"}}], "Version": "2012-10-17"}, "ManagedPolicyArns": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"]]}], "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "created-by", "Value": "amplify"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/ServiceRole/Resource"}}, "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleDefaultPolicyFF1C635B": {"Type": "AWS::IAM::Policy", "Properties": {"PolicyDocument": {"Statement": [{"Action": ["s3:GetObject*", "s3:GetBucket*", "s3:List*"], "Effect": "Allow", "Resource": [{"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":s3:::", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}]]}, {"Fn::Join": ["", ["arn:", {"Ref": "AWS::Partition"}, ":s3:::", {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "/*"]]}]}, {"Action": ["s3:GetObject*", "s3:GetBucket*", "s3:List*", "s3:DeleteObject*", "s3:PutObject", "s3:PutObjectLegalHold", "s3:PutObjectRetention", "s3:PutObjectTagging", "s3:PutObjectVersionTagging", "s3:Abort*"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsBucket9CCB4ACA", "<PERSON><PERSON>"]}, "/*"]]}]}, {"Action": ["s3:GetObject*", "s3:GetBucket*", "s3:List*", "s3:DeleteObject*", "s3:PutObject", "s3:PutObjectLegalHold", "s3:PutObjectRetention", "s3:PutObjectTagging", "s3:PutObjectVersionTagging", "s3:Abort*"], "Effect": "Allow", "Resource": [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, "/*"]]}]}], "Version": "2012-10-17"}, "PolicyName": "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleDefaultPolicyFF1C635B", "Roles": [{"Ref": "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleA41FC8C2"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/ServiceRole/DefaultPolicy/Resource"}}, "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBC5D8AB21": {"Type": "AWS::Lambda::Function", "Properties": {"Code": {"S3Bucket": {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "S3Key": "4fe0aba5e672b596d0f72505a9eec502f98d46906bb30fae2511fbdc1df4956f.zip"}, "Environment": {"Variables": {"AWS_CA_BUNDLE": "/etc/pki/ca-trust/extracted/pem/tls-ca-bundle.pem"}}, "Handler": "index.handler", "Layers": [{"Ref": "amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentAwsCliLayerE322F905"}], "MemorySize": 1536, "Role": {"Fn::GetAtt": ["CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleA41FC8C2", "<PERSON><PERSON>"]}, "Runtime": "python3.11", "Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "created-by", "Value": "amplify"}], "Timeout": 900}, "DependsOn": ["CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleDefaultPolicyFF1C635B", "CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBServiceRoleA41FC8C2"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/Custom::CDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiB/Resource", "aws:asset:path": "asset.4fe0aba5e672b596d0f72505a9eec502f98d46906bb30fae2511fbdc1df4956f", "aws:asset:is-bundled": false, "aws:asset:property": "Code"}}, "modelIntrospectionSchemaBucketF566B665": {"Type": "AWS::S3::<PERSON><PERSON>", "Properties": {"Tags": [{"Key": "amplify:deployment-type", "Value": "sandbox"}, {"Key": "aws-cdk:auto-delete-objects", "Value": "true"}, {"Key": "aws-cdk:cr-owned:3d1d070e", "Value": "true"}, {"Key": "created-by", "Value": "amplify"}]}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucket/Resource"}}, "modelIntrospectionSchemaBucketPolicy4DAB0D15": {"Type": "AWS::S3::BucketPolicy", "Properties": {"Bucket": {"Ref": "modelIntrospectionSchemaBucketF566B665"}, "PolicyDocument": {"Statement": [{"Action": "s3:*", "Condition": {"Bool": {"aws:SecureTransport": "false"}}, "Effect": "<PERSON><PERSON>", "Principal": {"AWS": "*"}, "Resource": [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, "/*"]]}]}, {"Action": ["s3:PutBucketPolicy", "s3:GetBucket*", "s3:List*", "s3:DeleteObject*"], "Effect": "Allow", "Principal": {"AWS": {"Fn::GetAtt": ["CustomS3AutoDeleteObjectsCustomResourceProviderRole3B1BD092", "<PERSON><PERSON>"]}}, "Resource": [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, {"Fn::Join": ["", [{"Fn::GetAtt": ["modelIntrospectionSchemaBucketF566B665", "<PERSON><PERSON>"]}, "/*"]]}]}], "Version": "2012-10-17"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucket/Policy/Resource"}}, "modelIntrospectionSchemaBucketAutoDeleteObjectsCustomResourceFE57309F": {"Type": "Custom::S3AutoDeleteObjects", "Properties": {"ServiceToken": {"Fn::GetAtt": ["CustomS3AutoDeleteObjectsCustomResourceProviderHandler9D90184F", "<PERSON><PERSON>"]}, "BucketName": {"Ref": "modelIntrospectionSchemaBucketF566B665"}}, "DependsOn": ["modelIntrospectionSchemaBucketPolicy4DAB0D15"], "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucket/AutoDeleteObjectsCustomResource/Default"}}, "modelIntrospectionSchemaBucketDeploymentAwsCliLayer13C432F7": {"Type": "AWS::Lambda::LayerVersion", "Properties": {"Content": {"S3Bucket": {"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}, "S3Key": "88697bc081d326ba958c89eda97ef89dbab19e586716d7cb9401f041d1b93269.zip"}, "Description": "/opt/awscli/aws"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucketDeployment/AwsCliLayer/Resource", "aws:asset:path": "asset.88697bc081d326ba958c89eda97ef89dbab19e586716d7cb9401f041d1b93269.zip", "aws:asset:is-bundled": false, "aws:asset:property": "Content"}}, "modelIntrospectionSchemaBucketDeploymentCustomResource1536MiB104B97EC": {"Type": "Custom::CDKBucketDeployment", "Properties": {"ServiceToken": {"Fn::GetAtt": ["CustomCDKBucketDeployment8693BB64968944B69AAFB0CC9EB8756C1536MiBC5D8AB21", "<PERSON><PERSON>"]}, "SourceBucketNames": [{"Fn::Sub": "cdk-hnb659fds-assets-${AWS::AccountId}-${AWS::Region}"}], "SourceObjectKeys": ["87fdbda2f9b5fbad8a8f0ee6c140fac9e53bda05220c7b8fc3db0af5eabebf94.zip"], "SourceMarkers": [{}], "DestinationBucketName": {"Ref": "modelIntrospectionSchemaBucketF566B665"}, "Prune": true, "OutputObjectKeys": true}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/modelIntrospectionSchemaBucketDeployment/CustomResource-1536MiB/Default"}}, "AMPLIFYDATAGRAPHQLENDPOINTParameter1C2CBB16": {"Type": "AWS::SSM::Parameter", "Properties": {"Name": "/amplify/resource_reference/RootsConnect/nkannaiyan-sandbox-183ad0d0d2/AMPLIFY_DATA_GRAPHQL_ENDPOINT", "Tags": {"amplify:deployment-type": "sandbox", "created-by": "amplify"}, "Type": "String", "Value": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "GraphQLUrl"]}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-n<PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/AMPLIFY_DATA_GRAPHQL_ENDPOINTParameter/Resource"}}, "AMPLIFYDATAMODELINTROSPECTIONSCHEMABUCKETNAMEParameter47BF4F44": {"Type": "AWS::SSM::Parameter", "Properties": {"Name": "/amplify/resource_reference/RootsConnect/nkan<PERSON><PERSON>-sandbox-183ad0d0d2/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_BUCKET_NAME", "Tags": {"amplify:deployment-type": "sandbox", "created-by": "amplify"}, "Type": "String", "Value": {"Ref": "modelIntrospectionSchemaBucketF566B665"}}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-<PERSON><PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_BUCKET_NAMEParameter/Resource"}}, "AMPLIFYDATAMODELINTROSPECTIONSCHEMAKEYParameterB6AEAE8A": {"Type": "AWS::SSM::Parameter", "Properties": {"Name": "/amplify/resource_reference/RootsConnect/nkan<PERSON><PERSON>-sandbox-183ad0d0d2/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_KEY", "Tags": {"amplify:deployment-type": "sandbox", "created-by": "amplify"}, "Type": "String", "Value": "modelIntrospectionSchema.json"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-<PERSON><PERSON><PERSON><PERSON>-sandbox-183ad0d0d2/data/AMPLIFY_DATA_MODEL_INTROSPECTION_SCHEMA_KEYParameter/Resource"}}, "AMPLIFYDATADEFAULTNAMEParameterE7C23CC4": {"Type": "AWS::SSM::Parameter", "Properties": {"Name": "/amplify/resource_reference/RootsConnect/nkannaiyan-sandbox-183ad0d0d2/AMPLIFY_DATA_DEFAULT_NAME", "Tags": {"amplify:deployment-type": "sandbox", "created-by": "amplify"}, "Type": "String", "Value": "amplifyData"}, "UpdateReplacePolicy": "Delete", "DeletionPolicy": "Delete", "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkan<PERSON>yan-sandbox-183ad0d0d2/data/AMPLIFY_DATA_DEFAULT_NAMEParameter/Resource"}}, "CDKMetadata": {"Type": "AWS::CDK::Metadata", "Properties": {"Analytics": "v2:deflate64:H4sIAAAAAAAA/1VRTXPCIBD9Ld6RNnppj35Me6h1rJnpNbOSraIEUpboZJj890IwWk/73tvl8VgmPHt55dkILjQW5Wms5I77NZLDMncgTmyLZBorkIWJwkNdU6sF9+8W6sOvmtVyDoRs8aN75WsVlH8sFwesIApB/8CWrY3GJTjIk2lo3FnHaMr9vBEndLFzRalsjJKivctXnkjMEE8XvsRambZC7XhqLW8CAyJ0xGexdExBtSuB++C4ghbtN1qSRrNc6r1CZ/Rbo4WLyg2E0QF3TELF/dao/hF9vUdMKCSiMJM7Gyw3YKFCh7bvD6TrIk2bXjTkTHXbd3QdcMpaqJizCB8hlOSzCy2U7KOz/knBZR8uevCP5DN8Wq8/+G+sOcsSbVqdNiXyIz2dJ1OeZfx5dCQpx7bRTlbIt6n+AbzZ/0ksAgAA"}, "Metadata": {"aws:cdk:path": "amplify-RootsConnect-nkannaiyan-sandbox-183ad0d0d2/data/CDKMetadata/Default"}, "Condition": "CDKMetadataAvailable"}}, "Parameters": {"DynamoDBModelTableReadIOPS": {"Type": "Number", "Default": 5, "Description": "The number of read IOPS the table should support."}, "DynamoDBModelTableWriteIOPS": {"Type": "Number", "Default": 5, "Description": "The number of write IOPS the table should support."}, "DynamoDBBillingMode": {"Type": "String", "Default": "PAY_PER_REQUEST", "AllowedValues": ["PAY_PER_REQUEST", "PROVISIONED"], "Description": "Configure @model types to create DynamoDB tables with PAY_PER_REQUEST or PROVISIONED billing modes."}, "DynamoDBEnablePointInTimeRecovery": {"Type": "String", "Default": "false", "AllowedValues": ["true", "false"], "Description": "Whether to enable Point in Time Recovery on the table."}, "DynamoDBEnableServerSideEncryption": {"Type": "String", "Default": "true", "AllowedValues": ["true", "false"], "Description": "Enable server side encryption powered by KMS."}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthUserPoolA19BA9C3Ref": {"Type": "String"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthauthenticatedUserRole2718EEF0Ref": {"Type": "String"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthunauthenticatedUserRole4D21354DRef": {"Type": "String"}, "referencetoamplifyRootsConnectnkannaiyansandbox183ad0d0d2authNestedStackauthNestedStackResourceA2EC464BOutputsamplifyRootsConnectnkannaiyansandbox183ad0d0d2authamplifyAuthIdentityPoolB72CC9CFRef": {"Type": "String"}}, "Mappings": {"LatestNodeRuntimeMap": {"af-south-1": {"value": "nodejs20.x"}, "ap-east-1": {"value": "nodejs20.x"}, "ap-northeast-1": {"value": "nodejs20.x"}, "ap-northeast-2": {"value": "nodejs20.x"}, "ap-northeast-3": {"value": "nodejs20.x"}, "ap-south-1": {"value": "nodejs20.x"}, "ap-south-2": {"value": "nodejs20.x"}, "ap-southeast-1": {"value": "nodejs20.x"}, "ap-southeast-2": {"value": "nodejs20.x"}, "ap-southeast-3": {"value": "nodejs20.x"}, "ap-southeast-4": {"value": "nodejs20.x"}, "ap-southeast-5": {"value": "nodejs20.x"}, "ap-southeast-7": {"value": "nodejs20.x"}, "ca-central-1": {"value": "nodejs20.x"}, "ca-west-1": {"value": "nodejs20.x"}, "cn-north-1": {"value": "nodejs20.x"}, "cn-northwest-1": {"value": "nodejs20.x"}, "eu-central-1": {"value": "nodejs20.x"}, "eu-central-2": {"value": "nodejs20.x"}, "eu-isoe-west-1": {"value": "nodejs18.x"}, "eu-north-1": {"value": "nodejs20.x"}, "eu-south-1": {"value": "nodejs20.x"}, "eu-south-2": {"value": "nodejs20.x"}, "eu-west-1": {"value": "nodejs20.x"}, "eu-west-2": {"value": "nodejs20.x"}, "eu-west-3": {"value": "nodejs20.x"}, "il-central-1": {"value": "nodejs20.x"}, "me-central-1": {"value": "nodejs20.x"}, "me-south-1": {"value": "nodejs20.x"}, "mx-central-1": {"value": "nodejs20.x"}, "sa-east-1": {"value": "nodejs20.x"}, "us-east-1": {"value": "nodejs20.x"}, "us-east-2": {"value": "nodejs20.x"}, "us-gov-east-1": {"value": "nodejs20.x"}, "us-gov-west-1": {"value": "nodejs20.x"}, "us-iso-east-1": {"value": "nodejs18.x"}, "us-iso-west-1": {"value": "nodejs18.x"}, "us-isob-east-1": {"value": "nodejs18.x"}, "us-west-1": {"value": "nodejs20.x"}, "us-west-2": {"value": "nodejs20.x"}}}, "Conditions": {"CDKMetadataAvailable": {"Fn::Or": [{"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "af-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-northeast-3"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-south-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-3"]}]}, {"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "ap-southeast-4"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ca-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "ca-west-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "cn-north-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "cn-northwest-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-central-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-north-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-south-2"]}]}, {"Fn::Or": [{"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "eu-west-3"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "il-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "me-central-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "me-south-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "sa-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-east-1"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-east-2"]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-west-1"]}]}, {"Fn::Equals": [{"Ref": "AWS::Region"}, "us-west-2"]}]}}, "Outputs": {"amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2ApiId": {"Value": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "ApiId"]}}, "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPID56204F2GraphQLUrl": {"Value": {"Fn::GetAtt": ["amplifyDataGraphQLAPI42A6FA33", "GraphQLUrl"]}}, "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentCustomResource1536MiB60279CB2DestinationBucketArn": {"Value": {"Fn::GetAtt": ["amplifyDataAmplifyCodegenAssetsAmplifyCodegenAssetsDeploymentCustomResource1536MiB21775929", "DestinationBucketArn"]}}, "amplifyRootsConnectnkannaiyansandbox183ad0d0d2dataamplifyDataGraphQLAPIDefaultApiKey9D3276BBApiKey": {"Value": {"Fn::GetAtt": ["amplifyDataGraphQLAPIDefaultApiKey1C8ED374", "<PERSON><PERSON><PERSON><PERSON>"]}}}}