{"version": 3, "file": "import-table.js", "sourceRoot": "", "sources": ["../../../../src/resources/amplify-dynamodb-table/amplify-table-manager-lambda/import-table.ts"], "names": [], "mappings": ";;;;;;AAAA,oEAAqC;AACrC,8DAAsI;AAO/H,MAAM,WAAW,GAAG,KAAK,EAAE,gBAAyC,EAAsD,EAAE;IACjI,MAAM,SAAS,GAAG,IAAI,0BAAQ,EAAE,CAAC;IACjC,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IAC/C,OAAO,CAAC,GAAG,CAAC,mCAAmC,gBAAgB,CAAC,SAAS,EAAE,CAAC,CAAC;IAC7E,MAAM,mBAAmB,GAAG,MAAM,SAAS,CAAC,aAAa,CAAC,EAAE,SAAS,EAAE,gBAAgB,CAAC,SAAS,EAAE,CAAC,CAAC;IACrG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE;QAC9B,MAAM,IAAI,KAAK,CAAC,kBAAkB,gBAAgB,CAAC,SAAS,YAAY,CAAC,CAAC;KAC3E;IACD,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,mBAAmB,CAAC,CAAC;IAC1D,MAAM,uBAAuB,GAAG,IAAA,kCAA0B,EAAC,gBAAgB,CAAC,CAAC;IAC7E,MAAM,uBAAuB,GAAG,IAAA,4CAAoC,EAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;IAChG,IAAA,uCAA+B,EAAC,uBAAuB,EAAE,uBAAuB,CAAC,CAAC;IAClF,MAAM,MAAM,GAAG;QACb,kBAAkB,EAAE,mBAAmB,CAAC,KAAK,CAAC,SAAS;QACvD,IAAI,EAAE;YACJ,QAAQ,EAAE,mBAAmB,CAAC,KAAK,CAAC,QAAQ;YAC5C,cAAc,EAAE,mBAAmB,CAAC,KAAK,CAAC,eAAe;YACzD,SAAS,EAAE,mBAAmB,CAAC,KAAK,CAAC,SAAS;SAC/C;KACF,CAAC;IACF,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,MAAM,CAAC,CAAC;IAC1C,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAtBW,QAAA,WAAW,eAsBtB;AAQK,MAAM,+BAA+B,GAAG,CAC7C,uBAAkD,EAClD,uBAAkD,EAC5C,EAAE;IACR,MAAM,MAAM,GAAa,EAAE,CAAC;IAG5B,MAAM,WAAW,GAAG,CAClB,YAAoB,EACpB,QAAoE,EACpE,QAAoE,EAC9D,EAAE;QACR,IAAI,CAAC,IAAA,wBAAO,EAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE;YAChC,MAAM,CAAC,IAAI,CACT,GAAG,YAAY,wDAAwD,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,eAAe,IAAI,CAAC,SAAS,CAC1H,QAAQ,CACT,EAAE,CACJ,CAAC;SACH;IACH,CAAC,CAAC;IAEF,MAAM,gCAAgC,GAAG,IAAA,+BAAuB,EAAC,uBAAuB,CAAC,CAAC;IAC1F,MAAM,gCAAgC,GAAG,IAAA,+BAAuB,EAAC,uBAAuB,CAAC,CAAC;IAG1F,WAAW,CACT,sBAAsB,EACtB,gCAAgC,CAAC,oBAAoB,EACrD,gCAAgC,CAAC,oBAAoB,CACtD,CAAC;IACF,WAAW,CAAC,WAAW,EAAE,gCAAgC,CAAC,SAAS,EAAE,gCAAgC,CAAC,SAAS,CAAC,CAAC;IACjH,WAAW,CACT,wBAAwB,EACxB,gCAAgC,CAAC,sBAAsB,EACvD,gCAAgC,CAAC,sBAAsB,CACxD,CAAC;IACF,WAAW,CACT,oBAAoB,EACpB,gCAAgC,CAAC,kBAAkB,EACnD,gCAAgC,CAAC,kBAAkB,CACpD,CAAC;IACF,WAAW,CACT,uBAAuB,EACvB,gCAAgC,CAAC,qBAAqB,EACtD,gCAAgC,CAAC,qBAAqB,CACvD,CAAC;IACF,WAAW,CACT,qBAAqB,EACrB,gCAAgC,CAAC,mBAAmB,EACpD,gCAAgC,CAAC,mBAAmB,CACrD,CAAC;IACF,WAAW,CAAC,gBAAgB,EAAE,gCAAgC,CAAC,cAAc,EAAE,gCAAgC,CAAC,cAAc,CAAC,CAAC;IAChI,WAAW,CACT,2BAA2B,EAC3B,gCAAgC,CAAC,yBAAyB,EAC1D,gCAAgC,CAAC,yBAAyB,CAC3D,CAAC;IAEF,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;QACrB,MAAM,IAAI,KAAK,CAAC,2EAA2E,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KACjH;AACH,CAAC,CAAC;AA7DW,QAAA,+BAA+B,mCA6D1C;AAsBK,MAAM,0BAA0B,GAAG,CAAC,gBAAyC,EAA6B,EAAE;IACjH,OAAO;QACL,oBAAoB,EAAE,+BAA+B,CAAC,gBAAgB,CAAC;QACvE,SAAS,EAAE,oBAAoB,CAAC,gBAAgB,CAAC;QACjD,sBAAsB,EAAE,iCAAiC,CAAC,gBAAgB,CAAC;QAC3E,kBAAkB,EAAE,6BAA6B,CAAC,gBAAgB,CAAC;QACnE,qBAAqB,EAAE,gCAAgC,CAAC,gBAAgB,CAAC;QACzE,mBAAmB,EAAE,8BAA8B,CAAC,gBAAgB,CAAC;QACrE,cAAc,EAAE,yBAAyB,CAAC,gBAAgB,CAAC;QAC3D,yBAAyB,EAAE,oCAAoC,CAAC,gBAAgB,CAAC;KAClF,CAAC;AACJ,CAAC,CAAC;AAXW,QAAA,0BAA0B,8BAWrC;AAEF,MAAM,+BAA+B,GAAG,CAAC,gBAAyC,EAAqD,EAAE;IACvI,OAAO,gBAAgB,CAAC,oBAAoB,CAAC;AAC/C,CAAC,CAAC;AAEF,MAAM,oBAAoB,GAAG,CAAC,gBAAyC,EAA0C,EAAE;IACjH,OAAO,gBAAgB,CAAC,SAAS,CAAC;AACpC,CAAC,CAAC;AAEF,MAAM,iCAAiC,GAAG,CACxC,gBAAyC,EACY,EAAE;;IACvD,OAAO,MAAA,gBAAgB,CAAC,sBAAsB,0CAAE,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;QAC1D,IAAI,gBAAgB,CAAC,WAAW,KAAK,iBAAiB,EAAE;YAGtD,OAAO;gBACL,GAAG,GAAG;gBACN,qBAAqB,EAAE,GAAG,CAAC,qBAAqB;oBAC9C,CAAC,CAAC;wBACE,iBAAiB,EAAE,CAAC;wBACpB,kBAAkB,EAAE,CAAC;qBACtB;oBACH,CAAC,CAAC,SAAS;aACd,CAAC;SACH;QACD,OAAO,GAAG,CAAC;IACb,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AAEF,MAAM,6BAA6B,GAAG,CAAC,gBAAyC,EAAmD,EAAE;IACnI,OAAO;QACL,WAAW,EAAE,gBAAgB,CAAC,WAAW;KAC1C,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,8BAA8B,GAAG,CAAC,gBAAyC,EAAoD,EAAE;IACrI,OAAO,gBAAgB,CAAC,mBAAmB,CAAC;AAC9C,CAAC,CAAC;AAEF,MAAM,gCAAgC,GAAG,CACvC,gBAAyC,EACW,EAAE;IACtD,OAAO,gBAAgB,CAAC,qBAAqB,IAAI,EAAE,iBAAiB,EAAE,CAAC,EAAE,kBAAkB,EAAE,CAAC,EAAE,CAAC;AACnG,CAAC,CAAC;AAEF,MAAM,yBAAyB,GAAG,CAAC,gBAAyC,EAA+C,EAAE;IAC3H,OAAO,gBAAgB,CAAC,gBAAgB,IAAI,gBAAgB,CAAC,gBAAgB,CAAC,OAAO;QACnF,CAAC,CAAC;YACE,OAAO,EAAE,gBAAgB,CAAC,gBAAgB,CAAC,OAAO,IAAI,KAAK;YAC3D,MAAM,EAAE,SAAS;SAClB;QACH,CAAC,CAAC,SAAS,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,oCAAoC,GAAG,CAC3C,gBAAyC,EACe,EAAE;IAC1D,OAAO,gBAAgB,CAAC,yBAAyB,IAAI,KAAK,CAAC;AAC7D,CAAC,CAAC;AAOK,MAAM,oCAAoC,GAAG,CAAC,aAA+B,EAA6B,EAAE;IACjH,OAAO;QACL,oBAAoB,EAAE,oCAAoC,CAAC,aAAa,CAAC;QACzE,SAAS,EAAE,yBAAyB,CAAC,aAAa,CAAC;QACnD,sBAAsB,EAAE,sCAAsC,CAAC,aAAa,CAAC;QAC7E,kBAAkB,EAAE,kCAAkC,CAAC,aAAa,CAAC;QACrE,qBAAqB,EAAE,qCAAqC,CAAC,aAAa,CAAC;QAC3E,mBAAmB,EAAE,mCAAmC,CAAC,aAAa,CAAC;QACvE,cAAc,EAAE,8BAA8B,CAAC,aAAa,CAAC;QAC7D,yBAAyB,EAAE,yCAAyC,CAAC,aAAa,CAAC;KACpF,CAAC;AACJ,CAAC,CAAC;AAXW,QAAA,oCAAoC,wCAW/C;AAEF,MAAM,oCAAoC,GAAG,CAAC,aAA+B,EAAqD,EAAE;;IAClI,OAAO,MAAA,aAAa,CAAC,oBAAoB,0CAAE,GAAG,CAAC,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;QACvE,aAAa,EAAE,mBAAmB,CAAC,aAAa;QAChD,aAAa,EAAE,mBAAmB,CAAC,aAAa;KACjD,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AAEF,MAAM,yBAAyB,GAAG,CAAC,aAA+B,EAA0C,EAAE;;IAC5G,OAAO,MAAA,aAAa,CAAC,SAAS,0CAAE,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;QAC5C,aAAa,EAAE,GAAG,CAAC,aAAa;QAChC,OAAO,EAAE,GAAG,CAAC,OAAO;KACrB,CAAC,CAAC,CAAC;AACN,CAAC,CAAC;AAEF,MAAM,sCAAsC,GAAG,CAAC,aAA+B,EAAuD,EAAE;;IACtI,OAAO,MAAA,aAAa,CAAC,sBAAsB,0CAAE,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;;QAAC,OAAA,CAAC;YACzD,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,SAAS,EAAE,MAAA,GAAG,CAAC,SAAS,0CAAE,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;gBACtC,aAAa,EAAE,GAAG,CAAC,aAAa;gBAChC,OAAO,EAAE,GAAG,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,kBAAkB,EAAE,GAAG,CAAC,kBAAkB;gBACxC,CAAC,CAAC;oBACE,mBAAmB,EAAE,GAAG,CAAC,kBAAkB,CAAC,mBAAmB;oBAC/D,oBAAoB,EAAE,GAAG,CAAC,kBAAkB,CAAC,oBAAoB;iBAClE;gBACH,CAAC,CAAC,SAAS;YACb,UAAU,EAAE,GAAG,CAAC,UAAU;gBACxB,CAAC,CAAC;oBACE,gBAAgB,EAAE,GAAG,CAAC,UAAU,CAAC,gBAAgB;oBACjD,cAAc,EAAE,GAAG,CAAC,UAAU,CAAC,cAAc;iBAC9C;gBACH,CAAC,CAAC,SAAS;YACb,qBAAqB,EAAE,GAAG,CAAC,qBAAqB;gBAC9C,CAAC,CAAC;oBACE,iBAAiB,EAAE,GAAG,CAAC,qBAAqB,CAAC,iBAAiB;oBAC9D,kBAAkB,EAAE,GAAG,CAAC,qBAAqB,CAAC,kBAAkB;iBACjE;gBACH,CAAC,CAAC,SAAS;SACd,CAAC,CAAA;KAAA,CAAC,CAAC;AACN,CAAC,CAAC;AAEF,MAAM,kCAAkC,GAAG,CAAC,aAA+B,EAAmD,EAAE;IAC9H,OAAO,aAAa,CAAC,kBAAkB;QACrC,CAAC,CAAC;YACE,WAAW,EAAE,aAAa,CAAC,kBAAkB,CAAC,WAAW;SAC1D;QACH,CAAC,CAAC,SAAS,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,qCAAqC,GAAG,CAAC,aAA+B,EAAsD,EAAE;IACpI,OAAO,aAAa,CAAC,qBAAqB;QACxC,CAAC,CAAC;YACE,iBAAiB,EAAE,aAAa,CAAC,qBAAqB,CAAC,iBAAiB;YACxE,kBAAkB,EAAE,aAAa,CAAC,qBAAqB,CAAC,kBAAkB;SAC3E;QACH,CAAC,CAAC,SAAS,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,mCAAmC,GAAG,CAAC,aAA+B,EAAoD,EAAE;;IAChI,OAAO,aAAa,CAAC,mBAAmB;QACtC,CAAC,CAAC;YACE,aAAa,EAAE,MAAA,aAAa,CAAC,mBAAmB,0CAAE,aAAa;YAC/D,cAAc,EAAE,MAAA,aAAa,CAAC,mBAAmB,0CAAE,cAAc;SAClE;QACH,CAAC,CAAC,SAAS,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,8BAA8B,GAAG,CAAC,aAA+B,EAA+C,EAAE;IACtH,OAAO,aAAa,CAAC,cAAc;QACjC,CAAC,CAAC;YACE,OAAO,EAAE,aAAa,CAAC,cAAc,CAAC,OAAO;SAC9C;QACH,CAAC,CAAC,SAAS,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,yCAAyC,GAAG,CAChD,aAA+B,EACyB,EAAE;IAC1D,OAAO,aAAa,CAAC,yBAAyB,CAAC;AACjD,CAAC,CAAC;AAaK,MAAM,uBAAuB,GAAG,CAAC,eAA0C,EAA6B,EAAE;IAC/G,MAAM,+BAA+B,GAA8B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,CAAC;IAC/G,IAAI,+BAA+B,CAAC,oBAAoB,EAAE;QAKxD,+BAA+B,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,eAEjE,OAAA,CAAC,MAAA,CAAC,CAAC,aAAa,mCAAI,EAAE,CAAC,CAAC,aAAa,CAAC,MAAA,CAAC,CAAC,aAAa,mCAAI,EAAE,CAAC,CAAA,EAAA,CAC7D,CAAC;KACH;IAGD,MAAM,aAAa,GAAG,CAAC,CAAmB,EAAE,CAAmB,EAAU,EAAE,eAEzE,OAAA,CAAC,MAAA,CAAC,CAAC,aAAa,mCAAI,EAAE,CAAC,CAAC,aAAa,CAAC,MAAA,CAAC,CAAC,aAAa,mCAAI,EAAE,CAAC,CAAA,EAAA,CAAC;IAE/D,IAAI,+BAA+B,CAAC,SAAS,EAAE;QAC7C,+BAA+B,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;KAC/D;IAGD,IAAI,+BAA+B,CAAC,sBAAsB,EAAE;QAC1D,+BAA+B,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,eAEnE,OAAA,CAAC,MAAA,CAAC,CAAC,SAAS,mCAAI,EAAE,CAAC,CAAC,aAAa,CAAC,MAAA,CAAC,CAAC,SAAS,mCAAI,EAAE,CAAC,CAAA,EAAA,CACrD,CAAC;QAGF,+BAA+B,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;YACrE,IAAI,GAAG,CAAC,SAAS,EAAE;gBACjB,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;aACnC;QACH,CAAC,CAAC,CAAC;KACJ;IAED,OAAO,+BAA+B,CAAC;AACzC,CAAC,CAAC;AAtCW,QAAA,uBAAuB,2BAsClC"}