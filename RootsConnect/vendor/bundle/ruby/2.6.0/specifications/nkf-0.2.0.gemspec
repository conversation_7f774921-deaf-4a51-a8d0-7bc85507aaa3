# -*- encoding: utf-8 -*-
# stub: nkf 0.2.0 ruby lib
# stub: ext/nkf/extconf.rb

Gem::Specification.new do |s|
  s.name = "nkf".freeze
  s.version = "0.2.0"

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.metadata = { "homepage_uri" => "https://github.com/ruby/nkf", "source_code_uri" => "https://github.com/ruby/nkf" } if s.respond_to? :metadata=
  s.require_paths = ["lib".freeze]
  s.authors = ["NARUSE Yui".freeze, "<PERSON>".freeze]
  s.bindir = "exe".freeze
  s.date = "2024-01-22"
  s.description = "Ruby extension for Network Kanji Filter".freeze
  s.email = ["<EMAIL>".freeze, "<EMAIL>".freeze]
  s.extensions = ["ext/nkf/extconf.rb".freeze]
  s.files = ["ext/nkf/extconf.rb".freeze]
  s.homepage = "https://github.com/ruby/nkf".freeze
  s.licenses = ["Ruby".freeze, "BSD-2-Clause".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.3.0".freeze)
  s.rubygems_version = "*******".freeze
  s.summary = "Ruby extension for Network Kanji Filter".freeze

  s.installed_by_version = "*******" if s.respond_to? :installed_by_version
end
