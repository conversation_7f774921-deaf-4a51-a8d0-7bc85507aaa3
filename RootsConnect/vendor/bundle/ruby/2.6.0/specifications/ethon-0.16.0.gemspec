# -*- encoding: utf-8 -*-
# stub: ethon 0.16.0 ruby lib

Gem::Specification.new do |s|
  s.name = "ethon".freeze
  s.version = "0.16.0"

  s.required_rubygems_version = Gem::Requirement.new(">= 1.3.6".freeze) if s.respond_to? :required_rubygems_version=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON>".freeze]
  s.date = "2022-11-04"
  s.description = "Very lightweight libcurl wrapper.".freeze
  s.email = ["<EMAIL>".freeze]
  s.homepage = "https://github.com/typhoeus/ethon".freeze
  s.licenses = ["MIT".freeze]
  s.rubygems_version = "*******".freeze
  s.summary = "Libcurl wrapper.".freeze

  s.installed_by_version = "*******" if s.respond_to? :installed_by_version

  if s.respond_to? :specification_version then
    s.specification_version = 4

    if Gem::Version.new(Gem::VERSION) >= Gem::Version.new('1.2.0') then
      s.add_runtime_dependency(%q<ffi>.freeze, [">= 1.15.0"])
    else
      s.add_dependency(%q<ffi>.freeze, [">= 1.15.0"])
    end
  else
    s.add_dependency(%q<ffi>.freeze, [">= 1.15.0"])
  end
end
