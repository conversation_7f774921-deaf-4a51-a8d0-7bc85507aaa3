# -*- encoding: utf-8 -*-
# stub: bigdecimal 3.2.2 ruby lib
# stub: ext/bigdecimal/extconf.rb

Gem::Specification.new do |s|
  s.name = "bigdecimal".freeze
  s.version = "3.2.2"

  s.required_rubygems_version = Gem::Requirement.new(">= 0".freeze) if s.respond_to? :required_rubygems_version=
  s.metadata = { "changelog_uri" => "https://github.com/ruby/bigdecimal/blob/master/CHANGES.md" } if s.respond_to? :metadata=
  s.require_paths = ["lib".freeze]
  s.authors = ["<PERSON><PERSON>".freeze, "<PERSON>".freeze, "Shige<PERSON>".freeze]
  s.date = "1980-01-02"
  s.description = "This library provides arbitrary-precision decimal floating-point number class.".freeze
  s.email = ["<EMAIL>".freeze]
  s.extensions = ["ext/bigdecimal/extconf.rb".freeze]
  s.files = ["ext/bigdecimal/extconf.rb".freeze]
  s.homepage = "https://github.com/ruby/bigdecimal".freeze
  s.licenses = ["Ruby".freeze, "BSD-2-Clause".freeze]
  s.required_ruby_version = Gem::Requirement.new(">= 2.5.0".freeze)
  s.rubygems_version = "*******".freeze
  s.summary = "Arbitrary-precision decimal floating-point number library.".freeze

  s.installed_by_version = "*******" if s.respond_to? :installed_by_version
end
