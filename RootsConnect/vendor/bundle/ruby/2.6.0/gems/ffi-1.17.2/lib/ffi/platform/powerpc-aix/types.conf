rbx.platform.typedef.UTF32Char = uint
rbx.platform.typedef.UniChar = ushort
rbx.platform.typedef.__cptr32 = string
rbx.platform.typedef.__cptr64 = ulong_long
rbx.platform.typedef.__long32_t = long
rbx.platform.typedef.__long64_t = int
rbx.platform.typedef.__ptr32 = pointer
rbx.platform.typedef.__ptr64 = ulong_long
rbx.platform.typedef.__ulong32_t = ulong
rbx.platform.typedef.__ulong64_t = uint
rbx.platform.typedef.aptx_t = ushort
rbx.platform.typedef.blkcnt32_t = int
rbx.platform.typedef.blkcnt64_t = ulong_long
rbx.platform.typedef.blkcnt_t = int
rbx.platform.typedef.blksize32_t = int
rbx.platform.typedef.blksize64_t = ulong_long
rbx.platform.typedef.blksize_t = int
rbx.platform.typedef.boolean_t = int
rbx.platform.typedef.caddr_t = string
rbx.platform.typedef.chan_t = int
rbx.platform.typedef.class_id_t = uint
rbx.platform.typedef.clock_t = int
rbx.platform.typedef.clockid_t = long_long
rbx.platform.typedef.cnt64_t = long_long
rbx.platform.typedef.cnt_t = short
rbx.platform.typedef.crid_t = int
rbx.platform.typedef.daddr32_t = int
rbx.platform.typedef.daddr64_t = long_long
rbx.platform.typedef.daddr_t = int
rbx.platform.typedef.dev32_t = uint
rbx.platform.typedef.dev64_t = ulong_long
rbx.platform.typedef.dev_t = uint
rbx.platform.typedef.esid_t = uint
rbx.platform.typedef.ext_t = int
rbx.platform.typedef.fpos64_t = long_long
rbx.platform.typedef.fpos_t = long
rbx.platform.typedef.fsblkcnt_t = ulong
rbx.platform.typedef.fsfilcnt_t = ulong
rbx.platform.typedef.gid_t = uint
rbx.platform.typedef.id_t = uint
rbx.platform.typedef.ino32_t = uint
rbx.platform.typedef.ino64_t = ulong_long
rbx.platform.typedef.ino_t = uint
rbx.platform.typedef.int16 = short
rbx.platform.typedef.int16_t = short
rbx.platform.typedef.int32 = int
rbx.platform.typedef.int32_t = int
rbx.platform.typedef.int32long64_t = int
rbx.platform.typedef.int64 = long_long
rbx.platform.typedef.int64_t = long_long
rbx.platform.typedef.int8 = char
rbx.platform.typedef.int8_t = char
rbx.platform.typedef.int_fast16_t = short
rbx.platform.typedef.int_fast32_t = int
rbx.platform.typedef.int_fast64_t = long_long
rbx.platform.typedef.int_fast8_t = char
rbx.platform.typedef.int_least16_t = short
rbx.platform.typedef.int_least32_t = int
rbx.platform.typedef.int_least64_t = long_long
rbx.platform.typedef.int_least8_t = char
rbx.platform.typedef.intfast_t = int
rbx.platform.typedef.intmax_t = long_long
rbx.platform.typedef.intptr_t = long
rbx.platform.typedef.key_t = int
rbx.platform.typedef.krpn_t = int
rbx.platform.typedef.kvmhandle_t = ulong
rbx.platform.typedef.kvmid_t = long
rbx.platform.typedef.kvpn_t = int
rbx.platform.typedef.level_t = int
rbx.platform.typedef.liobn_t = uint
rbx.platform.typedef.long32int64_t = long
rbx.platform.typedef.longlong_t = long_long
rbx.platform.typedef.mid_t = pointer
rbx.platform.typedef.mode_t = uint
rbx.platform.typedef.mtyp_t = long
rbx.platform.typedef.nlink_t = short
rbx.platform.typedef.off64_t = long_long
rbx.platform.typedef.off_t = long
rbx.platform.typedef.offset_t = long_long
rbx.platform.typedef.paddr_t = long
rbx.platform.typedef.pdtx_t = int
rbx.platform.typedef.pid32_t = int
rbx.platform.typedef.pid64_t = ulong_long
rbx.platform.typedef.pid_t = int
rbx.platform.typedef.pshift_t = ushort
rbx.platform.typedef.psize_t = long_long
rbx.platform.typedef.psx_t = short
rbx.platform.typedef.ptex_t = ulong
rbx.platform.typedef.pthread_key_t = uint
rbx.platform.typedef.pthread_t = uint
rbx.platform.typedef.ptrdiff_t = long
rbx.platform.typedef.rlim64_t = ulong_long
rbx.platform.typedef.rlim_t = ulong
rbx.platform.typedef.rpn64_t = long_long
rbx.platform.typedef.rpn_t = int
rbx.platform.typedef.sa_family_t = uchar
rbx.platform.typedef.signal_t = int
rbx.platform.typedef.size64_t = ulong_long
rbx.platform.typedef.size_t = ulong
rbx.platform.typedef.slab_t[12] = char
rbx.platform.typedef.snidx_t = int
rbx.platform.typedef.socklen_t = ulong
rbx.platform.typedef.soff_t = int
rbx.platform.typedef.sshift_t = ushort
rbx.platform.typedef.ssize64_t = long_long
rbx.platform.typedef.ssize_t = long
rbx.platform.typedef.suseconds_t = int
rbx.platform.typedef.swblk_t = int
rbx.platform.typedef.swhatx_t = ulong
rbx.platform.typedef.tid32_t = int
rbx.platform.typedef.tid64_t = ulong_long
rbx.platform.typedef.tid_t = int
rbx.platform.typedef.time32_t = int
rbx.platform.typedef.time64_t = long_long
rbx.platform.typedef.time_t = int
rbx.platform.typedef.timer32_t = int
rbx.platform.typedef.timer64_t = long_long
rbx.platform.typedef.timer_t = int
rbx.platform.typedef.u_char = uchar
rbx.platform.typedef.u_int = uint
rbx.platform.typedef.u_int16 = ushort
rbx.platform.typedef.u_int16_t = ushort
rbx.platform.typedef.u_int32 = uint
rbx.platform.typedef.u_int32_t = uint
rbx.platform.typedef.u_int64 = ulong_long
rbx.platform.typedef.u_int64_t = ulong_long
rbx.platform.typedef.u_int8 = uchar
rbx.platform.typedef.u_int8_t = uchar
rbx.platform.typedef.u_long = ulong
rbx.platform.typedef.u_longlong_t = ulong_long
rbx.platform.typedef.u_short = ushort
rbx.platform.typedef.uchar = uchar
rbx.platform.typedef.uchar_t = uchar
rbx.platform.typedef.uid_t = uint
rbx.platform.typedef.uint = uint
rbx.platform.typedef.uint16_t = ushort
rbx.platform.typedef.uint32_t = uint
rbx.platform.typedef.uint32long64_t = uint
rbx.platform.typedef.uint64_t = ulong_long
rbx.platform.typedef.uint8_t = uchar
rbx.platform.typedef.uint_fast16_t = ushort
rbx.platform.typedef.uint_fast32_t = uint
rbx.platform.typedef.uint_fast64_t = ulong_long
rbx.platform.typedef.uint_fast8_t = uchar
rbx.platform.typedef.uint_least16_t = ushort
rbx.platform.typedef.uint_least32_t = uint
rbx.platform.typedef.uint_least64_t = ulong_long
rbx.platform.typedef.uint_least8_t = uchar
rbx.platform.typedef.uint_t = uint
rbx.platform.typedef.uintfast_t = uint
rbx.platform.typedef.uintmax_t = ulong_long
rbx.platform.typedef.uintptr_t = ulong
rbx.platform.typedef.ulong = ulong
rbx.platform.typedef.ulong32int64_t = ulong
rbx.platform.typedef.ulong_t = ulong
rbx.platform.typedef.unidx_t = int
rbx.platform.typedef.unit_addr_t = ulong_long
rbx.platform.typedef.ureg_t = ulong
rbx.platform.typedef.useconds_t = uint
rbx.platform.typedef.ushort = ushort
rbx.platform.typedef.ushort_t = ushort
rbx.platform.typedef.va_list = string
rbx.platform.typedef.vmfkey_t = uint
rbx.platform.typedef.vmhandle32_t = uint
rbx.platform.typedef.vmhandle_t = ulong
rbx.platform.typedef.vmhwkey_t = int
rbx.platform.typedef.vmid32_t = int
rbx.platform.typedef.vmid64_t = long_long
rbx.platform.typedef.vmid_t = long
rbx.platform.typedef.vmidx_t = int
rbx.platform.typedef.vmkey_t = int
rbx.platform.typedef.vmlpghandle_t = ulong
rbx.platform.typedef.vmm_lock_t = int
rbx.platform.typedef.vmnodeidx_t = int
rbx.platform.typedef.vmprkey_t = uint
rbx.platform.typedef.vmsize_t = int
rbx.platform.typedef.vpn_t = int
rbx.platform.typedef.wchar_t = ushort
rbx.platform.typedef.wctype_t = uint
rbx.platform.typedef.wint_t = int
