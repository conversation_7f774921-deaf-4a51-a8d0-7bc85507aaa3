rbx.platform.typedef.*) = pointer
rbx.platform.typedef.___wchar_t = int
rbx.platform.typedef.__clock_t = ulong
rbx.platform.typedef.__clockid_t = ulong
rbx.platform.typedef.__fd_mask = ulong
rbx.platform.typedef.__int16_t = short
rbx.platform.typedef.__int32_t = int
rbx.platform.typedef.__int64_t = long
rbx.platform.typedef.__int8_t = char
rbx.platform.typedef.__int_fast16_t = int
rbx.platform.typedef.__int_fast32_t = int
rbx.platform.typedef.__int_fast64_t = long
rbx.platform.typedef.__int_fast8_t = int
rbx.platform.typedef.__int_least16_t = short
rbx.platform.typedef.__int_least32_t = int
rbx.platform.typedef.__int_least64_t = long
rbx.platform.typedef.__int_least8_t = char
rbx.platform.typedef.__intlp_t = long
rbx.platform.typedef.__intmax_t = long
rbx.platform.typedef.__intptr_t = long
rbx.platform.typedef.__off_t = long
rbx.platform.typedef.__pid_t = int
rbx.platform.typedef.__ptrdiff_t = long
rbx.platform.typedef.__register_t = long
rbx.platform.typedef.__rlim_t = long
rbx.platform.typedef.__sig_atomic_t = int
rbx.platform.typedef.__size_t = ulong
rbx.platform.typedef.__socklen_t = uint
rbx.platform.typedef.__ssize_t = long
rbx.platform.typedef.__suseconds_t = long
rbx.platform.typedef.__time_t = long
rbx.platform.typedef.__timer_t = int
rbx.platform.typedef.__uint16_t = ushort
rbx.platform.typedef.__uint32_t = uint
rbx.platform.typedef.__uint64_t = ulong
rbx.platform.typedef.__uint8_t = uchar
rbx.platform.typedef.__uint_fast16_t = uint
rbx.platform.typedef.__uint_fast32_t = uint
rbx.platform.typedef.__uint_fast64_t = ulong
rbx.platform.typedef.__uint_fast8_t = uint
rbx.platform.typedef.__uint_least16_t = ushort
rbx.platform.typedef.__uint_least32_t = uint
rbx.platform.typedef.__uint_least64_t = ulong
rbx.platform.typedef.__uint_least8_t = uchar
rbx.platform.typedef.__uintlp_t = ulong
rbx.platform.typedef.__uintmax_t = ulong
rbx.platform.typedef.__uintptr_t = ulong
rbx.platform.typedef.__wint_t = int
rbx.platform.typedef.blkcnt_t = long
rbx.platform.typedef.blksize_t = long
rbx.platform.typedef.c_caddr_t = pointer
rbx.platform.typedef.caddr_t = string
rbx.platform.typedef.clock_t = ulong
rbx.platform.typedef.clockid_t = ulong
rbx.platform.typedef.daddr_t = int
rbx.platform.typedef.dev_t = uint
rbx.platform.typedef.fixpt_t = uint
rbx.platform.typedef.fsblkcnt_t = ulong
rbx.platform.typedef.fsfilcnt_t = ulong
rbx.platform.typedef.gid_t = uint
rbx.platform.typedef.id_t = long
rbx.platform.typedef.in_addr_t = uint
rbx.platform.typedef.in_port_t = ushort
rbx.platform.typedef.ino_t = ulong
rbx.platform.typedef.int16_t = short
rbx.platform.typedef.int32_t = int
rbx.platform.typedef.int64_t = long
rbx.platform.typedef.int8_t = char
rbx.platform.typedef.int_fast16_t = int
rbx.platform.typedef.int_fast32_t = int
rbx.platform.typedef.int_fast64_t = long
rbx.platform.typedef.int_fast8_t = int
rbx.platform.typedef.int_least16_t = short
rbx.platform.typedef.int_least32_t = int
rbx.platform.typedef.int_least64_t = long
rbx.platform.typedef.int_least8_t = char
rbx.platform.typedef.intmax_t = long
rbx.platform.typedef.intptr_t = long
rbx.platform.typedef.key_t = long
rbx.platform.typedef.lwpid_t = int
rbx.platform.typedef.mode_t = ushort
rbx.platform.typedef.nlink_t = uint
rbx.platform.typedef.off_t = long
rbx.platform.typedef.pid_t = int
rbx.platform.typedef.pthread_key_t = int
rbx.platform.typedef.ptrdiff_t = long
rbx.platform.typedef.qaddr_t = pointer
rbx.platform.typedef.quad_t = long
rbx.platform.typedef.register_t = long
rbx.platform.typedef.rlim_t = long
rbx.platform.typedef.sa_family_t = uchar
rbx.platform.typedef.segsz_t = long
rbx.platform.typedef.size_t = ulong
rbx.platform.typedef.socklen_t = uint
rbx.platform.typedef.ssize_t = long
rbx.platform.typedef.suseconds_t = long
rbx.platform.typedef.time_t = long
rbx.platform.typedef.timer_t = int
rbx.platform.typedef.u_char = uchar
rbx.platform.typedef.u_daddr_t = uint
rbx.platform.typedef.u_int = uint
rbx.platform.typedef.u_int16_t = ushort
rbx.platform.typedef.u_int32_t = uint
rbx.platform.typedef.u_int64_t = ulong
rbx.platform.typedef.u_int8_t = uchar
rbx.platform.typedef.u_long = ulong
rbx.platform.typedef.u_quad_t = ulong
rbx.platform.typedef.u_short = ushort
rbx.platform.typedef.uid_t = uint
rbx.platform.typedef.uint = uint
rbx.platform.typedef.uint16_t = ushort
rbx.platform.typedef.uint32_t = uint
rbx.platform.typedef.uint64_t = ulong
rbx.platform.typedef.uint8_t = uchar
rbx.platform.typedef.uint_fast16_t = uint
rbx.platform.typedef.uint_fast32_t = uint
rbx.platform.typedef.uint_fast64_t = ulong
rbx.platform.typedef.uint_fast8_t = uint
rbx.platform.typedef.uint_least16_t = ushort
rbx.platform.typedef.uint_least32_t = uint
rbx.platform.typedef.uint_least64_t = ulong
rbx.platform.typedef.uint_least8_t = uchar
rbx.platform.typedef.uintmax_t = ulong
rbx.platform.typedef.uintptr_t = ulong
rbx.platform.typedef.ulong = ulong
rbx.platform.typedef.unchar = uchar
rbx.platform.typedef.useconds_t = uint
rbx.platform.typedef.ushort = ushort
rbx.platform.typedef.v_caddr_t = pointer
rbx.platform.typedef.wchar_t = int
