rbx.platform.typedef.*) = pointer
rbx.platform.typedef.___wchar_t = int
rbx.platform.typedef.__accmode_t = int
rbx.platform.typedef.__blkcnt_t = long
rbx.platform.typedef.__blksize_t = int
rbx.platform.typedef.__char16_t = ushort
rbx.platform.typedef.__char32_t = uint
rbx.platform.typedef.__clock_t = int
rbx.platform.typedef.__clockid_t = int
rbx.platform.typedef.__cpulevel_t = int
rbx.platform.typedef.__cpusetid_t = int
rbx.platform.typedef.__cpuwhich_t = int
rbx.platform.typedef.__critical_t = long
rbx.platform.typedef.__ct_rune_t = int
rbx.platform.typedef.__dev_t = ulong
rbx.platform.typedef.__fd_mask = ulong
rbx.platform.typedef.__fflags_t = uint
rbx.platform.typedef.__fixpt_t = uint
rbx.platform.typedef.__fsblkcnt_t = ulong
rbx.platform.typedef.__fsfilcnt_t = ulong
rbx.platform.typedef.__gid_t = uint
rbx.platform.typedef.__id_t = long
rbx.platform.typedef.__ino_t = ulong
rbx.platform.typedef.__int16_t = short
rbx.platform.typedef.__int32_t = int
rbx.platform.typedef.__int64_t = long
rbx.platform.typedef.__int8_t = char
rbx.platform.typedef.__int_fast16_t = int
rbx.platform.typedef.__int_fast32_t = int
rbx.platform.typedef.__int_fast64_t = long
rbx.platform.typedef.__int_fast8_t = int
rbx.platform.typedef.__int_least16_t = short
rbx.platform.typedef.__int_least32_t = int
rbx.platform.typedef.__int_least64_t = long
rbx.platform.typedef.__int_least8_t = char
rbx.platform.typedef.__intfptr_t = long
rbx.platform.typedef.__intmax_t = long
rbx.platform.typedef.__intptr_t = long
rbx.platform.typedef.__key_t = long
rbx.platform.typedef.__lwpid_t = int
rbx.platform.typedef.__mode_t = ushort
rbx.platform.typedef.__nl_item = int
rbx.platform.typedef.__nlink_t = ulong
rbx.platform.typedef.__off64_t = long
rbx.platform.typedef.__off_t = long
rbx.platform.typedef.__pid_t = int
rbx.platform.typedef.__ptrdiff_t = long
rbx.platform.typedef.__register_t = long
rbx.platform.typedef.__rlim_t = long
rbx.platform.typedef.__rman_res_t = ulong
rbx.platform.typedef.__rune_t = int
rbx.platform.typedef.__sa_family_t = uchar
rbx.platform.typedef.__segsz_t = long
rbx.platform.typedef.__size_t = ulong
rbx.platform.typedef.__socklen_t = uint
rbx.platform.typedef.__ssize_t = long
rbx.platform.typedef.__suseconds_t = long
rbx.platform.typedef.__time_t = long
rbx.platform.typedef.__u_register_t = ulong
rbx.platform.typedef.__uid_t = uint
rbx.platform.typedef.__uint16_t = ushort
rbx.platform.typedef.__uint32_t = uint
rbx.platform.typedef.__uint64_t = ulong
rbx.platform.typedef.__uint8_t = uchar
rbx.platform.typedef.__uint_fast16_t = uint
rbx.platform.typedef.__uint_fast32_t = uint
rbx.platform.typedef.__uint_fast64_t = ulong
rbx.platform.typedef.__uint_fast8_t = uint
rbx.platform.typedef.__uint_least16_t = ushort
rbx.platform.typedef.__uint_least32_t = uint
rbx.platform.typedef.__uint_least64_t = ulong
rbx.platform.typedef.__uint_least8_t = uchar
rbx.platform.typedef.__uintfptr_t = ulong
rbx.platform.typedef.__uintmax_t = ulong
rbx.platform.typedef.__uintptr_t = ulong
rbx.platform.typedef.__useconds_t = uint
rbx.platform.typedef.__vm_offset_t = ulong
rbx.platform.typedef.__vm_paddr_t = ulong
rbx.platform.typedef.__vm_size_t = ulong
rbx.platform.typedef.__wint_t = int
rbx.platform.typedef.accmode_t = int
rbx.platform.typedef.blkcnt_t = long
rbx.platform.typedef.blksize_t = int
rbx.platform.typedef.c_caddr_t = pointer
rbx.platform.typedef.caddr_t = string
rbx.platform.typedef.cap_ioctl_t = ulong
rbx.platform.typedef.clock_t = int
rbx.platform.typedef.clockid_t = int
rbx.platform.typedef.cpulevel_t = int
rbx.platform.typedef.cpusetid_t = int
rbx.platform.typedef.cpuwhich_t = int
rbx.platform.typedef.critical_t = long
rbx.platform.typedef.daddr_t = long
rbx.platform.typedef.dev_t = ulong
rbx.platform.typedef.fd_mask = ulong
rbx.platform.typedef.fflags_t = uint
rbx.platform.typedef.fixpt_t = uint
rbx.platform.typedef.fsblkcnt_t = ulong
rbx.platform.typedef.fsfilcnt_t = ulong
rbx.platform.typedef.gid_t = uint
rbx.platform.typedef.id_t = long
rbx.platform.typedef.in_addr_t = uint
rbx.platform.typedef.in_port_t = ushort
rbx.platform.typedef.ino_t = ulong
rbx.platform.typedef.int16_t = short
rbx.platform.typedef.int32_t = int
rbx.platform.typedef.int64_t = long
rbx.platform.typedef.int8_t = char
rbx.platform.typedef.intmax_t = long
rbx.platform.typedef.intptr_t = long
rbx.platform.typedef.key_t = long
rbx.platform.typedef.ksize_t = ulong
rbx.platform.typedef.kvaddr_t = ulong
rbx.platform.typedef.lwpid_t = int
rbx.platform.typedef.mode_t = ushort
rbx.platform.typedef.nlink_t = ulong
rbx.platform.typedef.off64_t = long
rbx.platform.typedef.off_t = long
rbx.platform.typedef.pid_t = int
rbx.platform.typedef.pthread_key_t = int
rbx.platform.typedef.qaddr_t = pointer
rbx.platform.typedef.quad_t = long
rbx.platform.typedef.register_t = long
rbx.platform.typedef.rlim_t = long
rbx.platform.typedef.rman_res_t = ulong
rbx.platform.typedef.sa_family_t = uchar
rbx.platform.typedef.sbintime_t = long
rbx.platform.typedef.segsz_t = long
rbx.platform.typedef.size_t = ulong
rbx.platform.typedef.socklen_t = uint
rbx.platform.typedef.ssize_t = long
rbx.platform.typedef.suseconds_t = long
rbx.platform.typedef.time_t = long
rbx.platform.typedef.u_char = uchar
rbx.platform.typedef.u_int = uint
rbx.platform.typedef.u_int16_t = ushort
rbx.platform.typedef.u_int32_t = uint
rbx.platform.typedef.u_int64_t = ulong
rbx.platform.typedef.u_int8_t = uchar
rbx.platform.typedef.u_long = ulong
rbx.platform.typedef.u_quad_t = ulong
rbx.platform.typedef.u_register_t = ulong
rbx.platform.typedef.u_short = ushort
rbx.platform.typedef.uid_t = uint
rbx.platform.typedef.uint = uint
rbx.platform.typedef.uint16_t = ushort
rbx.platform.typedef.uint32_t = uint
rbx.platform.typedef.uint64_t = ulong
rbx.platform.typedef.uint8_t = uchar
rbx.platform.typedef.uintmax_t = ulong
rbx.platform.typedef.uintptr_t = ulong
rbx.platform.typedef.useconds_t = uint
rbx.platform.typedef.ushort = ushort
rbx.platform.typedef.vm_offset_t = ulong
rbx.platform.typedef.vm_ooffset_t = long
rbx.platform.typedef.vm_paddr_t = ulong
rbx.platform.typedef.vm_pindex_t = ulong
rbx.platform.typedef.vm_size_t = ulong
