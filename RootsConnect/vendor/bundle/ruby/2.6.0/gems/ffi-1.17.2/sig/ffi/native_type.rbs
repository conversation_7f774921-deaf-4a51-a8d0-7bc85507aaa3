module FFI
  class Type
    INT8: Builtin
    SCHAR: Builtin
    CHAR: Builtin
    UINT8: Builtin
    UCHAR: Builtin
    INT16: Builtin
    SHORT: Builtin
    SSHORT: Builtin
    UINT16: Builtin
    USHORT: Builtin
    INT32: Builtin
    INT: Builtin
    SINT: Builtin
    UINT32: Builtin
    UINT: Builtin
    INT64: Builtin
    LONG_LONG: Builtin
    SLONG_LONG: Builtin
    UINT64: Builtin
    ULONG_LONG: Builtin
    LONG: Builtin
    SLONG: Builtin
    ULONG: Builtin
    FLOAT32: Builtin
    FLOAT: Builtin
    FLOAT64: Builtin
    DOUBLE: Builtin
    LONGDOUBLE: Builtin
    POINTER: Builtin
    BOOL: Builtin
    STRING: Builtin
    BUFFER_IN: Builtin
    BUFFER_OUT: Builtin
    BUFFER_INOUT: Builtin
    VARARGS: Builtin
    VOID: Builtin
  end

  module NativeType
    INT8: Type::Builtin
    UINT8: Type::Builtin
    INT16: Type::Builtin
    UINT16: Type::Builtin
    INT32: Type::Builtin
    UINT32: Type::Builtin
    INT64: Type::Builtin
    UINT64: Type::Builtin
    LONG: Type::Builtin
    ULONG: Type::Builtin
    FLOAT32: Type::Builtin
    FLOAT64: Type::Builtin
    LONGDOUBLE: Type::Builtin
    POINTER: Type::Builtin
    BOOL: Type::Builtin
    STRING: Type::Builtin
    BUFFER_IN: Type::Builtin
    BUFFER_OUT: Type::Builtin
    BUFFER_INOUT: Type::Builtin
    VARARGS: Type::Builtin
    VOID: Type::Builtin
  end

  TYPE_INT8: Type::Builtin
  TYPE_UINT8: Type::Builtin
  TYPE_INT16: Type::Builtin
  TYPE_UINT16: Type::Builtin
  TYPE_INT32: Type::Builtin
  TYPE_UINT32: Type::Builtin
  TYPE_INT64: Type::Builtin
  TYPE_UINT64: Type::Builtin
  TYPE_LONG: Type::Builtin
  TYPE_ULONG: Type::Builtin
  TYPE_FLOAT32: Type::Builtin
  TYPE_FLOAT64: Type::Builtin
  TYPE_LONGDOUBLE: Type::Builtin
  TYPE_POINTER: Type::Builtin
  TYPE_BOOL: Type::Builtin
  TYPE_STRING: Type::Builtin
  TYPE_BUFFER_IN: Type::Builtin
  TYPE_BUFFER_OUT: Type::Builtin
  TYPE_BUFFER_INOUT: Type::Builtin
  TYPE_VARARGS: Type::Builtin
  TYPE_VOID: Type::Builtin
end
