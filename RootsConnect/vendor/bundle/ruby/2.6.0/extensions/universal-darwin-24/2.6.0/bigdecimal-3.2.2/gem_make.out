current directory: /Users/<USER>/Code/ChatGPTApps/communitymap/RootsConnect/vendor/bundle/ruby/2.6.0/gems/bigdecimal-3.2.2/ext/bigdecimal
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -I /System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/lib/ruby/2.6.0 -r ./siteconf20250622-11565-1bfqwm6.rb extconf.rb
checking for __builtin_clz()... yes
checking for __builtin_clzl()... yes
checking for __builtin_clzll()... yes
checking for float.h... yes
checking for math.h... yes
checking for stdbool.h... yes
checking for stdlib.h... yes
checking for x86intrin.h... yes
checking for _lzcnt_u32() in x86intrin.h... yes
checking for _lzcnt_u64() in x86intrin.h... yes
checking for intrin.h... no
checking for labs() in stdlib.h... yes
checking for llabs() in stdlib.h... yes
checking for finite() in math.h... yes
checking for isfinite() in math.h... no
checking for ruby/atomic.h... no
checking for ruby/internal/has/builtin.h... no
checking for ruby/internal/static_assert.h... no
checking for rb_rational_num() in ruby.h... yes
checking for rb_rational_den() in ruby.h... yes
checking for rb_complex_real() in ruby.h... yes
checking for rb_complex_imag() in ruby.h... yes
checking for rb_opts_exception_p() in ruby.h... no
checking for rb_category_warn() in ruby.h... no
checking for RB_WARN_CATEGORY_DEPRECATED in ruby.h... no
creating Makefile

current directory: /Users/<USER>/Code/ChatGPTApps/communitymap/RootsConnect/vendor/bundle/ruby/2.6.0/gems/bigdecimal-3.2.2/ext/bigdecimal
make "DESTDIR=" clean

current directory: /Users/<USER>/Code/ChatGPTApps/communitymap/RootsConnect/vendor/bundle/ruby/2.6.0/gems/bigdecimal-3.2.2/ext/bigdecimal
make "DESTDIR="
compiling bigdecimal.c
bigdecimal.c:1272:39: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1272 |         VALUE numerator = rb_funcall(digits, rb_intern("to_i"), 0);
      |                                              ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
bigdecimal.c:1272:39: note: '{' token is here
 1272 |         VALUE numerator = rb_funcall(digits, rb_intern("to_i"), 0);
      |                                              ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
bigdecimal.c:1272:39: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1272 |         VALUE numerator = rb_funcall(digits, rb_intern("to_i"), 0);
      |                                              ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
bigdecimal.c:1272:39: note: ')' token is here
 1272 |         VALUE numerator = rb_funcall(digits, rb_intern("to_i"), 0);
      |                                              ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
bigdecimal.c:1281:34: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1281 |                               rb_funcall(INT2FIX(10), rb_intern("**"), 1,
      |                                                       ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2599:35: note: expanded from macro 'rb_funcall'
 2599 |         const VALUE rb_funcall_args[] = {__VA_ARGS__}; \
      |                                          ^~~~~~~~~~~
bigdecimal.c:1281:34: note: '{' token is here
 1281 |                               rb_funcall(INT2FIX(10), rb_intern("**"), 1,
      |                                                       ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2599:35: note: expanded from macro 'rb_funcall'
 2599 |         const VALUE rb_funcall_args[] = {__VA_ARGS__}; \
      |                                          ^~~~~~~~~~~
bigdecimal.c:1281:34: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1281 |                               rb_funcall(INT2FIX(10), rb_intern("**"), 1,
      |                                                       ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2599:35: note: expanded from macro 'rb_funcall'
 2599 |         const VALUE rb_funcall_args[] = {__VA_ARGS__}; \
      |                                          ^~~~~~~~~~~
bigdecimal.c:1281:34: note: ')' token is here
 1281 |                               rb_funcall(INT2FIX(10), rb_intern("**"), 1,
      |                                                       ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2599:35: note: expanded from macro 'rb_funcall'
 2599 |         const VALUE rb_funcall_args[] = {__VA_ARGS__}; \
      |                                          ^~~~~~~~~~~
bigdecimal.c:1280:34: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1280 |             ret = rb_funcall(numerator, rb_intern("div"), 1,
      |                                         ^~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
bigdecimal.c:1280:34: note: '{' token is here
 1280 |             ret = rb_funcall(numerator, rb_intern("div"), 1,
      |                                         ^~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
bigdecimal.c:1280:34: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1280 |             ret = rb_funcall(numerator, rb_intern("div"), 1,
      |                                         ^~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
bigdecimal.c:1280:34: note: ')' token is here
 1280 |             ret = rb_funcall(numerator, rb_intern("div"), 1,
      |                                         ^~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
bigdecimal.c:1286:33: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1286 |                              rb_funcall(INT2FIX(10), rb_intern("**"), 1,
      |                                                      ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2599:35: note: expanded from macro 'rb_funcall'
 2599 |         const VALUE rb_funcall_args[] = {__VA_ARGS__}; \
      |                                          ^~~~~~~~~~~
bigdecimal.c:1286:33: note: '{' token is here
 1286 |                              rb_funcall(INT2FIX(10), rb_intern("**"), 1,
      |                                                      ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2599:35: note: expanded from macro 'rb_funcall'
 2599 |         const VALUE rb_funcall_args[] = {__VA_ARGS__}; \
      |                                          ^~~~~~~~~~~
bigdecimal.c:1286:33: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1286 |                              rb_funcall(INT2FIX(10), rb_intern("**"), 1,
      |                                                      ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2599:35: note: expanded from macro 'rb_funcall'
 2599 |         const VALUE rb_funcall_args[] = {__VA_ARGS__}; \
      |                                          ^~~~~~~~~~~
bigdecimal.c:1286:33: note: ')' token is here
 1286 |                              rb_funcall(INT2FIX(10), rb_intern("**"), 1,
      |                                                      ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2599:35: note: expanded from macro 'rb_funcall'
 2599 |         const VALUE rb_funcall_args[] = {__VA_ARGS__}; \
      |                                          ^~~~~~~~~~~
bigdecimal.c:1362:36: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1362 |     numerator = rb_funcall(digits, rb_intern("to_i"), 0);
      |                                    ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
bigdecimal.c:1362:36: note: '{' token is here
 1362 |     numerator = rb_funcall(digits, rb_intern("to_i"), 0);
      |                                    ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
bigdecimal.c:1362:36: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1362 |     numerator = rb_funcall(digits, rb_intern("to_i"), 0);
      |                                    ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
bigdecimal.c:1362:36: note: ')' token is here
 1362 |     numerator = rb_funcall(digits, rb_intern("to_i"), 0);
      |                                    ^~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
bigdecimal.c:1369:31: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1369 |                            rb_funcall(INT2FIX(10), rb_intern("**"), 1,
      |                                                    ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
bigdecimal.c:1369:31: note: '{' token is here
 1369 |                            rb_funcall(INT2FIX(10), rb_intern("**"), 1,
      |                                                    ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
bigdecimal.c:1369:31: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1369 |                            rb_funcall(INT2FIX(10), rb_intern("**"), 1,
      |                                                    ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
bigdecimal.c:1369:31: note: ')' token is here
 1369 |                            rb_funcall(INT2FIX(10), rb_intern("**"), 1,
      |                                                    ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
bigdecimal.c:1374:36: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1374 |                                        rb_funcall(INT2FIX(10), rb_intern("**"), 1,
      |                                                                ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2599:35: note: expanded from macro 'rb_funcall'
 2599 |         const VALUE rb_funcall_args[] = {__VA_ARGS__}; \
      |                                          ^~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/intern.h:178:38: note: expanded from macro 'rb_Rational1'
  178 | #define rb_Rational1(x) rb_Rational((x), INT2FIX(1))
      |                                      ^
bigdecimal.c:1374:36: note: '{' token is here
 1374 |                                        rb_funcall(INT2FIX(10), rb_intern("**"), 1,
      |                                                                ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2599:35: note: expanded from macro 'rb_funcall'
 2599 |         const VALUE rb_funcall_args[] = {__VA_ARGS__}; \
      |                                          ^~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/intern.h:178:38: note: expanded from macro 'rb_Rational1'
  178 | #define rb_Rational1(x) rb_Rational((x), INT2FIX(1))
      |                                      ^
bigdecimal.c:1374:36: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1374 |                                        rb_funcall(INT2FIX(10), rb_intern("**"), 1,
      |                                                                ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2599:35: note: expanded from macro 'rb_funcall'
 2599 |         const VALUE rb_funcall_args[] = {__VA_ARGS__}; \
      |                                          ^~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/intern.h:178:38: note: expanded from macro 'rb_Rational1'
  178 | #define rb_Rational1(x) rb_Rational((x), INT2FIX(1))
      |                                      ^
bigdecimal.c:1374:36: note: ')' token is here
 1374 |                                        rb_funcall(INT2FIX(10), rb_intern("**"), 1,
      |                                                                ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2602:20: note: expanded from macro 'rb_funcall'
 2602 |         rb_funcallv(recv, mid, \
      |                           ^~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:2599:35: note: expanded from macro 'rb_funcall'
 2599 |         const VALUE rb_funcall_args[] = {__VA_ARGS__}; \
      |                                          ^~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/intern.h:178:38: note: expanded from macro 'rb_Rational1'
  178 | #define rb_Rational1(x) rb_Rational((x), INT2FIX(1))
      |                                      ^
bigdecimal.c:1581:40: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1581 |             return rb_num_coerce_cmp(self, r, rb_intern("<=>"));
      |                                               ^~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
bigdecimal.c:1581:40: note: '{' token is here
 1581 |             return rb_num_coerce_cmp(self, r, rb_intern("<=>"));
      |                                               ^~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
bigdecimal.c:1581:40: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1581 |             return rb_num_coerce_cmp(self, r, rb_intern("<=>"));
      |                                               ^~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
bigdecimal.c:1581:40: note: ')' token is here
 1581 |             return rb_num_coerce_cmp(self, r, rb_intern("<=>"));
      |                                               ^~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
bigdecimal.c:1584:46: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1584 |             return RTEST(rb_num_coerce_cmp(self, r, rb_intern("=="))) ? Qtrue : Qfalse;
      |                                                     ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:480:26: note: expanded from macro 'RTEST'
  480 | #define RTEST(v) RB_TEST(v)
      |                          ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:478:31: note: expanded from macro 'RB_TEST'
  478 | #define RB_TEST(v) !(((VALUE)(v) & (VALUE)~RUBY_Qnil) == 0)
      |                               ^
bigdecimal.c:1584:46: note: '{' token is here
 1584 |             return RTEST(rb_num_coerce_cmp(self, r, rb_intern("=="))) ? Qtrue : Qfalse;
      |                                                     ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:480:26: note: expanded from macro 'RTEST'
  480 | #define RTEST(v) RB_TEST(v)
      |                          ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:478:31: note: expanded from macro 'RB_TEST'
  478 | #define RB_TEST(v) !(((VALUE)(v) & (VALUE)~RUBY_Qnil) == 0)
      |                               ^
bigdecimal.c:1584:46: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1584 |             return RTEST(rb_num_coerce_cmp(self, r, rb_intern("=="))) ? Qtrue : Qfalse;
      |                                                     ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:480:26: note: expanded from macro 'RTEST'
  480 | #define RTEST(v) RB_TEST(v)
      |                          ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:478:31: note: expanded from macro 'RB_TEST'
  478 | #define RB_TEST(v) !(((VALUE)(v) & (VALUE)~RUBY_Qnil) == 0)
      |                               ^
bigdecimal.c:1584:46: note: ')' token is here
 1584 |             return RTEST(rb_num_coerce_cmp(self, r, rb_intern("=="))) ? Qtrue : Qfalse;
      |                                                     ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:480:26: note: expanded from macro 'RTEST'
  480 | #define RTEST(v) RB_TEST(v)
      |                          ^
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:478:31: note: expanded from macro 'RB_TEST'
  478 | #define RB_TEST(v) !(((VALUE)(v) & (VALUE)~RUBY_Qnil) == 0)
      |                               ^
bigdecimal.c:1587:10: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1587 |             f = rb_intern(">=");
      |                 ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
bigdecimal.c:1587:10: note: '{' token is here
 1587 |             f = rb_intern(">=");
      |                 ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
bigdecimal.c:1587:10: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1587 |             f = rb_intern(">=");
      |                 ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
bigdecimal.c:1587:10: note: ')' token is here
 1587 |             f = rb_intern(">=");
      |                 ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
bigdecimal.c:1591:10: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1591 |             f = rb_intern("<=");
      |                 ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
bigdecimal.c:1591:10: note: '{' token is here
 1591 |             f = rb_intern("<=");
      |                 ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
bigdecimal.c:1591:10: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 1591 |             f = rb_intern("<=");
      |                 ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
bigdecimal.c:1591:10: note: ')' token is here
 1591 |             f = rb_intern("<=");
      |                 ^~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
bigdecimal.c:2046:39: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 2046 |     if (!b) return DoSomeOne(self, r, rb_intern("remainder"));
      |                                       ^~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
bigdecimal.c:272:48: note: expanded from macro 'DoSomeOne'
  272 | #define DoSomeOne(x,y,f) rb_num_coerce_bin(x,y,f)
      |                                                ^
bigdecimal.c:2046:39: note: '{' token is here
 2046 |     if (!b) return DoSomeOne(self, r, rb_intern("remainder"));
      |                                       ^~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
bigdecimal.c:272:48: note: expanded from macro 'DoSomeOne'
  272 | #define DoSomeOne(x,y,f) rb_num_coerce_bin(x,y,f)
      |                                                ^
bigdecimal.c:2046:39: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 2046 |     if (!b) return DoSomeOne(self, r, rb_intern("remainder"));
      |                                       ^~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
bigdecimal.c:272:48: note: expanded from macro 'DoSomeOne'
  272 | #define DoSomeOne(x,y,f) rb_num_coerce_bin(x,y,f)
      |                                                ^
bigdecimal.c:2046:39: note: ')' token is here
 2046 |     if (!b) return DoSomeOne(self, r, rb_intern("remainder"));
      |                                       ^~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
bigdecimal.c:272:48: note: expanded from macro 'DoSomeOne'
  272 | #define DoSomeOne(x,y,f) rb_num_coerce_bin(x,y,f)
      |                                                ^
bigdecimal.c:2129:29: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 2129 |     return DoSomeOne(self,r,rb_intern("divmod"));
      |                             ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
bigdecimal.c:272:48: note: expanded from macro 'DoSomeOne'
  272 | #define DoSomeOne(x,y,f) rb_num_coerce_bin(x,y,f)
      |                                                ^
bigdecimal.c:2129:29: note: '{' token is here
 2129 |     return DoSomeOne(self,r,rb_intern("divmod"));
      |                             ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
bigdecimal.c:272:48: note: expanded from macro 'DoSomeOne'
  272 | #define DoSomeOne(x,y,f) rb_num_coerce_bin(x,y,f)
      |                                                ^
bigdecimal.c:2129:29: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 2129 |     return DoSomeOne(self,r,rb_intern("divmod"));
      |                             ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
bigdecimal.c:272:48: note: expanded from macro 'DoSomeOne'
  272 | #define DoSomeOne(x,y,f) rb_num_coerce_bin(x,y,f)
      |                                                ^
bigdecimal.c:2129:29: note: ')' token is here
 2129 |     return DoSomeOne(self,r,rb_intern("divmod"));
      |                             ^~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
bigdecimal.c:272:48: note: expanded from macro 'DoSomeOne'
  272 | #define DoSomeOne(x,y,f) rb_num_coerce_bin(x,y,f)
      |                                                ^
bigdecimal.c:2151:35: warning: '(' and '{' tokens introducing statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 2151 |         return DoSomeOne(self, b, rb_intern("div"));
      |                                   ^~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:23: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                       ^
bigdecimal.c:272:48: note: expanded from macro 'DoSomeOne'
  272 | #define DoSomeOne(x,y,f) rb_num_coerce_bin(x,y,f)
      |                                                ^
bigdecimal.c:2151:35: note: '{' token is here
 2151 |         return DoSomeOne(self, b, rb_intern("div"));
      |                                   ^~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1811:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1811 |     {                                                   \
      |     ^
bigdecimal.c:272:48: note: expanded from macro 'DoSomeOne'
  272 | #define DoSomeOne(x,y,f) rb_num_coerce_bin(x,y,f)
      |                                                ^
bigdecimal.c:2151:35: warning: '}' and ')' tokens terminating statement expression appear in different macro expansion contexts [-Wcompound-token-split-by-macro]
 2151 |         return DoSomeOne(self, b, rb_intern("div"));
      |                                   ^~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:24: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1816:5: note: expanded from macro 'RUBY_CONST_ID_CACHE'
 1816 |     }
      |     ^
bigdecimal.c:272:48: note: expanded from macro 'DoSomeOne'
  272 | #define DoSomeOne(x,y,f) rb_num_coerce_bin(x,y,f)
      |                                                ^
bigdecimal.c:2151:35: note: ')' token is here
 2151 |         return DoSomeOne(self, b, rb_intern("div"));
      |                                   ^~~~~~~~~~~~~~~~
/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/include/ruby-2.6.0/ruby/ruby.h:1826:56: note: expanded from macro 'rb_intern'
 1826 |         __extension__ (RUBY_CONST_ID_CACHE((ID), (str))) : \
      |                                                        ^
bigdecimal.c:272:48: note: expanded from macro 'DoSomeOne'
  272 | #define DoSomeOne(x,y,f) rb_num_coerce_bin(x,y,f)
      |                                                ^
28 warnings generated.
compiling missing.c
linking shared-object bigdecimal.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found

current directory: /Users/<USER>/Code/ChatGPTApps/communitymap/RootsConnect/vendor/bundle/ruby/2.6.0/gems/bigdecimal-3.2.2/ext/bigdecimal
make "DESTDIR=" install
/usr/bin/install -c -m 0755 bigdecimal.bundle ./.gem.20250622-11565-1sbcuop
