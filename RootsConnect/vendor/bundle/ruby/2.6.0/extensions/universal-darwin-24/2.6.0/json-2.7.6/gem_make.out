current directory: /Users/<USER>/Code/ChatGPTApps/communitymap/RootsConnect/vendor/bundle/ruby/2.6.0/gems/json-2.7.6/ext/json/ext/parser
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -I /System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/lib/ruby/2.6.0 -r ./siteconf20250622-11565-1gzw4kn.rb extconf.rb
checking for rb_enc_raise() in ruby.h... yes
checking for rb_enc_interned_str() in ruby.h... no
checking for whether -std=c99 is accepted as CFLAGS... yes
creating Makefile

current directory: /Users/<USER>/Code/ChatGPTApps/communitymap/RootsConnect/vendor/bundle/ruby/2.6.0/gems/json-2.7.6/ext/json/ext/parser
make "DESTDIR=" clean

current directory: /Users/<USER>/Code/ChatGPTApps/communitymap/RootsConnect/vendor/bundle/ruby/2.6.0/gems/json-2.7.6/ext/json/ext/parser
make "DESTDIR="
compiling parser.c
linking shared-object json/ext/parser.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found

current directory: /Users/<USER>/Code/ChatGPTApps/communitymap/RootsConnect/vendor/bundle/ruby/2.6.0/gems/json-2.7.6/ext/json/ext/parser
make "DESTDIR=" install
/usr/bin/install -c -m 0755 parser.bundle ./.gem.20250622-11565-1jl4f3/json/ext
