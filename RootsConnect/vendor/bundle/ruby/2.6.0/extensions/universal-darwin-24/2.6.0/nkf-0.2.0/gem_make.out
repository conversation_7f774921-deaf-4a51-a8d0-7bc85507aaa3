current directory: /Users/<USER>/Code/ChatGPTApps/communitymap/RootsConnect/vendor/bundle/ruby/2.6.0/gems/nkf-0.2.0/ext/nkf
/System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/bin/ruby -I /System/Library/Frameworks/Ruby.framework/Versions/2.6/usr/lib/ruby/2.6.0 -r ./siteconf20250622-11565-7ksorw.rb extconf.rb
creating Makefile

current directory: /Users/<USER>/Code/ChatGPTApps/communitymap/RootsConnect/vendor/bundle/ruby/2.6.0/gems/nkf-0.2.0/ext/nkf
make "DESTDIR=" clean

current directory: /Users/<USER>/Code/ChatGPTApps/communitymap/RootsConnect/vendor/bundle/ruby/2.6.0/gems/nkf-0.2.0/ext/nkf
make "DESTDIR="
compiling nkf.c
linking shared-object nkf.bundle
ld: warning: search path '/AppleInternal/Library/BuildRoots/1c8f7852-1ca9-11f0-b28b-226177e5bb69/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.Internal.sdk/usr/local/lib' not found

current directory: /Users/<USER>/Code/ChatGPTApps/communitymap/RootsConnect/vendor/bundle/ruby/2.6.0/gems/nkf-0.2.0/ext/nkf
make "DESTDIR=" install
/usr/bin/install -c -m 0755 nkf.bundle ./.gem.20250622-11565-1raky6f
