import { HttpRequest as __HttpRequest, HttpResponse as __HttpResponse } from "@smithy/protocol-http";
import { SerdeContext as __SerdeContext } from "@smithy/types";
import { AssociateApiCommandInput, AssociateApiCommandOutput } from "../commands/AssociateApiCommand";
import { AssociateMergedGraphqlApiCommandInput, AssociateMergedGraphqlApiCommandOutput } from "../commands/AssociateMergedGraphqlApiCommand";
import { AssociateSourceGraphqlApiCommandInput, AssociateSourceGraphqlApiCommandOutput } from "../commands/AssociateSourceGraphqlApiCommand";
import { CreateApiCacheCommandInput, CreateApiCacheCommandOutput } from "../commands/CreateApiCacheCommand";
import { CreateApiCommandInput, CreateApiCommandOutput } from "../commands/CreateApiCommand";
import { Create<PERSON><PERSON>KeyCommandInput, CreateApiKeyCommandOutput } from "../commands/CreateApiKeyCommand";
import { CreateChannelNamespaceCommandInput, CreateChannelNamespaceCommandOutput } from "../commands/CreateChannelNamespaceCommand";
import { CreateDataSourceCommandInput, CreateDataSourceCommandOutput } from "../commands/CreateDataSourceCommand";
import { CreateDomainNameCommandInput, CreateDomainNameCommandOutput } from "../commands/CreateDomainNameCommand";
import { CreateFunctionCommandInput, CreateFunctionCommandOutput } from "../commands/CreateFunctionCommand";
import { CreateGraphqlApiCommandInput, CreateGraphqlApiCommandOutput } from "../commands/CreateGraphqlApiCommand";
import { CreateResolverCommandInput, CreateResolverCommandOutput } from "../commands/CreateResolverCommand";
import { CreateTypeCommandInput, CreateTypeCommandOutput } from "../commands/CreateTypeCommand";
import { DeleteApiCacheCommandInput, DeleteApiCacheCommandOutput } from "../commands/DeleteApiCacheCommand";
import { DeleteApiCommandInput, DeleteApiCommandOutput } from "../commands/DeleteApiCommand";
import { DeleteApiKeyCommandInput, DeleteApiKeyCommandOutput } from "../commands/DeleteApiKeyCommand";
import { DeleteChannelNamespaceCommandInput, DeleteChannelNamespaceCommandOutput } from "../commands/DeleteChannelNamespaceCommand";
import { DeleteDataSourceCommandInput, DeleteDataSourceCommandOutput } from "../commands/DeleteDataSourceCommand";
import { DeleteDomainNameCommandInput, DeleteDomainNameCommandOutput } from "../commands/DeleteDomainNameCommand";
import { DeleteFunctionCommandInput, DeleteFunctionCommandOutput } from "../commands/DeleteFunctionCommand";
import { DeleteGraphqlApiCommandInput, DeleteGraphqlApiCommandOutput } from "../commands/DeleteGraphqlApiCommand";
import { DeleteResolverCommandInput, DeleteResolverCommandOutput } from "../commands/DeleteResolverCommand";
import { DeleteTypeCommandInput, DeleteTypeCommandOutput } from "../commands/DeleteTypeCommand";
import { DisassociateApiCommandInput, DisassociateApiCommandOutput } from "../commands/DisassociateApiCommand";
import { DisassociateMergedGraphqlApiCommandInput, DisassociateMergedGraphqlApiCommandOutput } from "../commands/DisassociateMergedGraphqlApiCommand";
import { DisassociateSourceGraphqlApiCommandInput, DisassociateSourceGraphqlApiCommandOutput } from "../commands/DisassociateSourceGraphqlApiCommand";
import { EvaluateCodeCommandInput, EvaluateCodeCommandOutput } from "../commands/EvaluateCodeCommand";
import { EvaluateMappingTemplateCommandInput, EvaluateMappingTemplateCommandOutput } from "../commands/EvaluateMappingTemplateCommand";
import { FlushApiCacheCommandInput, FlushApiCacheCommandOutput } from "../commands/FlushApiCacheCommand";
import { GetApiAssociationCommandInput, GetApiAssociationCommandOutput } from "../commands/GetApiAssociationCommand";
import { GetApiCacheCommandInput, GetApiCacheCommandOutput } from "../commands/GetApiCacheCommand";
import { GetApiCommandInput, GetApiCommandOutput } from "../commands/GetApiCommand";
import { GetChannelNamespaceCommandInput, GetChannelNamespaceCommandOutput } from "../commands/GetChannelNamespaceCommand";
import { GetDataSourceCommandInput, GetDataSourceCommandOutput } from "../commands/GetDataSourceCommand";
import { GetDataSourceIntrospectionCommandInput, GetDataSourceIntrospectionCommandOutput } from "../commands/GetDataSourceIntrospectionCommand";
import { GetDomainNameCommandInput, GetDomainNameCommandOutput } from "../commands/GetDomainNameCommand";
import { GetFunctionCommandInput, GetFunctionCommandOutput } from "../commands/GetFunctionCommand";
import { GetGraphqlApiCommandInput, GetGraphqlApiCommandOutput } from "../commands/GetGraphqlApiCommand";
import { GetGraphqlApiEnvironmentVariablesCommandInput, GetGraphqlApiEnvironmentVariablesCommandOutput } from "../commands/GetGraphqlApiEnvironmentVariablesCommand";
import { GetIntrospectionSchemaCommandInput, GetIntrospectionSchemaCommandOutput } from "../commands/GetIntrospectionSchemaCommand";
import { GetResolverCommandInput, GetResolverCommandOutput } from "../commands/GetResolverCommand";
import { GetSchemaCreationStatusCommandInput, GetSchemaCreationStatusCommandOutput } from "../commands/GetSchemaCreationStatusCommand";
import { GetSourceApiAssociationCommandInput, GetSourceApiAssociationCommandOutput } from "../commands/GetSourceApiAssociationCommand";
import { GetTypeCommandInput, GetTypeCommandOutput } from "../commands/GetTypeCommand";
import { ListApiKeysCommandInput, ListApiKeysCommandOutput } from "../commands/ListApiKeysCommand";
import { ListApisCommandInput, ListApisCommandOutput } from "../commands/ListApisCommand";
import { ListChannelNamespacesCommandInput, ListChannelNamespacesCommandOutput } from "../commands/ListChannelNamespacesCommand";
import { ListDataSourcesCommandInput, ListDataSourcesCommandOutput } from "../commands/ListDataSourcesCommand";
import { ListDomainNamesCommandInput, ListDomainNamesCommandOutput } from "../commands/ListDomainNamesCommand";
import { ListFunctionsCommandInput, ListFunctionsCommandOutput } from "../commands/ListFunctionsCommand";
import { ListGraphqlApisCommandInput, ListGraphqlApisCommandOutput } from "../commands/ListGraphqlApisCommand";
import { ListResolversByFunctionCommandInput, ListResolversByFunctionCommandOutput } from "../commands/ListResolversByFunctionCommand";
import { ListResolversCommandInput, ListResolversCommandOutput } from "../commands/ListResolversCommand";
import { ListSourceApiAssociationsCommandInput, ListSourceApiAssociationsCommandOutput } from "../commands/ListSourceApiAssociationsCommand";
import { ListTagsForResourceCommandInput, ListTagsForResourceCommandOutput } from "../commands/ListTagsForResourceCommand";
import { ListTypesByAssociationCommandInput, ListTypesByAssociationCommandOutput } from "../commands/ListTypesByAssociationCommand";
import { ListTypesCommandInput, ListTypesCommandOutput } from "../commands/ListTypesCommand";
import { PutGraphqlApiEnvironmentVariablesCommandInput, PutGraphqlApiEnvironmentVariablesCommandOutput } from "../commands/PutGraphqlApiEnvironmentVariablesCommand";
import { StartDataSourceIntrospectionCommandInput, StartDataSourceIntrospectionCommandOutput } from "../commands/StartDataSourceIntrospectionCommand";
import { StartSchemaCreationCommandInput, StartSchemaCreationCommandOutput } from "../commands/StartSchemaCreationCommand";
import { StartSchemaMergeCommandInput, StartSchemaMergeCommandOutput } from "../commands/StartSchemaMergeCommand";
import { TagResourceCommandInput, TagResourceCommandOutput } from "../commands/TagResourceCommand";
import { UntagResourceCommandInput, UntagResourceCommandOutput } from "../commands/UntagResourceCommand";
import { UpdateApiCacheCommandInput, UpdateApiCacheCommandOutput } from "../commands/UpdateApiCacheCommand";
import { UpdateApiCommandInput, UpdateApiCommandOutput } from "../commands/UpdateApiCommand";
import { UpdateApiKeyCommandInput, UpdateApiKeyCommandOutput } from "../commands/UpdateApiKeyCommand";
import { UpdateChannelNamespaceCommandInput, UpdateChannelNamespaceCommandOutput } from "../commands/UpdateChannelNamespaceCommand";
import { UpdateDataSourceCommandInput, UpdateDataSourceCommandOutput } from "../commands/UpdateDataSourceCommand";
import { UpdateDomainNameCommandInput, UpdateDomainNameCommandOutput } from "../commands/UpdateDomainNameCommand";
import { UpdateFunctionCommandInput, UpdateFunctionCommandOutput } from "../commands/UpdateFunctionCommand";
import { UpdateGraphqlApiCommandInput, UpdateGraphqlApiCommandOutput } from "../commands/UpdateGraphqlApiCommand";
import { UpdateResolverCommandInput, UpdateResolverCommandOutput } from "../commands/UpdateResolverCommand";
import { UpdateSourceApiAssociationCommandInput, UpdateSourceApiAssociationCommandOutput } from "../commands/UpdateSourceApiAssociationCommand";
import { UpdateTypeCommandInput, UpdateTypeCommandOutput } from "../commands/UpdateTypeCommand";
/**
 * serializeAws_restJson1AssociateApiCommand
 */
export declare const se_AssociateApiCommand: (input: AssociateApiCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1AssociateMergedGraphqlApiCommand
 */
export declare const se_AssociateMergedGraphqlApiCommand: (input: AssociateMergedGraphqlApiCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1AssociateSourceGraphqlApiCommand
 */
export declare const se_AssociateSourceGraphqlApiCommand: (input: AssociateSourceGraphqlApiCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateApiCommand
 */
export declare const se_CreateApiCommand: (input: CreateApiCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateApiCacheCommand
 */
export declare const se_CreateApiCacheCommand: (input: CreateApiCacheCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateApiKeyCommand
 */
export declare const se_CreateApiKeyCommand: (input: CreateApiKeyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateChannelNamespaceCommand
 */
export declare const se_CreateChannelNamespaceCommand: (input: CreateChannelNamespaceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateDataSourceCommand
 */
export declare const se_CreateDataSourceCommand: (input: CreateDataSourceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateDomainNameCommand
 */
export declare const se_CreateDomainNameCommand: (input: CreateDomainNameCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateFunctionCommand
 */
export declare const se_CreateFunctionCommand: (input: CreateFunctionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateGraphqlApiCommand
 */
export declare const se_CreateGraphqlApiCommand: (input: CreateGraphqlApiCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateResolverCommand
 */
export declare const se_CreateResolverCommand: (input: CreateResolverCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1CreateTypeCommand
 */
export declare const se_CreateTypeCommand: (input: CreateTypeCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteApiCommand
 */
export declare const se_DeleteApiCommand: (input: DeleteApiCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteApiCacheCommand
 */
export declare const se_DeleteApiCacheCommand: (input: DeleteApiCacheCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteApiKeyCommand
 */
export declare const se_DeleteApiKeyCommand: (input: DeleteApiKeyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteChannelNamespaceCommand
 */
export declare const se_DeleteChannelNamespaceCommand: (input: DeleteChannelNamespaceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteDataSourceCommand
 */
export declare const se_DeleteDataSourceCommand: (input: DeleteDataSourceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteDomainNameCommand
 */
export declare const se_DeleteDomainNameCommand: (input: DeleteDomainNameCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteFunctionCommand
 */
export declare const se_DeleteFunctionCommand: (input: DeleteFunctionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteGraphqlApiCommand
 */
export declare const se_DeleteGraphqlApiCommand: (input: DeleteGraphqlApiCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteResolverCommand
 */
export declare const se_DeleteResolverCommand: (input: DeleteResolverCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DeleteTypeCommand
 */
export declare const se_DeleteTypeCommand: (input: DeleteTypeCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DisassociateApiCommand
 */
export declare const se_DisassociateApiCommand: (input: DisassociateApiCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DisassociateMergedGraphqlApiCommand
 */
export declare const se_DisassociateMergedGraphqlApiCommand: (input: DisassociateMergedGraphqlApiCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1DisassociateSourceGraphqlApiCommand
 */
export declare const se_DisassociateSourceGraphqlApiCommand: (input: DisassociateSourceGraphqlApiCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1EvaluateCodeCommand
 */
export declare const se_EvaluateCodeCommand: (input: EvaluateCodeCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1EvaluateMappingTemplateCommand
 */
export declare const se_EvaluateMappingTemplateCommand: (input: EvaluateMappingTemplateCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1FlushApiCacheCommand
 */
export declare const se_FlushApiCacheCommand: (input: FlushApiCacheCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetApiCommand
 */
export declare const se_GetApiCommand: (input: GetApiCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetApiAssociationCommand
 */
export declare const se_GetApiAssociationCommand: (input: GetApiAssociationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetApiCacheCommand
 */
export declare const se_GetApiCacheCommand: (input: GetApiCacheCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetChannelNamespaceCommand
 */
export declare const se_GetChannelNamespaceCommand: (input: GetChannelNamespaceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetDataSourceCommand
 */
export declare const se_GetDataSourceCommand: (input: GetDataSourceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetDataSourceIntrospectionCommand
 */
export declare const se_GetDataSourceIntrospectionCommand: (input: GetDataSourceIntrospectionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetDomainNameCommand
 */
export declare const se_GetDomainNameCommand: (input: GetDomainNameCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetFunctionCommand
 */
export declare const se_GetFunctionCommand: (input: GetFunctionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetGraphqlApiCommand
 */
export declare const se_GetGraphqlApiCommand: (input: GetGraphqlApiCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetGraphqlApiEnvironmentVariablesCommand
 */
export declare const se_GetGraphqlApiEnvironmentVariablesCommand: (input: GetGraphqlApiEnvironmentVariablesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetIntrospectionSchemaCommand
 */
export declare const se_GetIntrospectionSchemaCommand: (input: GetIntrospectionSchemaCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetResolverCommand
 */
export declare const se_GetResolverCommand: (input: GetResolverCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetSchemaCreationStatusCommand
 */
export declare const se_GetSchemaCreationStatusCommand: (input: GetSchemaCreationStatusCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetSourceApiAssociationCommand
 */
export declare const se_GetSourceApiAssociationCommand: (input: GetSourceApiAssociationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1GetTypeCommand
 */
export declare const se_GetTypeCommand: (input: GetTypeCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListApiKeysCommand
 */
export declare const se_ListApiKeysCommand: (input: ListApiKeysCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListApisCommand
 */
export declare const se_ListApisCommand: (input: ListApisCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListChannelNamespacesCommand
 */
export declare const se_ListChannelNamespacesCommand: (input: ListChannelNamespacesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListDataSourcesCommand
 */
export declare const se_ListDataSourcesCommand: (input: ListDataSourcesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListDomainNamesCommand
 */
export declare const se_ListDomainNamesCommand: (input: ListDomainNamesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListFunctionsCommand
 */
export declare const se_ListFunctionsCommand: (input: ListFunctionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListGraphqlApisCommand
 */
export declare const se_ListGraphqlApisCommand: (input: ListGraphqlApisCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListResolversCommand
 */
export declare const se_ListResolversCommand: (input: ListResolversCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListResolversByFunctionCommand
 */
export declare const se_ListResolversByFunctionCommand: (input: ListResolversByFunctionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListSourceApiAssociationsCommand
 */
export declare const se_ListSourceApiAssociationsCommand: (input: ListSourceApiAssociationsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListTagsForResourceCommand
 */
export declare const se_ListTagsForResourceCommand: (input: ListTagsForResourceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListTypesCommand
 */
export declare const se_ListTypesCommand: (input: ListTypesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1ListTypesByAssociationCommand
 */
export declare const se_ListTypesByAssociationCommand: (input: ListTypesByAssociationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1PutGraphqlApiEnvironmentVariablesCommand
 */
export declare const se_PutGraphqlApiEnvironmentVariablesCommand: (input: PutGraphqlApiEnvironmentVariablesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1StartDataSourceIntrospectionCommand
 */
export declare const se_StartDataSourceIntrospectionCommand: (input: StartDataSourceIntrospectionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1StartSchemaCreationCommand
 */
export declare const se_StartSchemaCreationCommand: (input: StartSchemaCreationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1StartSchemaMergeCommand
 */
export declare const se_StartSchemaMergeCommand: (input: StartSchemaMergeCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1TagResourceCommand
 */
export declare const se_TagResourceCommand: (input: TagResourceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1UntagResourceCommand
 */
export declare const se_UntagResourceCommand: (input: UntagResourceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1UpdateApiCommand
 */
export declare const se_UpdateApiCommand: (input: UpdateApiCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1UpdateApiCacheCommand
 */
export declare const se_UpdateApiCacheCommand: (input: UpdateApiCacheCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1UpdateApiKeyCommand
 */
export declare const se_UpdateApiKeyCommand: (input: UpdateApiKeyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1UpdateChannelNamespaceCommand
 */
export declare const se_UpdateChannelNamespaceCommand: (input: UpdateChannelNamespaceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1UpdateDataSourceCommand
 */
export declare const se_UpdateDataSourceCommand: (input: UpdateDataSourceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1UpdateDomainNameCommand
 */
export declare const se_UpdateDomainNameCommand: (input: UpdateDomainNameCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1UpdateFunctionCommand
 */
export declare const se_UpdateFunctionCommand: (input: UpdateFunctionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1UpdateGraphqlApiCommand
 */
export declare const se_UpdateGraphqlApiCommand: (input: UpdateGraphqlApiCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1UpdateResolverCommand
 */
export declare const se_UpdateResolverCommand: (input: UpdateResolverCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1UpdateSourceApiAssociationCommand
 */
export declare const se_UpdateSourceApiAssociationCommand: (input: UpdateSourceApiAssociationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restJson1UpdateTypeCommand
 */
export declare const se_UpdateTypeCommand: (input: UpdateTypeCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * deserializeAws_restJson1AssociateApiCommand
 */
export declare const de_AssociateApiCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<AssociateApiCommandOutput>;
/**
 * deserializeAws_restJson1AssociateMergedGraphqlApiCommand
 */
export declare const de_AssociateMergedGraphqlApiCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<AssociateMergedGraphqlApiCommandOutput>;
/**
 * deserializeAws_restJson1AssociateSourceGraphqlApiCommand
 */
export declare const de_AssociateSourceGraphqlApiCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<AssociateSourceGraphqlApiCommandOutput>;
/**
 * deserializeAws_restJson1CreateApiCommand
 */
export declare const de_CreateApiCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateApiCommandOutput>;
/**
 * deserializeAws_restJson1CreateApiCacheCommand
 */
export declare const de_CreateApiCacheCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateApiCacheCommandOutput>;
/**
 * deserializeAws_restJson1CreateApiKeyCommand
 */
export declare const de_CreateApiKeyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateApiKeyCommandOutput>;
/**
 * deserializeAws_restJson1CreateChannelNamespaceCommand
 */
export declare const de_CreateChannelNamespaceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateChannelNamespaceCommandOutput>;
/**
 * deserializeAws_restJson1CreateDataSourceCommand
 */
export declare const de_CreateDataSourceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateDataSourceCommandOutput>;
/**
 * deserializeAws_restJson1CreateDomainNameCommand
 */
export declare const de_CreateDomainNameCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateDomainNameCommandOutput>;
/**
 * deserializeAws_restJson1CreateFunctionCommand
 */
export declare const de_CreateFunctionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateFunctionCommandOutput>;
/**
 * deserializeAws_restJson1CreateGraphqlApiCommand
 */
export declare const de_CreateGraphqlApiCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateGraphqlApiCommandOutput>;
/**
 * deserializeAws_restJson1CreateResolverCommand
 */
export declare const de_CreateResolverCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateResolverCommandOutput>;
/**
 * deserializeAws_restJson1CreateTypeCommand
 */
export declare const de_CreateTypeCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateTypeCommandOutput>;
/**
 * deserializeAws_restJson1DeleteApiCommand
 */
export declare const de_DeleteApiCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteApiCommandOutput>;
/**
 * deserializeAws_restJson1DeleteApiCacheCommand
 */
export declare const de_DeleteApiCacheCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteApiCacheCommandOutput>;
/**
 * deserializeAws_restJson1DeleteApiKeyCommand
 */
export declare const de_DeleteApiKeyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteApiKeyCommandOutput>;
/**
 * deserializeAws_restJson1DeleteChannelNamespaceCommand
 */
export declare const de_DeleteChannelNamespaceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteChannelNamespaceCommandOutput>;
/**
 * deserializeAws_restJson1DeleteDataSourceCommand
 */
export declare const de_DeleteDataSourceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteDataSourceCommandOutput>;
/**
 * deserializeAws_restJson1DeleteDomainNameCommand
 */
export declare const de_DeleteDomainNameCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteDomainNameCommandOutput>;
/**
 * deserializeAws_restJson1DeleteFunctionCommand
 */
export declare const de_DeleteFunctionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteFunctionCommandOutput>;
/**
 * deserializeAws_restJson1DeleteGraphqlApiCommand
 */
export declare const de_DeleteGraphqlApiCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteGraphqlApiCommandOutput>;
/**
 * deserializeAws_restJson1DeleteResolverCommand
 */
export declare const de_DeleteResolverCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteResolverCommandOutput>;
/**
 * deserializeAws_restJson1DeleteTypeCommand
 */
export declare const de_DeleteTypeCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteTypeCommandOutput>;
/**
 * deserializeAws_restJson1DisassociateApiCommand
 */
export declare const de_DisassociateApiCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DisassociateApiCommandOutput>;
/**
 * deserializeAws_restJson1DisassociateMergedGraphqlApiCommand
 */
export declare const de_DisassociateMergedGraphqlApiCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DisassociateMergedGraphqlApiCommandOutput>;
/**
 * deserializeAws_restJson1DisassociateSourceGraphqlApiCommand
 */
export declare const de_DisassociateSourceGraphqlApiCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DisassociateSourceGraphqlApiCommandOutput>;
/**
 * deserializeAws_restJson1EvaluateCodeCommand
 */
export declare const de_EvaluateCodeCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<EvaluateCodeCommandOutput>;
/**
 * deserializeAws_restJson1EvaluateMappingTemplateCommand
 */
export declare const de_EvaluateMappingTemplateCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<EvaluateMappingTemplateCommandOutput>;
/**
 * deserializeAws_restJson1FlushApiCacheCommand
 */
export declare const de_FlushApiCacheCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<FlushApiCacheCommandOutput>;
/**
 * deserializeAws_restJson1GetApiCommand
 */
export declare const de_GetApiCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetApiCommandOutput>;
/**
 * deserializeAws_restJson1GetApiAssociationCommand
 */
export declare const de_GetApiAssociationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetApiAssociationCommandOutput>;
/**
 * deserializeAws_restJson1GetApiCacheCommand
 */
export declare const de_GetApiCacheCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetApiCacheCommandOutput>;
/**
 * deserializeAws_restJson1GetChannelNamespaceCommand
 */
export declare const de_GetChannelNamespaceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetChannelNamespaceCommandOutput>;
/**
 * deserializeAws_restJson1GetDataSourceCommand
 */
export declare const de_GetDataSourceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetDataSourceCommandOutput>;
/**
 * deserializeAws_restJson1GetDataSourceIntrospectionCommand
 */
export declare const de_GetDataSourceIntrospectionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetDataSourceIntrospectionCommandOutput>;
/**
 * deserializeAws_restJson1GetDomainNameCommand
 */
export declare const de_GetDomainNameCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetDomainNameCommandOutput>;
/**
 * deserializeAws_restJson1GetFunctionCommand
 */
export declare const de_GetFunctionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetFunctionCommandOutput>;
/**
 * deserializeAws_restJson1GetGraphqlApiCommand
 */
export declare const de_GetGraphqlApiCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetGraphqlApiCommandOutput>;
/**
 * deserializeAws_restJson1GetGraphqlApiEnvironmentVariablesCommand
 */
export declare const de_GetGraphqlApiEnvironmentVariablesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetGraphqlApiEnvironmentVariablesCommandOutput>;
/**
 * deserializeAws_restJson1GetIntrospectionSchemaCommand
 */
export declare const de_GetIntrospectionSchemaCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetIntrospectionSchemaCommandOutput>;
/**
 * deserializeAws_restJson1GetResolverCommand
 */
export declare const de_GetResolverCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetResolverCommandOutput>;
/**
 * deserializeAws_restJson1GetSchemaCreationStatusCommand
 */
export declare const de_GetSchemaCreationStatusCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetSchemaCreationStatusCommandOutput>;
/**
 * deserializeAws_restJson1GetSourceApiAssociationCommand
 */
export declare const de_GetSourceApiAssociationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetSourceApiAssociationCommandOutput>;
/**
 * deserializeAws_restJson1GetTypeCommand
 */
export declare const de_GetTypeCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetTypeCommandOutput>;
/**
 * deserializeAws_restJson1ListApiKeysCommand
 */
export declare const de_ListApiKeysCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListApiKeysCommandOutput>;
/**
 * deserializeAws_restJson1ListApisCommand
 */
export declare const de_ListApisCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListApisCommandOutput>;
/**
 * deserializeAws_restJson1ListChannelNamespacesCommand
 */
export declare const de_ListChannelNamespacesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListChannelNamespacesCommandOutput>;
/**
 * deserializeAws_restJson1ListDataSourcesCommand
 */
export declare const de_ListDataSourcesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListDataSourcesCommandOutput>;
/**
 * deserializeAws_restJson1ListDomainNamesCommand
 */
export declare const de_ListDomainNamesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListDomainNamesCommandOutput>;
/**
 * deserializeAws_restJson1ListFunctionsCommand
 */
export declare const de_ListFunctionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListFunctionsCommandOutput>;
/**
 * deserializeAws_restJson1ListGraphqlApisCommand
 */
export declare const de_ListGraphqlApisCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListGraphqlApisCommandOutput>;
/**
 * deserializeAws_restJson1ListResolversCommand
 */
export declare const de_ListResolversCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListResolversCommandOutput>;
/**
 * deserializeAws_restJson1ListResolversByFunctionCommand
 */
export declare const de_ListResolversByFunctionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListResolversByFunctionCommandOutput>;
/**
 * deserializeAws_restJson1ListSourceApiAssociationsCommand
 */
export declare const de_ListSourceApiAssociationsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListSourceApiAssociationsCommandOutput>;
/**
 * deserializeAws_restJson1ListTagsForResourceCommand
 */
export declare const de_ListTagsForResourceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListTagsForResourceCommandOutput>;
/**
 * deserializeAws_restJson1ListTypesCommand
 */
export declare const de_ListTypesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListTypesCommandOutput>;
/**
 * deserializeAws_restJson1ListTypesByAssociationCommand
 */
export declare const de_ListTypesByAssociationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListTypesByAssociationCommandOutput>;
/**
 * deserializeAws_restJson1PutGraphqlApiEnvironmentVariablesCommand
 */
export declare const de_PutGraphqlApiEnvironmentVariablesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PutGraphqlApiEnvironmentVariablesCommandOutput>;
/**
 * deserializeAws_restJson1StartDataSourceIntrospectionCommand
 */
export declare const de_StartDataSourceIntrospectionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StartDataSourceIntrospectionCommandOutput>;
/**
 * deserializeAws_restJson1StartSchemaCreationCommand
 */
export declare const de_StartSchemaCreationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StartSchemaCreationCommandOutput>;
/**
 * deserializeAws_restJson1StartSchemaMergeCommand
 */
export declare const de_StartSchemaMergeCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<StartSchemaMergeCommandOutput>;
/**
 * deserializeAws_restJson1TagResourceCommand
 */
export declare const de_TagResourceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<TagResourceCommandOutput>;
/**
 * deserializeAws_restJson1UntagResourceCommand
 */
export declare const de_UntagResourceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UntagResourceCommandOutput>;
/**
 * deserializeAws_restJson1UpdateApiCommand
 */
export declare const de_UpdateApiCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateApiCommandOutput>;
/**
 * deserializeAws_restJson1UpdateApiCacheCommand
 */
export declare const de_UpdateApiCacheCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateApiCacheCommandOutput>;
/**
 * deserializeAws_restJson1UpdateApiKeyCommand
 */
export declare const de_UpdateApiKeyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateApiKeyCommandOutput>;
/**
 * deserializeAws_restJson1UpdateChannelNamespaceCommand
 */
export declare const de_UpdateChannelNamespaceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateChannelNamespaceCommandOutput>;
/**
 * deserializeAws_restJson1UpdateDataSourceCommand
 */
export declare const de_UpdateDataSourceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateDataSourceCommandOutput>;
/**
 * deserializeAws_restJson1UpdateDomainNameCommand
 */
export declare const de_UpdateDomainNameCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateDomainNameCommandOutput>;
/**
 * deserializeAws_restJson1UpdateFunctionCommand
 */
export declare const de_UpdateFunctionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateFunctionCommandOutput>;
/**
 * deserializeAws_restJson1UpdateGraphqlApiCommand
 */
export declare const de_UpdateGraphqlApiCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateGraphqlApiCommandOutput>;
/**
 * deserializeAws_restJson1UpdateResolverCommand
 */
export declare const de_UpdateResolverCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateResolverCommandOutput>;
/**
 * deserializeAws_restJson1UpdateSourceApiAssociationCommand
 */
export declare const de_UpdateSourceApiAssociationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateSourceApiAssociationCommandOutput>;
/**
 * deserializeAws_restJson1UpdateTypeCommand
 */
export declare const de_UpdateTypeCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateTypeCommandOutput>;
