import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  AppSyncClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../AppSyncClient";
import {
  CreateGraphqlApiRequest,
  CreateGraphqlApiResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface CreateGraphqlApiCommandInput extends CreateGraphqlApiRequest {}
export interface CreateGraphqlApiCommandOutput
  extends CreateGraphqlApiResponse,
    __MetadataBearer {}
declare const CreateGraphqlApiCommand_base: {
  new (
    input: CreateGraphqlApiCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateGraphqlApiCommandInput,
    CreateGraphqlApiCommandOutput,
    AppSyncClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateGraphqlApiCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateGraphqlApiCommandInput,
    CreateGraphqlApiCommandOutput,
    AppSyncClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateGraphqlApiCommand extends CreateGraphqlApiCommand_base {
  protected static __types: {
    api: {
      input: CreateGraphqlApiRequest;
      output: CreateGraphqlApiResponse;
    };
    sdk: {
      input: CreateGraphqlApiCommandInput;
      output: CreateGraphqlApiCommandOutput;
    };
  };
}
