import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  AppSyncClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../AppSyncClient";
import { CreateApiKeyRequest, CreateApiKeyResponse } from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface CreateApiKeyCommandInput extends CreateApiKeyRequest {}
export interface CreateApiKeyCommandOutput
  extends CreateApiKeyResponse,
    __MetadataBearer {}
declare const CreateApiKeyCommand_base: {
  new (
    input: CreateApiKeyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateApiKeyCommandInput,
    CreateApiKeyCommandOutput,
    AppSyncClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateApiKeyCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateApiKeyCommandInput,
    CreateApiKeyCommandOutput,
    AppSyncClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateApiKeyCommand extends CreateApiKeyCommand_base {
  protected static __types: {
    api: {
      input: CreateApiKeyRequest;
      output: CreateApiKeyResponse;
    };
    sdk: {
      input: CreateApiKeyCommandInput;
      output: CreateApiKeyCommandOutput;
    };
  };
}
