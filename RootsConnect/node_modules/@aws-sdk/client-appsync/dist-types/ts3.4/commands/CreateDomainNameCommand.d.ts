import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  AppSyncClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../AppSyncClient";
import {
  CreateDomainNameRequest,
  CreateDomainNameResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface CreateDomainNameCommandInput extends CreateDomainNameRequest {}
export interface CreateDomainNameCommandOutput
  extends CreateDomainNameResponse,
    __MetadataBearer {}
declare const CreateDomainNameCommand_base: {
  new (
    input: CreateDomainNameCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateDomainNameCommandInput,
    CreateDomainNameCommandOutput,
    AppSyncClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateDomainNameCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateDomainNameCommandInput,
    CreateDomainNameCommandOutput,
    AppSyncClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateDomainNameCommand extends CreateDomainNameCommand_base {
  protected static __types: {
    api: {
      input: CreateDomainNameRequest;
      output: CreateDomainNameResponse;
    };
    sdk: {
      input: CreateDomainNameCommandInput;
      output: CreateDomainNameCommandOutput;
    };
  };
}
