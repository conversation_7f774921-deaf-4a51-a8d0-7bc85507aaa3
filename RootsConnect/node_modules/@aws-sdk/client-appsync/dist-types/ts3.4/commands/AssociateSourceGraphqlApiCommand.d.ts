import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  AppSyncClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../AppSyncClient";
import {
  AssociateSourceGraphqlApiRequest,
  AssociateSourceGraphqlApiResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface AssociateSourceGraphqlApiCommandInput
  extends AssociateSourceGraphqlApiRequest {}
export interface AssociateSourceGraphqlApiCommandOutput
  extends AssociateSourceGraphqlApiResponse,
    __MetadataBearer {}
declare const AssociateSourceGraphqlApiCommand_base: {
  new (
    input: AssociateSourceGraphqlApiCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    AssociateSourceGraphqlApiCommandInput,
    AssociateSourceGraphqlApiCommandOutput,
    AppSyncClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: AssociateSourceGraphqlApiCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    AssociateSourceGraphqlApiCommandInput,
    AssociateSourceGraphqlApiCommandOutput,
    AppSyncClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class AssociateSourceGraphqlApiCommand extends AssociateSourceGraphqlApiCommand_base {
  protected static __types: {
    api: {
      input: AssociateSourceGraphqlApiRequest;
      output: AssociateSourceGraphqlApiResponse;
    };
    sdk: {
      input: AssociateSourceGraphqlApiCommandInput;
      output: AssociateSourceGraphqlApiCommandOutput;
    };
  };
}
