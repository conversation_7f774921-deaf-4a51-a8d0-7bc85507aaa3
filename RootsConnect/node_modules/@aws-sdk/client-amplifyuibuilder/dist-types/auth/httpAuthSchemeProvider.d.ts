import { AwsSdkSigV4AuthInputConfig, AwsSdkSigV4AuthResolvedConfig, AwsSdkSigV4PreviouslyResolved } from "@aws-sdk/core";
import { HandlerExecutionContext, HttpAuthScheme, HttpAuthSchemeParameters, HttpAuthSchemeParametersProvider, HttpAuthSchemeProvider, Provider } from "@smithy/types";
import { AmplifyUIBuilderClientResolvedConfig } from "../AmplifyUIBuilderClient";
/**
 * @internal
 */
export interface AmplifyUIBuilderHttpAuthSchemeParameters extends HttpAuthSchemeParameters {
    region?: string;
}
/**
 * @internal
 */
export interface AmplifyUIBuilderHttpAuthSchemeParametersProvider extends HttpAuthSchemeParametersProvider<AmplifyUIBuilderClientResolvedConfig, HandlerExecutionContext, AmplifyUIBuilderHttpAuthSchemeParameters, object> {
}
/**
 * @internal
 */
export declare const defaultAmplifyUIBuilderHttpAuthSchemeParametersProvider: (config: AmplifyUIBuilderClientResolvedConfig, context: HandlerExecutionContext, input: object) => Promise<AmplifyUIBuilderHttpAuthSchemeParameters>;
/**
 * @internal
 */
export interface AmplifyUIBuilderHttpAuthSchemeProvider extends HttpAuthSchemeProvider<AmplifyUIBuilderHttpAuthSchemeParameters> {
}
/**
 * @internal
 */
export declare const defaultAmplifyUIBuilderHttpAuthSchemeProvider: AmplifyUIBuilderHttpAuthSchemeProvider;
/**
 * @internal
 */
export interface HttpAuthSchemeInputConfig extends AwsSdkSigV4AuthInputConfig {
    /**
     * A comma-separated list of case-sensitive auth scheme names.
     * An auth scheme name is a fully qualified auth scheme ID with the namespace prefix trimmed.
     * For example, the auth scheme with ID aws.auth#sigv4 is named sigv4.
     * @public
     */
    authSchemePreference?: string[] | Provider<string[]>;
    /**
     * Configuration of HttpAuthSchemes for a client which provides default identity providers and signers per auth scheme.
     * @internal
     */
    httpAuthSchemes?: HttpAuthScheme[];
    /**
     * Configuration of an HttpAuthSchemeProvider for a client which resolves which HttpAuthScheme to use.
     * @internal
     */
    httpAuthSchemeProvider?: AmplifyUIBuilderHttpAuthSchemeProvider;
}
/**
 * @internal
 */
export interface HttpAuthSchemeResolvedConfig extends AwsSdkSigV4AuthResolvedConfig {
    /**
     * A comma-separated list of case-sensitive auth scheme names.
     * An auth scheme name is a fully qualified auth scheme ID with the namespace prefix trimmed.
     * For example, the auth scheme with ID aws.auth#sigv4 is named sigv4.
     * @public
     */
    readonly authSchemePreference: Provider<string[]>;
    /**
     * Configuration of HttpAuthSchemes for a client which provides default identity providers and signers per auth scheme.
     * @internal
     */
    readonly httpAuthSchemes: HttpAuthScheme[];
    /**
     * Configuration of an HttpAuthSchemeProvider for a client which resolves which HttpAuthScheme to use.
     * @internal
     */
    readonly httpAuthSchemeProvider: AmplifyUIBuilderHttpAuthSchemeProvider;
}
/**
 * @internal
 */
export declare const resolveHttpAuthSchemeConfig: <T>(config: T & HttpAuthSchemeInputConfig & AwsSdkSigV4PreviouslyResolved) => T & HttpAuthSchemeResolvedConfig;
