import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { AmplifyUIBuilderClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes } from "../AmplifyUIBuilderClient";
import { ListCodegenJobsRequest, ListCodegenJobsResponse } from "../models/models_0";
/**
 * @public
 */
export type { __MetadataBearer };
export { $Command };
/**
 * @public
 *
 * The input for {@link ListCodegenJobsCommand}.
 */
export interface ListCodegenJobsCommandInput extends ListCodegenJobsRequest {
}
/**
 * @public
 *
 * The output of {@link ListCodegenJobsCommand}.
 */
export interface ListCodegenJobsCommandOutput extends ListCodegenJobsResponse, __MetadataBearer {
}
declare const ListCodegenJobsCommand_base: {
    new (input: ListCodegenJobsCommandInput): import("@smithy/smithy-client").CommandImpl<ListCodegenJobsCommandInput, ListCodegenJobsCommandOutput, AmplifyUIBuilderClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    new (input: ListCodegenJobsCommandInput): import("@smithy/smithy-client").CommandImpl<ListCodegenJobsCommandInput, ListCodegenJobsCommandOutput, AmplifyUIBuilderClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
/**
 * <p>Retrieves a list of code generation jobs for a specified Amplify app and backend environment.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { AmplifyUIBuilderClient, ListCodegenJobsCommand } from "@aws-sdk/client-amplifyuibuilder"; // ES Modules import
 * // const { AmplifyUIBuilderClient, ListCodegenJobsCommand } = require("@aws-sdk/client-amplifyuibuilder"); // CommonJS import
 * const client = new AmplifyUIBuilderClient(config);
 * const input = { // ListCodegenJobsRequest
 *   appId: "STRING_VALUE", // required
 *   environmentName: "STRING_VALUE", // required
 *   nextToken: "STRING_VALUE",
 *   maxResults: Number("int"),
 * };
 * const command = new ListCodegenJobsCommand(input);
 * const response = await client.send(command);
 * // { // ListCodegenJobsResponse
 * //   entities: [ // CodegenJobSummaryList // required
 * //     { // CodegenJobSummary
 * //       appId: "STRING_VALUE", // required
 * //       environmentName: "STRING_VALUE", // required
 * //       id: "STRING_VALUE", // required
 * //       createdAt: new Date("TIMESTAMP"),
 * //       modifiedAt: new Date("TIMESTAMP"),
 * //     },
 * //   ],
 * //   nextToken: "STRING_VALUE",
 * // };
 *
 * ```
 *
 * @param ListCodegenJobsCommandInput - {@link ListCodegenJobsCommandInput}
 * @returns {@link ListCodegenJobsCommandOutput}
 * @see {@link ListCodegenJobsCommandInput} for command's `input` shape.
 * @see {@link ListCodegenJobsCommandOutput} for command's `response` shape.
 * @see {@link AmplifyUIBuilderClientResolvedConfig | config} for AmplifyUIBuilderClient's `config` shape.
 *
 * @throws {@link InternalServerException} (server fault)
 *  <p>An internal error has occurred. Please retry your request.</p>
 *
 * @throws {@link InvalidParameterException} (client fault)
 *  <p>An invalid or out-of-range value was supplied for the input parameter.</p>
 *
 * @throws {@link ThrottlingException} (client fault)
 *  <p>The request was denied due to request throttling.</p>
 *
 * @throws {@link AmplifyUIBuilderServiceException}
 * <p>Base exception class for all service exceptions from AmplifyUIBuilder service.</p>
 *
 *
 * @public
 */
export declare class ListCodegenJobsCommand extends ListCodegenJobsCommand_base {
    /** @internal type navigation helper, not in runtime. */
    protected static __types: {
        api: {
            input: ListCodegenJobsRequest;
            output: ListCodegenJobsResponse;
        };
        sdk: {
            input: ListCodegenJobsCommandInput;
            output: ListCodegenJobsCommandOutput;
        };
    };
}
