import { Paginator } from "@smithy/types";
import {
  ListCodegenJobsCommandInput,
  ListCodegenJobsCommandOutput,
} from "../commands/ListCodegenJobsCommand";
import { AmplifyUIBuilderPaginationConfiguration } from "./Interfaces";
export declare const paginateListCodegenJobs: (
  config: AmplifyUIBuilderPaginationConfiguration,
  input: ListCodegenJobsCommandInput,
  ...rest: any[]
) => Paginator<ListCodegenJobsCommandOutput>;
