import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  AmplifyUIBuilderClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../AmplifyUIBuilderClient";
import {
  ListCodegenJobsRequest,
  ListCodegenJobsResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface ListCodegenJobsCommandInput extends ListCodegenJobsRequest {}
export interface ListCodegenJobsCommandOutput
  extends ListCodegenJobsResponse,
    __MetadataBearer {}
declare const ListCodegenJobsCommand_base: {
  new (
    input: ListCodegenJobsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListCodegenJobsCommandInput,
    ListCodegenJobsCommandOutput,
    AmplifyUIBuilderClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListCodegenJobsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListCodegenJobsCommandInput,
    ListCodegenJobsCommandOutput,
    AmplifyUIBuilderClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListCodegenJobsCommand extends ListCodegenJobsCommand_base {
  protected static __types: {
    api: {
      input: ListCodegenJobsRequest;
      output: ListCodegenJobsResponse;
    };
    sdk: {
      input: ListCodegenJobsCommandInput;
      output: ListCodegenJobsCommandOutput;
    };
  };
}
