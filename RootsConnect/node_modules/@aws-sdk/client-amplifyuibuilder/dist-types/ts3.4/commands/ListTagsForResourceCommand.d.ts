import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  AmplifyUIBuilderClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../AmplifyUIBuilderClient";
import {
  ListTagsForResourceRequest,
  ListTagsForResourceResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface ListTagsForResourceCommandInput
  extends ListTagsForResourceRequest {}
export interface ListTagsForResourceCommandOutput
  extends ListTagsForResourceResponse,
    __MetadataBearer {}
declare const ListTagsForResourceCommand_base: {
  new (
    input: ListTagsForResourceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListTagsForResourceCommandInput,
    ListTagsForResourceCommandOutput,
    AmplifyUIBuilderClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListTagsForResourceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListTagsForResourceCommandInput,
    ListTagsForResourceCommandOutput,
    AmplifyUIBuilderClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListTagsForResourceCommand extends ListTagsForResourceCommand_base {
  protected static __types: {
    api: {
      input: ListTagsForResourceRequest;
      output: ListTagsForResourceResponse;
    };
    sdk: {
      input: ListTagsForResourceCommandInput;
      output: ListTagsForResourceCommandOutput;
    };
  };
}
