import { Paginator } from "@smithy/types";
import {
  ListSecretsCommandInput,
  ListSecretsCommandOutput,
} from "../commands/ListSecretsCommand";
import { SecretsManagerPaginationConfiguration } from "./Interfaces";
export declare const paginateListSecrets: (
  config: SecretsManagerPaginationConfiguration,
  input: ListSecretsCommandInput,
  ...rest: any[]
) => Paginator<ListSecretsCommandOutput>;
