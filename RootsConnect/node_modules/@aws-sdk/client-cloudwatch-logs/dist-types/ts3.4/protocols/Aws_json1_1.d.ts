import {
  HttpRequest as __HttpRequest,
  HttpResponse as __HttpResponse,
} from "@smithy/protocol-http";
import {
  EventStreamSerdeContext as __EventStreamSerdeContext,
  SerdeContext as __SerdeContext,
} from "@smithy/types";
import {
  AssociateKmsKeyCommandInput,
  AssociateKmsKeyCommandOutput,
} from "../commands/AssociateKmsKeyCommand";
import {
  CancelExportTaskCommandInput,
  CancelExportTaskCommandOutput,
} from "../commands/CancelExportTaskCommand";
import {
  CreateDeliveryCommandInput,
  CreateDeliveryCommandOutput,
} from "../commands/CreateDeliveryCommand";
import {
  CreateExportTaskCommandInput,
  CreateExportTaskCommandOutput,
} from "../commands/CreateExportTaskCommand";
import {
  CreateLogAnomalyDetectorCommandInput,
  CreateLogAnomalyDetectorCommandOutput,
} from "../commands/CreateLogAnomalyDetectorCommand";
import {
  CreateLogGroupCommandInput,
  CreateLogGroupCommandOutput,
} from "../commands/CreateLogGroupCommand";
import {
  CreateLogStreamCommandInput,
  CreateLogStreamCommandOutput,
} from "../commands/CreateLogStreamCommand";
import {
  DeleteAccountPolicyCommandInput,
  DeleteAccountPolicyCommandOutput,
} from "../commands/DeleteAccountPolicyCommand";
import {
  DeleteDataProtectionPolicyCommandInput,
  DeleteDataProtectionPolicyCommandOutput,
} from "../commands/DeleteDataProtectionPolicyCommand";
import {
  DeleteDeliveryCommandInput,
  DeleteDeliveryCommandOutput,
} from "../commands/DeleteDeliveryCommand";
import {
  DeleteDeliveryDestinationCommandInput,
  DeleteDeliveryDestinationCommandOutput,
} from "../commands/DeleteDeliveryDestinationCommand";
import {
  DeleteDeliveryDestinationPolicyCommandInput,
  DeleteDeliveryDestinationPolicyCommandOutput,
} from "../commands/DeleteDeliveryDestinationPolicyCommand";
import {
  DeleteDeliverySourceCommandInput,
  DeleteDeliverySourceCommandOutput,
} from "../commands/DeleteDeliverySourceCommand";
import {
  DeleteDestinationCommandInput,
  DeleteDestinationCommandOutput,
} from "../commands/DeleteDestinationCommand";
import {
  DeleteIndexPolicyCommandInput,
  DeleteIndexPolicyCommandOutput,
} from "../commands/DeleteIndexPolicyCommand";
import {
  DeleteIntegrationCommandInput,
  DeleteIntegrationCommandOutput,
} from "../commands/DeleteIntegrationCommand";
import {
  DeleteLogAnomalyDetectorCommandInput,
  DeleteLogAnomalyDetectorCommandOutput,
} from "../commands/DeleteLogAnomalyDetectorCommand";
import {
  DeleteLogGroupCommandInput,
  DeleteLogGroupCommandOutput,
} from "../commands/DeleteLogGroupCommand";
import {
  DeleteLogStreamCommandInput,
  DeleteLogStreamCommandOutput,
} from "../commands/DeleteLogStreamCommand";
import {
  DeleteMetricFilterCommandInput,
  DeleteMetricFilterCommandOutput,
} from "../commands/DeleteMetricFilterCommand";
import {
  DeleteQueryDefinitionCommandInput,
  DeleteQueryDefinitionCommandOutput,
} from "../commands/DeleteQueryDefinitionCommand";
import {
  DeleteResourcePolicyCommandInput,
  DeleteResourcePolicyCommandOutput,
} from "../commands/DeleteResourcePolicyCommand";
import {
  DeleteRetentionPolicyCommandInput,
  DeleteRetentionPolicyCommandOutput,
} from "../commands/DeleteRetentionPolicyCommand";
import {
  DeleteSubscriptionFilterCommandInput,
  DeleteSubscriptionFilterCommandOutput,
} from "../commands/DeleteSubscriptionFilterCommand";
import {
  DeleteTransformerCommandInput,
  DeleteTransformerCommandOutput,
} from "../commands/DeleteTransformerCommand";
import {
  DescribeAccountPoliciesCommandInput,
  DescribeAccountPoliciesCommandOutput,
} from "../commands/DescribeAccountPoliciesCommand";
import {
  DescribeConfigurationTemplatesCommandInput,
  DescribeConfigurationTemplatesCommandOutput,
} from "../commands/DescribeConfigurationTemplatesCommand";
import {
  DescribeDeliveriesCommandInput,
  DescribeDeliveriesCommandOutput,
} from "../commands/DescribeDeliveriesCommand";
import {
  DescribeDeliveryDestinationsCommandInput,
  DescribeDeliveryDestinationsCommandOutput,
} from "../commands/DescribeDeliveryDestinationsCommand";
import {
  DescribeDeliverySourcesCommandInput,
  DescribeDeliverySourcesCommandOutput,
} from "../commands/DescribeDeliverySourcesCommand";
import {
  DescribeDestinationsCommandInput,
  DescribeDestinationsCommandOutput,
} from "../commands/DescribeDestinationsCommand";
import {
  DescribeExportTasksCommandInput,
  DescribeExportTasksCommandOutput,
} from "../commands/DescribeExportTasksCommand";
import {
  DescribeFieldIndexesCommandInput,
  DescribeFieldIndexesCommandOutput,
} from "../commands/DescribeFieldIndexesCommand";
import {
  DescribeIndexPoliciesCommandInput,
  DescribeIndexPoliciesCommandOutput,
} from "../commands/DescribeIndexPoliciesCommand";
import {
  DescribeLogGroupsCommandInput,
  DescribeLogGroupsCommandOutput,
} from "../commands/DescribeLogGroupsCommand";
import {
  DescribeLogStreamsCommandInput,
  DescribeLogStreamsCommandOutput,
} from "../commands/DescribeLogStreamsCommand";
import {
  DescribeMetricFiltersCommandInput,
  DescribeMetricFiltersCommandOutput,
} from "../commands/DescribeMetricFiltersCommand";
import {
  DescribeQueriesCommandInput,
  DescribeQueriesCommandOutput,
} from "../commands/DescribeQueriesCommand";
import {
  DescribeQueryDefinitionsCommandInput,
  DescribeQueryDefinitionsCommandOutput,
} from "../commands/DescribeQueryDefinitionsCommand";
import {
  DescribeResourcePoliciesCommandInput,
  DescribeResourcePoliciesCommandOutput,
} from "../commands/DescribeResourcePoliciesCommand";
import {
  DescribeSubscriptionFiltersCommandInput,
  DescribeSubscriptionFiltersCommandOutput,
} from "../commands/DescribeSubscriptionFiltersCommand";
import {
  DisassociateKmsKeyCommandInput,
  DisassociateKmsKeyCommandOutput,
} from "../commands/DisassociateKmsKeyCommand";
import {
  FilterLogEventsCommandInput,
  FilterLogEventsCommandOutput,
} from "../commands/FilterLogEventsCommand";
import {
  GetDataProtectionPolicyCommandInput,
  GetDataProtectionPolicyCommandOutput,
} from "../commands/GetDataProtectionPolicyCommand";
import {
  GetDeliveryCommandInput,
  GetDeliveryCommandOutput,
} from "../commands/GetDeliveryCommand";
import {
  GetDeliveryDestinationCommandInput,
  GetDeliveryDestinationCommandOutput,
} from "../commands/GetDeliveryDestinationCommand";
import {
  GetDeliveryDestinationPolicyCommandInput,
  GetDeliveryDestinationPolicyCommandOutput,
} from "../commands/GetDeliveryDestinationPolicyCommand";
import {
  GetDeliverySourceCommandInput,
  GetDeliverySourceCommandOutput,
} from "../commands/GetDeliverySourceCommand";
import {
  GetIntegrationCommandInput,
  GetIntegrationCommandOutput,
} from "../commands/GetIntegrationCommand";
import {
  GetLogAnomalyDetectorCommandInput,
  GetLogAnomalyDetectorCommandOutput,
} from "../commands/GetLogAnomalyDetectorCommand";
import {
  GetLogEventsCommandInput,
  GetLogEventsCommandOutput,
} from "../commands/GetLogEventsCommand";
import {
  GetLogGroupFieldsCommandInput,
  GetLogGroupFieldsCommandOutput,
} from "../commands/GetLogGroupFieldsCommand";
import {
  GetLogRecordCommandInput,
  GetLogRecordCommandOutput,
} from "../commands/GetLogRecordCommand";
import {
  GetQueryResultsCommandInput,
  GetQueryResultsCommandOutput,
} from "../commands/GetQueryResultsCommand";
import {
  GetTransformerCommandInput,
  GetTransformerCommandOutput,
} from "../commands/GetTransformerCommand";
import {
  ListAnomaliesCommandInput,
  ListAnomaliesCommandOutput,
} from "../commands/ListAnomaliesCommand";
import {
  ListIntegrationsCommandInput,
  ListIntegrationsCommandOutput,
} from "../commands/ListIntegrationsCommand";
import {
  ListLogAnomalyDetectorsCommandInput,
  ListLogAnomalyDetectorsCommandOutput,
} from "../commands/ListLogAnomalyDetectorsCommand";
import {
  ListLogGroupsCommandInput,
  ListLogGroupsCommandOutput,
} from "../commands/ListLogGroupsCommand";
import {
  ListLogGroupsForQueryCommandInput,
  ListLogGroupsForQueryCommandOutput,
} from "../commands/ListLogGroupsForQueryCommand";
import {
  ListTagsForResourceCommandInput,
  ListTagsForResourceCommandOutput,
} from "../commands/ListTagsForResourceCommand";
import {
  ListTagsLogGroupCommandInput,
  ListTagsLogGroupCommandOutput,
} from "../commands/ListTagsLogGroupCommand";
import {
  PutAccountPolicyCommandInput,
  PutAccountPolicyCommandOutput,
} from "../commands/PutAccountPolicyCommand";
import {
  PutDataProtectionPolicyCommandInput,
  PutDataProtectionPolicyCommandOutput,
} from "../commands/PutDataProtectionPolicyCommand";
import {
  PutDeliveryDestinationCommandInput,
  PutDeliveryDestinationCommandOutput,
} from "../commands/PutDeliveryDestinationCommand";
import {
  PutDeliveryDestinationPolicyCommandInput,
  PutDeliveryDestinationPolicyCommandOutput,
} from "../commands/PutDeliveryDestinationPolicyCommand";
import {
  PutDeliverySourceCommandInput,
  PutDeliverySourceCommandOutput,
} from "../commands/PutDeliverySourceCommand";
import {
  PutDestinationCommandInput,
  PutDestinationCommandOutput,
} from "../commands/PutDestinationCommand";
import {
  PutDestinationPolicyCommandInput,
  PutDestinationPolicyCommandOutput,
} from "../commands/PutDestinationPolicyCommand";
import {
  PutIndexPolicyCommandInput,
  PutIndexPolicyCommandOutput,
} from "../commands/PutIndexPolicyCommand";
import {
  PutIntegrationCommandInput,
  PutIntegrationCommandOutput,
} from "../commands/PutIntegrationCommand";
import {
  PutLogEventsCommandInput,
  PutLogEventsCommandOutput,
} from "../commands/PutLogEventsCommand";
import {
  PutMetricFilterCommandInput,
  PutMetricFilterCommandOutput,
} from "../commands/PutMetricFilterCommand";
import {
  PutQueryDefinitionCommandInput,
  PutQueryDefinitionCommandOutput,
} from "../commands/PutQueryDefinitionCommand";
import {
  PutResourcePolicyCommandInput,
  PutResourcePolicyCommandOutput,
} from "../commands/PutResourcePolicyCommand";
import {
  PutRetentionPolicyCommandInput,
  PutRetentionPolicyCommandOutput,
} from "../commands/PutRetentionPolicyCommand";
import {
  PutSubscriptionFilterCommandInput,
  PutSubscriptionFilterCommandOutput,
} from "../commands/PutSubscriptionFilterCommand";
import {
  PutTransformerCommandInput,
  PutTransformerCommandOutput,
} from "../commands/PutTransformerCommand";
import {
  StartLiveTailCommandInput,
  StartLiveTailCommandOutput,
} from "../commands/StartLiveTailCommand";
import {
  StartQueryCommandInput,
  StartQueryCommandOutput,
} from "../commands/StartQueryCommand";
import {
  StopQueryCommandInput,
  StopQueryCommandOutput,
} from "../commands/StopQueryCommand";
import {
  TagLogGroupCommandInput,
  TagLogGroupCommandOutput,
} from "../commands/TagLogGroupCommand";
import {
  TagResourceCommandInput,
  TagResourceCommandOutput,
} from "../commands/TagResourceCommand";
import {
  TestMetricFilterCommandInput,
  TestMetricFilterCommandOutput,
} from "../commands/TestMetricFilterCommand";
import {
  TestTransformerCommandInput,
  TestTransformerCommandOutput,
} from "../commands/TestTransformerCommand";
import {
  UntagLogGroupCommandInput,
  UntagLogGroupCommandOutput,
} from "../commands/UntagLogGroupCommand";
import {
  UntagResourceCommandInput,
  UntagResourceCommandOutput,
} from "../commands/UntagResourceCommand";
import {
  UpdateAnomalyCommandInput,
  UpdateAnomalyCommandOutput,
} from "../commands/UpdateAnomalyCommand";
import {
  UpdateDeliveryConfigurationCommandInput,
  UpdateDeliveryConfigurationCommandOutput,
} from "../commands/UpdateDeliveryConfigurationCommand";
import {
  UpdateLogAnomalyDetectorCommandInput,
  UpdateLogAnomalyDetectorCommandOutput,
} from "../commands/UpdateLogAnomalyDetectorCommand";
export declare const se_AssociateKmsKeyCommand: (
  input: AssociateKmsKeyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CancelExportTaskCommand: (
  input: CancelExportTaskCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateDeliveryCommand: (
  input: CreateDeliveryCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateExportTaskCommand: (
  input: CreateExportTaskCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateLogAnomalyDetectorCommand: (
  input: CreateLogAnomalyDetectorCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateLogGroupCommand: (
  input: CreateLogGroupCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateLogStreamCommand: (
  input: CreateLogStreamCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteAccountPolicyCommand: (
  input: DeleteAccountPolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteDataProtectionPolicyCommand: (
  input: DeleteDataProtectionPolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteDeliveryCommand: (
  input: DeleteDeliveryCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteDeliveryDestinationCommand: (
  input: DeleteDeliveryDestinationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteDeliveryDestinationPolicyCommand: (
  input: DeleteDeliveryDestinationPolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteDeliverySourceCommand: (
  input: DeleteDeliverySourceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteDestinationCommand: (
  input: DeleteDestinationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteIndexPolicyCommand: (
  input: DeleteIndexPolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteIntegrationCommand: (
  input: DeleteIntegrationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteLogAnomalyDetectorCommand: (
  input: DeleteLogAnomalyDetectorCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteLogGroupCommand: (
  input: DeleteLogGroupCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteLogStreamCommand: (
  input: DeleteLogStreamCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteMetricFilterCommand: (
  input: DeleteMetricFilterCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteQueryDefinitionCommand: (
  input: DeleteQueryDefinitionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteResourcePolicyCommand: (
  input: DeleteResourcePolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteRetentionPolicyCommand: (
  input: DeleteRetentionPolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteSubscriptionFilterCommand: (
  input: DeleteSubscriptionFilterCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteTransformerCommand: (
  input: DeleteTransformerCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeAccountPoliciesCommand: (
  input: DescribeAccountPoliciesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeConfigurationTemplatesCommand: (
  input: DescribeConfigurationTemplatesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeDeliveriesCommand: (
  input: DescribeDeliveriesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeDeliveryDestinationsCommand: (
  input: DescribeDeliveryDestinationsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeDeliverySourcesCommand: (
  input: DescribeDeliverySourcesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeDestinationsCommand: (
  input: DescribeDestinationsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeExportTasksCommand: (
  input: DescribeExportTasksCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeFieldIndexesCommand: (
  input: DescribeFieldIndexesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeIndexPoliciesCommand: (
  input: DescribeIndexPoliciesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeLogGroupsCommand: (
  input: DescribeLogGroupsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeLogStreamsCommand: (
  input: DescribeLogStreamsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeMetricFiltersCommand: (
  input: DescribeMetricFiltersCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeQueriesCommand: (
  input: DescribeQueriesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeQueryDefinitionsCommand: (
  input: DescribeQueryDefinitionsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeResourcePoliciesCommand: (
  input: DescribeResourcePoliciesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DescribeSubscriptionFiltersCommand: (
  input: DescribeSubscriptionFiltersCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DisassociateKmsKeyCommand: (
  input: DisassociateKmsKeyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_FilterLogEventsCommand: (
  input: FilterLogEventsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetDataProtectionPolicyCommand: (
  input: GetDataProtectionPolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetDeliveryCommand: (
  input: GetDeliveryCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetDeliveryDestinationCommand: (
  input: GetDeliveryDestinationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetDeliveryDestinationPolicyCommand: (
  input: GetDeliveryDestinationPolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetDeliverySourceCommand: (
  input: GetDeliverySourceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetIntegrationCommand: (
  input: GetIntegrationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetLogAnomalyDetectorCommand: (
  input: GetLogAnomalyDetectorCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetLogEventsCommand: (
  input: GetLogEventsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetLogGroupFieldsCommand: (
  input: GetLogGroupFieldsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetLogRecordCommand: (
  input: GetLogRecordCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetQueryResultsCommand: (
  input: GetQueryResultsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetTransformerCommand: (
  input: GetTransformerCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListAnomaliesCommand: (
  input: ListAnomaliesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListIntegrationsCommand: (
  input: ListIntegrationsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListLogAnomalyDetectorsCommand: (
  input: ListLogAnomalyDetectorsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListLogGroupsCommand: (
  input: ListLogGroupsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListLogGroupsForQueryCommand: (
  input: ListLogGroupsForQueryCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListTagsForResourceCommand: (
  input: ListTagsForResourceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListTagsLogGroupCommand: (
  input: ListTagsLogGroupCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutAccountPolicyCommand: (
  input: PutAccountPolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutDataProtectionPolicyCommand: (
  input: PutDataProtectionPolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutDeliveryDestinationCommand: (
  input: PutDeliveryDestinationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutDeliveryDestinationPolicyCommand: (
  input: PutDeliveryDestinationPolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutDeliverySourceCommand: (
  input: PutDeliverySourceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutDestinationCommand: (
  input: PutDestinationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutDestinationPolicyCommand: (
  input: PutDestinationPolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutIndexPolicyCommand: (
  input: PutIndexPolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutIntegrationCommand: (
  input: PutIntegrationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutLogEventsCommand: (
  input: PutLogEventsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutMetricFilterCommand: (
  input: PutMetricFilterCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutQueryDefinitionCommand: (
  input: PutQueryDefinitionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutResourcePolicyCommand: (
  input: PutResourcePolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutRetentionPolicyCommand: (
  input: PutRetentionPolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutSubscriptionFilterCommand: (
  input: PutSubscriptionFilterCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutTransformerCommand: (
  input: PutTransformerCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StartLiveTailCommand: (
  input: StartLiveTailCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StartQueryCommand: (
  input: StartQueryCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_StopQueryCommand: (
  input: StopQueryCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_TagLogGroupCommand: (
  input: TagLogGroupCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_TagResourceCommand: (
  input: TagResourceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_TestMetricFilterCommand: (
  input: TestMetricFilterCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_TestTransformerCommand: (
  input: TestTransformerCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UntagLogGroupCommand: (
  input: UntagLogGroupCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UntagResourceCommand: (
  input: UntagResourceCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateAnomalyCommand: (
  input: UpdateAnomalyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateDeliveryConfigurationCommand: (
  input: UpdateDeliveryConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UpdateLogAnomalyDetectorCommand: (
  input: UpdateLogAnomalyDetectorCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const de_AssociateKmsKeyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<AssociateKmsKeyCommandOutput>;
export declare const de_CancelExportTaskCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CancelExportTaskCommandOutput>;
export declare const de_CreateDeliveryCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateDeliveryCommandOutput>;
export declare const de_CreateExportTaskCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateExportTaskCommandOutput>;
export declare const de_CreateLogAnomalyDetectorCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateLogAnomalyDetectorCommandOutput>;
export declare const de_CreateLogGroupCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateLogGroupCommandOutput>;
export declare const de_CreateLogStreamCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateLogStreamCommandOutput>;
export declare const de_DeleteAccountPolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteAccountPolicyCommandOutput>;
export declare const de_DeleteDataProtectionPolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteDataProtectionPolicyCommandOutput>;
export declare const de_DeleteDeliveryCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteDeliveryCommandOutput>;
export declare const de_DeleteDeliveryDestinationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteDeliveryDestinationCommandOutput>;
export declare const de_DeleteDeliveryDestinationPolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteDeliveryDestinationPolicyCommandOutput>;
export declare const de_DeleteDeliverySourceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteDeliverySourceCommandOutput>;
export declare const de_DeleteDestinationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteDestinationCommandOutput>;
export declare const de_DeleteIndexPolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteIndexPolicyCommandOutput>;
export declare const de_DeleteIntegrationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteIntegrationCommandOutput>;
export declare const de_DeleteLogAnomalyDetectorCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteLogAnomalyDetectorCommandOutput>;
export declare const de_DeleteLogGroupCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteLogGroupCommandOutput>;
export declare const de_DeleteLogStreamCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteLogStreamCommandOutput>;
export declare const de_DeleteMetricFilterCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteMetricFilterCommandOutput>;
export declare const de_DeleteQueryDefinitionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteQueryDefinitionCommandOutput>;
export declare const de_DeleteResourcePolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteResourcePolicyCommandOutput>;
export declare const de_DeleteRetentionPolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteRetentionPolicyCommandOutput>;
export declare const de_DeleteSubscriptionFilterCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteSubscriptionFilterCommandOutput>;
export declare const de_DeleteTransformerCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteTransformerCommandOutput>;
export declare const de_DescribeAccountPoliciesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeAccountPoliciesCommandOutput>;
export declare const de_DescribeConfigurationTemplatesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeConfigurationTemplatesCommandOutput>;
export declare const de_DescribeDeliveriesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeDeliveriesCommandOutput>;
export declare const de_DescribeDeliveryDestinationsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeDeliveryDestinationsCommandOutput>;
export declare const de_DescribeDeliverySourcesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeDeliverySourcesCommandOutput>;
export declare const de_DescribeDestinationsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeDestinationsCommandOutput>;
export declare const de_DescribeExportTasksCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeExportTasksCommandOutput>;
export declare const de_DescribeFieldIndexesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeFieldIndexesCommandOutput>;
export declare const de_DescribeIndexPoliciesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeIndexPoliciesCommandOutput>;
export declare const de_DescribeLogGroupsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeLogGroupsCommandOutput>;
export declare const de_DescribeLogStreamsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeLogStreamsCommandOutput>;
export declare const de_DescribeMetricFiltersCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeMetricFiltersCommandOutput>;
export declare const de_DescribeQueriesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeQueriesCommandOutput>;
export declare const de_DescribeQueryDefinitionsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeQueryDefinitionsCommandOutput>;
export declare const de_DescribeResourcePoliciesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeResourcePoliciesCommandOutput>;
export declare const de_DescribeSubscriptionFiltersCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DescribeSubscriptionFiltersCommandOutput>;
export declare const de_DisassociateKmsKeyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DisassociateKmsKeyCommandOutput>;
export declare const de_FilterLogEventsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<FilterLogEventsCommandOutput>;
export declare const de_GetDataProtectionPolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetDataProtectionPolicyCommandOutput>;
export declare const de_GetDeliveryCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetDeliveryCommandOutput>;
export declare const de_GetDeliveryDestinationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetDeliveryDestinationCommandOutput>;
export declare const de_GetDeliveryDestinationPolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetDeliveryDestinationPolicyCommandOutput>;
export declare const de_GetDeliverySourceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetDeliverySourceCommandOutput>;
export declare const de_GetIntegrationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetIntegrationCommandOutput>;
export declare const de_GetLogAnomalyDetectorCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetLogAnomalyDetectorCommandOutput>;
export declare const de_GetLogEventsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetLogEventsCommandOutput>;
export declare const de_GetLogGroupFieldsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetLogGroupFieldsCommandOutput>;
export declare const de_GetLogRecordCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetLogRecordCommandOutput>;
export declare const de_GetQueryResultsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetQueryResultsCommandOutput>;
export declare const de_GetTransformerCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetTransformerCommandOutput>;
export declare const de_ListAnomaliesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListAnomaliesCommandOutput>;
export declare const de_ListIntegrationsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListIntegrationsCommandOutput>;
export declare const de_ListLogAnomalyDetectorsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListLogAnomalyDetectorsCommandOutput>;
export declare const de_ListLogGroupsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListLogGroupsCommandOutput>;
export declare const de_ListLogGroupsForQueryCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListLogGroupsForQueryCommandOutput>;
export declare const de_ListTagsForResourceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListTagsForResourceCommandOutput>;
export declare const de_ListTagsLogGroupCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListTagsLogGroupCommandOutput>;
export declare const de_PutAccountPolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutAccountPolicyCommandOutput>;
export declare const de_PutDataProtectionPolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutDataProtectionPolicyCommandOutput>;
export declare const de_PutDeliveryDestinationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutDeliveryDestinationCommandOutput>;
export declare const de_PutDeliveryDestinationPolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutDeliveryDestinationPolicyCommandOutput>;
export declare const de_PutDeliverySourceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutDeliverySourceCommandOutput>;
export declare const de_PutDestinationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutDestinationCommandOutput>;
export declare const de_PutDestinationPolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutDestinationPolicyCommandOutput>;
export declare const de_PutIndexPolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutIndexPolicyCommandOutput>;
export declare const de_PutIntegrationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutIntegrationCommandOutput>;
export declare const de_PutLogEventsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutLogEventsCommandOutput>;
export declare const de_PutMetricFilterCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutMetricFilterCommandOutput>;
export declare const de_PutQueryDefinitionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutQueryDefinitionCommandOutput>;
export declare const de_PutResourcePolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutResourcePolicyCommandOutput>;
export declare const de_PutRetentionPolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutRetentionPolicyCommandOutput>;
export declare const de_PutSubscriptionFilterCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutSubscriptionFilterCommandOutput>;
export declare const de_PutTransformerCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutTransformerCommandOutput>;
export declare const de_StartLiveTailCommand: (
  output: __HttpResponse,
  context: __SerdeContext & __EventStreamSerdeContext
) => Promise<StartLiveTailCommandOutput>;
export declare const de_StartQueryCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StartQueryCommandOutput>;
export declare const de_StopQueryCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<StopQueryCommandOutput>;
export declare const de_TagLogGroupCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<TagLogGroupCommandOutput>;
export declare const de_TagResourceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<TagResourceCommandOutput>;
export declare const de_TestMetricFilterCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<TestMetricFilterCommandOutput>;
export declare const de_TestTransformerCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<TestTransformerCommandOutput>;
export declare const de_UntagLogGroupCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UntagLogGroupCommandOutput>;
export declare const de_UntagResourceCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UntagResourceCommandOutput>;
export declare const de_UpdateAnomalyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateAnomalyCommandOutput>;
export declare const de_UpdateDeliveryConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateDeliveryConfigurationCommandOutput>;
export declare const de_UpdateLogAnomalyDetectorCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UpdateLogAnomalyDetectorCommandOutput>;
