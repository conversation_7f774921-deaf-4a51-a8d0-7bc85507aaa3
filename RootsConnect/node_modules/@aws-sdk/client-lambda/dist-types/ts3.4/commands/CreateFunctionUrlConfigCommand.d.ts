import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  LambdaClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../LambdaClient";
import {
  CreateFunctionUrlConfigRequest,
  CreateFunctionUrlConfigResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface CreateFunctionUrlConfigCommandInput
  extends CreateFunctionUrlConfigRequest {}
export interface CreateFunctionUrlConfigCommandOutput
  extends CreateFunctionUrlConfigResponse,
    __MetadataBearer {}
declare const CreateFunctionUrlConfigCommand_base: {
  new (
    input: CreateFunctionUrlConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateFunctionUrlConfigCommandInput,
    CreateFunctionUrlConfigCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateFunctionUrlConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateFunctionUrlConfigCommandInput,
    CreateFunctionUrlConfigCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateFunctionUrlConfigCommand extends CreateFunctionUrlConfigCommand_base {
  protected static __types: {
    api: {
      input: CreateFunctionUrlConfigRequest;
      output: CreateFunctionUrlConfigResponse;
    };
    sdk: {
      input: CreateFunctionUrlConfigCommandInput;
      output: CreateFunctionUrlConfigCommandOutput;
    };
  };
}
