import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ChangeCidrCollectionRequest,
  ChangeCidrCollectionResponse,
} from "../models/models_0";
import {
  Route53ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../Route53Client";
export { __MetadataBearer };
export { $Command };
export interface ChangeCidrCollectionCommandInput
  extends ChangeCidrCollectionRequest {}
export interface ChangeCidrCollectionCommandOutput
  extends ChangeCidrCollectionResponse,
    __MetadataBearer {}
declare const ChangeCidrCollectionCommand_base: {
  new (
    input: ChangeCidrCollectionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ChangeCidrCollectionCommandInput,
    ChangeCidrCollectionCommandOutput,
    Route53ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ChangeCidrCollectionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ChangeCidrCollectionCommandInput,
    ChangeCidrCollectionCommandOutput,
    Route53ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ChangeCidrCollectionCommand extends ChangeCidrCollectionCommand_base {
  protected static __types: {
    api: {
      input: ChangeCidrCollectionRequest;
      output: ChangeCidrCollectionResponse;
    };
    sdk: {
      input: ChangeCidrCollectionCommandInput;
      output: ChangeCidrCollectionCommandOutput;
    };
  };
}
