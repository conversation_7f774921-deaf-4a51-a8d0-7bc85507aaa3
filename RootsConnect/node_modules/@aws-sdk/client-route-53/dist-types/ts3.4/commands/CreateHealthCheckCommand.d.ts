import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateHealthCheckRequest,
  CreateHealthCheckResponse,
} from "../models/models_0";
import {
  Route53ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../Route53Client";
export { __MetadataBearer };
export { $Command };
export interface CreateHealthCheckCommandInput
  extends CreateHealthCheckRequest {}
export interface CreateHealthCheckCommandOutput
  extends CreateHealthCheckResponse,
    __MetadataBearer {}
declare const CreateHealthCheckCommand_base: {
  new (
    input: CreateHealthCheckCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateHealthCheckCommandInput,
    CreateHealthCheckCommandOutput,
    Route53ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateHealthCheckCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateHealthCheckCommandInput,
    CreateHealthCheckCommandOutput,
    Route53ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateHealthCheckCommand extends CreateHealthCheckCommand_base {
  protected static __types: {
    api: {
      input: CreateHealthCheckRequest;
      output: CreateHealthCheckResponse;
    };
    sdk: {
      input: CreateHealthCheckCommandInput;
      output: CreateHealthCheckCommandOutput;
    };
  };
}
