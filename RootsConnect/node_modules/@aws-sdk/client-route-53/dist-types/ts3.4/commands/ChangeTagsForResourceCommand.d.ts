import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ChangeTagsForResourceRequest,
  ChangeTagsForResourceResponse,
} from "../models/models_0";
import {
  Route53ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../Route53Client";
export { __MetadataBearer };
export { $Command };
export interface ChangeTagsForResourceCommandInput
  extends ChangeTagsForResourceRequest {}
export interface ChangeTagsForResourceCommandOutput
  extends ChangeTagsForResourceResponse,
    __MetadataBearer {}
declare const ChangeTagsForResourceCommand_base: {
  new (
    input: ChangeTagsForResourceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ChangeTagsForResourceCommandInput,
    ChangeTagsForResourceCommandOutput,
    Route53ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ChangeTagsForResourceCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ChangeTagsForResourceCommandInput,
    ChangeTagsForResourceCommandOutput,
    Route53ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ChangeTagsForResourceCommand extends ChangeTagsForResourceCommand_base {
  protected static __types: {
    api: {
      input: ChangeTagsForResourceRequest;
      output: {};
    };
    sdk: {
      input: ChangeTagsForResourceCommandInput;
      output: ChangeTagsForResourceCommandOutput;
    };
  };
}
