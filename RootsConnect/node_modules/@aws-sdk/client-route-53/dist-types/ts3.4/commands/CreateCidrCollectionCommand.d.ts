import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CreateCidrCollectionRequest,
  CreateCidrCollectionResponse,
} from "../models/models_0";
import {
  Route53ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../Route53Client";
export { __MetadataBearer };
export { $Command };
export interface CreateCidrCollectionCommandInput
  extends CreateCidrCollectionRequest {}
export interface CreateCidrCollectionCommandOutput
  extends CreateCidrCollectionResponse,
    __MetadataBearer {}
declare const CreateCidrCollectionCommand_base: {
  new (
    input: CreateCidrCollectionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateCidrCollectionCommandInput,
    CreateCidrCollectionCommandOutput,
    Route53ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateCidrCollectionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateCidrCollectionCommandInput,
    CreateCidrCollectionCommandOutput,
    Route53ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateCidrCollectionCommand extends CreateCidrCollectionCommand_base {
  protected static __types: {
    api: {
      input: CreateCidrCollectionRequest;
      output: CreateCidrCollectionResponse;
    };
    sdk: {
      input: CreateCidrCollectionCommandInput;
      output: CreateCidrCollectionCommandOutput;
    };
  };
}
