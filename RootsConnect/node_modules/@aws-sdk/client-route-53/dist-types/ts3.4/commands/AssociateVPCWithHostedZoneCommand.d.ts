import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  AssociateVPCWithHostedZoneRequest,
  AssociateVPCWithHostedZoneResponse,
} from "../models/models_0";
import {
  Route53ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../Route53Client";
export { __MetadataBearer };
export { $Command };
export interface AssociateVPCWithHostedZoneCommandInput
  extends AssociateVPCWithHostedZoneRequest {}
export interface AssociateVPCWithHostedZoneCommandOutput
  extends AssociateVPCWithHostedZoneResponse,
    __MetadataBearer {}
declare const AssociateVPCWithHostedZoneCommand_base: {
  new (
    input: AssociateVPCWithHostedZoneCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    AssociateVPCWithHostedZoneCommandInput,
    AssociateVPCWithHostedZoneCommandOutput,
    Route53ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: AssociateVPCWithHostedZoneCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    AssociateVPCWithHostedZoneCommandInput,
    AssociateVPCWithHostedZoneCommandOutput,
    Route53ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class AssociateVPCWithHostedZoneCommand extends AssociateVPCWithHostedZoneCommand_base {
  protected static __types: {
    api: {
      input: AssociateVPCWithHostedZoneRequest;
      output: AssociateVPCWithHostedZoneResponse;
    };
    sdk: {
      input: AssociateVPCWithHostedZoneCommandInput;
      output: AssociateVPCWithHostedZoneCommandOutput;
    };
  };
}
