import { Paginator } from "@smithy/types";
import { DescribeImagesCommandInput, DescribeImagesCommandOutput } from "../commands/DescribeImagesCommand";
import { ECRPaginationConfiguration } from "./Interfaces";
/**
 * @public
 */
export declare const paginateDescribeImages: (config: ECRPaginationConfiguration, input: DescribeImagesCommandInput, ...rest: any[]) => Paginator<DescribeImagesCommandOutput>;
