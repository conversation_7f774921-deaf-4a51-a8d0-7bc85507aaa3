import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ECRClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../ECRClient";
import {
  DescribeImageReplicationStatusRequest,
  DescribeImageReplicationStatusResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface DescribeImageReplicationStatusCommandInput
  extends DescribeImageReplicationStatusRequest {}
export interface DescribeImageReplicationStatusCommandOutput
  extends DescribeImageReplicationStatusResponse,
    __MetadataBearer {}
declare const DescribeImageReplicationStatusCommand_base: {
  new (
    input: DescribeImageReplicationStatusCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeImageReplicationStatusCommandInput,
    DescribeImageReplicationStatusCommandOutput,
    ECRClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeImageReplicationStatusCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeImageReplicationStatusCommandInput,
    DescribeImageReplicationStatusCommandOutput,
    ECRClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeImageReplicationStatusCommand extends DescribeImageReplicationStatusCommand_base {
  protected static __types: {
    api: {
      input: DescribeImageReplicationStatusRequest;
      output: DescribeImageReplicationStatusResponse;
    };
    sdk: {
      input: DescribeImageReplicationStatusCommandInput;
      output: DescribeImageReplicationStatusCommandOutput;
    };
  };
}
