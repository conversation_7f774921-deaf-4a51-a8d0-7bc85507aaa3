import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ECRClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../ECRClient";
import {
  DescribePullThroughCacheRulesRequest,
  DescribePullThroughCacheRulesResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface DescribePullThroughCacheRulesCommandInput
  extends DescribePullThroughCacheRulesRequest {}
export interface DescribePullThroughCacheRulesCommandOutput
  extends DescribePullThroughCacheRulesResponse,
    __MetadataBearer {}
declare const DescribePullThroughCacheRulesCommand_base: {
  new (
    input: DescribePullThroughCacheRulesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribePullThroughCacheRulesCommandInput,
    DescribePullThroughCacheRulesCommandOutput,
    ECRClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [DescribePullThroughCacheRulesCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    DescribePullThroughCacheRulesCommandInput,
    DescribePullThroughCacheRulesCommandOutput,
    ECRClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribePullThroughCacheRulesCommand extends DescribePullThroughCacheRulesCommand_base {
  protected static __types: {
    api: {
      input: DescribePullThroughCacheRulesRequest;
      output: DescribePullThroughCacheRulesResponse;
    };
    sdk: {
      input: DescribePullThroughCacheRulesCommandInput;
      output: DescribePullThroughCacheRulesCommandOutput;
    };
  };
}
