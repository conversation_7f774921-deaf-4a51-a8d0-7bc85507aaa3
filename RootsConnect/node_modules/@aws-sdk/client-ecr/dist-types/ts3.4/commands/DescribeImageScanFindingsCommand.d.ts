import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ECRClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../ECRClient";
import {
  DescribeImageScanFindingsRequest,
  DescribeImageScanFindingsResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface DescribeImageScanFindingsCommandInput
  extends DescribeImageScanFindingsRequest {}
export interface DescribeImageScanFindingsCommandOutput
  extends DescribeImageScanFindingsResponse,
    __MetadataBearer {}
declare const DescribeImageScanFindingsCommand_base: {
  new (
    input: DescribeImageScanFindingsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeImageScanFindingsCommandInput,
    DescribeImageScanFindingsCommandOutput,
    ECRClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeImageScanFindingsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeImageScanFindingsCommandInput,
    DescribeImageScanFindingsCommandOutput,
    ECRClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeImageScanFindingsCommand extends DescribeImageScanFindingsCommand_base {
  protected static __types: {
    api: {
      input: DescribeImageScanFindingsRequest;
      output: DescribeImageScanFindingsResponse;
    };
    sdk: {
      input: DescribeImageScanFindingsCommandInput;
      output: DescribeImageScanFindingsCommandOutput;
    };
  };
}
