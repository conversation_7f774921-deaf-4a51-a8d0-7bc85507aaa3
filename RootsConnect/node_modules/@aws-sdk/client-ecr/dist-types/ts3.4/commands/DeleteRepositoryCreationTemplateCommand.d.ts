import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ECRClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../ECRClient";
import {
  DeleteRepositoryCreationTemplateRequest,
  DeleteRepositoryCreationTemplateResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface DeleteRepositoryCreationTemplateCommandInput
  extends DeleteRepositoryCreationTemplateRequest {}
export interface DeleteRepositoryCreationTemplateCommandOutput
  extends DeleteRepositoryCreationTemplateResponse,
    __MetadataBearer {}
declare const DeleteRepositoryCreationTemplateCommand_base: {
  new (
    input: DeleteRepositoryCreationTemplateCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteRepositoryCreationTemplateCommandInput,
    DeleteRepositoryCreationTemplateCommandOutput,
    ECRClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteRepositoryCreationTemplateCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteRepositoryCreationTemplateCommandInput,
    DeleteRepositoryCreationTemplateCommandOutput,
    ECRClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteRepositoryCreationTemplateCommand extends DeleteRepositoryCreationTemplateCommand_base {
  protected static __types: {
    api: {
      input: DeleteRepositoryCreationTemplateRequest;
      output: DeleteRepositoryCreationTemplateResponse;
    };
    sdk: {
      input: DeleteRepositoryCreationTemplateCommandInput;
      output: DeleteRepositoryCreationTemplateCommandOutput;
    };
  };
}
