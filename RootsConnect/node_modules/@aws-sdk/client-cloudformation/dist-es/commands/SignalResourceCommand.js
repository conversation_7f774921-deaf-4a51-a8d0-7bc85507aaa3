import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_SignalResourceCommand, se_SignalResourceCommand } from "../protocols/Aws_query";
export { $Command };
export class SignalResourceCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("CloudFormation", "SignalResource", {})
    .n("CloudFormationClient", "SignalResourceCommand")
    .f(void 0, void 0)
    .ser(se_SignalResourceCommand)
    .de(de_SignalResourceCommand)
    .build() {
}
