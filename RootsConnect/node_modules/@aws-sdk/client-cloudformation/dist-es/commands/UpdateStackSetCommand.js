import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_UpdateStackSetCommand, se_UpdateStackSetCommand } from "../protocols/Aws_query";
export { $Command };
export class UpdateStackSetCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("CloudFormation", "UpdateStackSet", {})
    .n("CloudFormationClient", "UpdateStackSetCommand")
    .f(void 0, void 0)
    .ser(se_UpdateStackSetCommand)
    .de(de_UpdateStackSetCommand)
    .build() {
}
