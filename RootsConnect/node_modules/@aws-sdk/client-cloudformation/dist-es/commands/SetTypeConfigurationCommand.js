import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_SetTypeConfigurationCommand, se_SetTypeConfigurationCommand } from "../protocols/Aws_query";
export { $Command };
export class SetTypeConfigurationCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("CloudFormation", "SetTypeConfiguration", {})
    .n("CloudFormationClient", "SetTypeConfigurationCommand")
    .f(void 0, void 0)
    .ser(se_SetTypeConfigurationCommand)
    .de(de_SetTypeConfigurationCommand)
    .build() {
}
