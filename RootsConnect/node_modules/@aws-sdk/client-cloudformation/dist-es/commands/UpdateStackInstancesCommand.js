import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_UpdateStackInstancesCommand, se_UpdateStackInstancesCommand } from "../protocols/Aws_query";
export { $Command };
export class UpdateStackInstancesCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("CloudFormation", "UpdateStackInstances", {})
    .n("CloudFormationClient", "UpdateStackInstancesCommand")
    .f(void 0, void 0)
    .ser(se_UpdateStackInstancesCommand)
    .de(de_UpdateStackInstancesCommand)
    .build() {
}
