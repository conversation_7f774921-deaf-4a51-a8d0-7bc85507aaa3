import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_SetStackPolicyCommand, se_SetStackPolicyCommand } from "../protocols/Aws_query";
export { $Command };
export class SetStackPolicyCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("CloudFormation", "SetStackPolicy", {})
    .n("CloudFormationClient", "SetStackPolicyCommand")
    .f(void 0, void 0)
    .ser(se_SetStackPolicyCommand)
    .de(de_SetStackPolicyCommand)
    .build() {
}
