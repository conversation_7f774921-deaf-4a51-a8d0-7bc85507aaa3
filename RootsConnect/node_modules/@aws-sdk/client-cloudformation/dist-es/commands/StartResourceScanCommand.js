import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_StartResourceScanCommand, se_StartResourceScanCommand } from "../protocols/Aws_query";
export { $Command };
export class StartResourceScanCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("CloudFormation", "StartResourceScan", {})
    .n("CloudFormationClient", "StartResourceScanCommand")
    .f(void 0, void 0)
    .ser(se_StartResourceScanCommand)
    .de(de_StartResourceScanCommand)
    .build() {
}
