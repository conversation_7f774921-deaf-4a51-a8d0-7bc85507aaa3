import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_SetTypeDefaultVersionCommand, se_SetTypeDefaultVersionCommand } from "../protocols/Aws_query";
export { $Command };
export class SetTypeDefaultVersionCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("CloudFormation", "SetTypeDefaultVersion", {})
    .n("CloudFormationClient", "SetTypeDefaultVersionCommand")
    .f(void 0, void 0)
    .ser(se_SetTypeDefaultVersionCommand)
    .de(de_SetTypeDefaultVersionCommand)
    .build() {
}
