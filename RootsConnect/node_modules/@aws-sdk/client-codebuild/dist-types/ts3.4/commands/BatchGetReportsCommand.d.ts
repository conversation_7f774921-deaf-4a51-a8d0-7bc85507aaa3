import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CodeBuildClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../CodeBuildClient";
import {
  BatchGetReportsInput,
  BatchGetReportsOutput,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface BatchGetReportsCommandInput extends BatchGetReportsInput {}
export interface BatchGetReportsCommandOutput
  extends BatchGetReportsOutput,
    __MetadataBearer {}
declare const BatchGetReportsCommand_base: {
  new (
    input: BatchGetReportsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    BatchGetReportsCommandInput,
    BatchGetReportsCommandOutput,
    CodeBuildClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: BatchGetReportsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    BatchGetReportsCommandInput,
    BatchGetReportsCommandOutput,
    CodeBuildClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class BatchGetReportsCommand extends BatchGetReportsCommand_base {
  protected static __types: {
    api: {
      input: BatchGetReportsInput;
      output: BatchGetReportsOutput;
    };
    sdk: {
      input: BatchGetReportsCommandInput;
      output: BatchGetReportsCommandOutput;
    };
  };
}
