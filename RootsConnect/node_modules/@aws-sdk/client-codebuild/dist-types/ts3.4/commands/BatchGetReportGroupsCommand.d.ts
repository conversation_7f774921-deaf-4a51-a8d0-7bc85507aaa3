import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  CodeBuildClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../CodeBuildClient";
import {
  BatchGetReportGroupsInput,
  BatchGetReportGroupsOutput,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface BatchGetReportGroupsCommandInput
  extends BatchGetReportGroupsInput {}
export interface BatchGetReportGroupsCommandOutput
  extends BatchGetReportGroupsOutput,
    __MetadataBearer {}
declare const BatchGetReportGroupsCommand_base: {
  new (
    input: BatchGetReportGroupsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    BatchGetReportGroupsCommandInput,
    BatchGetReportGroupsCommandOutput,
    CodeBuildClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: BatchGetReportGroupsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    BatchGetReportGroupsCommandInput,
    BatchGetReportGroupsCommandOutput,
    CodeBuildClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class BatchGetReportGroupsCommand extends BatchGetReportGroupsCommand_base {
  protected static __types: {
    api: {
      input: BatchGetReportGroupsInput;
      output: BatchGetReportGroupsOutput;
    };
    sdk: {
      input: BatchGetReportGroupsCommandInput;
      output: BatchGetReportGroupsCommandOutput;
    };
  };
}
