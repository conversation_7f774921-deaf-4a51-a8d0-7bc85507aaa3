import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  CodeBuildClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../CodeBuildClient";
import {
  BatchGetSandboxesInput,
  BatchGetSandboxesOutput,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface BatchGetSandboxesCommandInput extends BatchGetSandboxesInput {}
export interface BatchGetSandboxesCommandOutput
  extends BatchGetSandboxesOutput,
    __MetadataBearer {}
declare const BatchGetSandboxesCommand_base: {
  new (
    input: BatchGetSandboxesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    BatchGetSandboxesCommandInput,
    BatchGetSandboxesCommandOutput,
    CodeBuildClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: BatchGetSandboxesCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    BatchGetSandboxesCommandInput,
    BatchGetSandboxesCommandOutput,
    CodeBuildClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class BatchGetSandboxesCommand extends BatchGetSandboxesCommand_base {
  protected static __types: {
    api: {
      input: BatchGetSandboxesInput;
      output: BatchGetSandboxesOutput;
    };
    sdk: {
      input: BatchGetSandboxesCommandInput;
      output: BatchGetSandboxesCommandOutput;
    };
  };
}
