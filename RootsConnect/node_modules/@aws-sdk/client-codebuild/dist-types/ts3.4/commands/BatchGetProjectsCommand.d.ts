import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  CodeBuildClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../CodeBuildClient";
import {
  BatchGetProjectsInput,
  BatchGetProjectsOutput,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface BatchGetProjectsCommandInput extends BatchGetProjectsInput {}
export interface BatchGetProjectsCommandOutput
  extends BatchGetProjectsOutput,
    __MetadataBearer {}
declare const BatchGetProjectsCommand_base: {
  new (
    input: BatchGetProjectsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    BatchGetProjectsCommandInput,
    BatchGetProjectsCommandOutput,
    CodeBuildClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: BatchGetProjectsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    BatchGetProjectsCommandInput,
    BatchGetProjectsCommandOutput,
    CodeBuildClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class BatchGetProjectsCommand extends BatchGetProjectsCommand_base {
  protected static __types: {
    api: {
      input: BatchGetProjectsInput;
      output: BatchGetProjectsOutput;
    };
    sdk: {
      input: BatchGetProjectsCommandInput;
      output: BatchGetProjectsCommandOutput;
    };
  };
}
