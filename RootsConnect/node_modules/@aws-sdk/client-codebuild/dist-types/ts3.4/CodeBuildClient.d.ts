import {
  HostHeaderInputConfig,
  HostHeaderResolvedConfig,
} from "@aws-sdk/middleware-host-header";
import {
  UserAgentInputConfig,
  UserAgentResolvedConfig,
} from "@aws-sdk/middleware-user-agent";
import {
  RegionInputConfig,
  RegionResolvedConfig,
} from "@smithy/config-resolver";
import {
  EndpointInputConfig,
  EndpointResolvedConfig,
} from "@smithy/middleware-endpoint";
import {
  RetryInputConfig,
  RetryResolvedConfig,
} from "@smithy/middleware-retry";
import { HttpHandlerUserInput as __HttpHandlerUserInput } from "@smithy/protocol-http";
import {
  Client as __Client,
  DefaultsMode as __DefaultsMode,
  SmithyConfiguration as __SmithyConfiguration,
  SmithyResolvedConfiguration as __SmithyResolvedConfiguration,
} from "@smithy/smithy-client";
import {
  AwsCredentialIdentityProvider,
  BodyLengthCalculator as __BodyLengthCalculator,
  CheckOptionalClientConfig as __CheckOptionalClientConfig,
  ChecksumConstructor as __ChecksumConstructor,
  Decoder as __Decoder,
  Encoder as __Encoder,
  HashConstructor as __HashConstructor,
  HttpHandlerOptions as __HttpHandlerOptions,
  Logger as __Logger,
  Provider as __Provider,
  Provider,
  StreamCollector as __StreamCollector,
  UrlParser as __UrlParser,
  UserAgent as __UserAgent,
} from "@smithy/types";
import {
  HttpAuthSchemeInputConfig,
  HttpAuthSchemeResolvedConfig,
} from "./auth/httpAuthSchemeProvider";
import {
  BatchDeleteBuildsCommandInput,
  BatchDeleteBuildsCommandOutput,
} from "./commands/BatchDeleteBuildsCommand";
import {
  BatchGetBuildBatchesCommandInput,
  BatchGetBuildBatchesCommandOutput,
} from "./commands/BatchGetBuildBatchesCommand";
import {
  BatchGetBuildsCommandInput,
  BatchGetBuildsCommandOutput,
} from "./commands/BatchGetBuildsCommand";
import {
  BatchGetCommandExecutionsCommandInput,
  BatchGetCommandExecutionsCommandOutput,
} from "./commands/BatchGetCommandExecutionsCommand";
import {
  BatchGetFleetsCommandInput,
  BatchGetFleetsCommandOutput,
} from "./commands/BatchGetFleetsCommand";
import {
  BatchGetProjectsCommandInput,
  BatchGetProjectsCommandOutput,
} from "./commands/BatchGetProjectsCommand";
import {
  BatchGetReportGroupsCommandInput,
  BatchGetReportGroupsCommandOutput,
} from "./commands/BatchGetReportGroupsCommand";
import {
  BatchGetReportsCommandInput,
  BatchGetReportsCommandOutput,
} from "./commands/BatchGetReportsCommand";
import {
  BatchGetSandboxesCommandInput,
  BatchGetSandboxesCommandOutput,
} from "./commands/BatchGetSandboxesCommand";
import {
  CreateFleetCommandInput,
  CreateFleetCommandOutput,
} from "./commands/CreateFleetCommand";
import {
  CreateProjectCommandInput,
  CreateProjectCommandOutput,
} from "./commands/CreateProjectCommand";
import {
  CreateReportGroupCommandInput,
  CreateReportGroupCommandOutput,
} from "./commands/CreateReportGroupCommand";
import {
  CreateWebhookCommandInput,
  CreateWebhookCommandOutput,
} from "./commands/CreateWebhookCommand";
import {
  DeleteBuildBatchCommandInput,
  DeleteBuildBatchCommandOutput,
} from "./commands/DeleteBuildBatchCommand";
import {
  DeleteFleetCommandInput,
  DeleteFleetCommandOutput,
} from "./commands/DeleteFleetCommand";
import {
  DeleteProjectCommandInput,
  DeleteProjectCommandOutput,
} from "./commands/DeleteProjectCommand";
import {
  DeleteReportCommandInput,
  DeleteReportCommandOutput,
} from "./commands/DeleteReportCommand";
import {
  DeleteReportGroupCommandInput,
  DeleteReportGroupCommandOutput,
} from "./commands/DeleteReportGroupCommand";
import {
  DeleteResourcePolicyCommandInput,
  DeleteResourcePolicyCommandOutput,
} from "./commands/DeleteResourcePolicyCommand";
import {
  DeleteSourceCredentialsCommandInput,
  DeleteSourceCredentialsCommandOutput,
} from "./commands/DeleteSourceCredentialsCommand";
import {
  DeleteWebhookCommandInput,
  DeleteWebhookCommandOutput,
} from "./commands/DeleteWebhookCommand";
import {
  DescribeCodeCoveragesCommandInput,
  DescribeCodeCoveragesCommandOutput,
} from "./commands/DescribeCodeCoveragesCommand";
import {
  DescribeTestCasesCommandInput,
  DescribeTestCasesCommandOutput,
} from "./commands/DescribeTestCasesCommand";
import {
  GetReportGroupTrendCommandInput,
  GetReportGroupTrendCommandOutput,
} from "./commands/GetReportGroupTrendCommand";
import {
  GetResourcePolicyCommandInput,
  GetResourcePolicyCommandOutput,
} from "./commands/GetResourcePolicyCommand";
import {
  ImportSourceCredentialsCommandInput,
  ImportSourceCredentialsCommandOutput,
} from "./commands/ImportSourceCredentialsCommand";
import {
  InvalidateProjectCacheCommandInput,
  InvalidateProjectCacheCommandOutput,
} from "./commands/InvalidateProjectCacheCommand";
import {
  ListBuildBatchesCommandInput,
  ListBuildBatchesCommandOutput,
} from "./commands/ListBuildBatchesCommand";
import {
  ListBuildBatchesForProjectCommandInput,
  ListBuildBatchesForProjectCommandOutput,
} from "./commands/ListBuildBatchesForProjectCommand";
import {
  ListBuildsCommandInput,
  ListBuildsCommandOutput,
} from "./commands/ListBuildsCommand";
import {
  ListBuildsForProjectCommandInput,
  ListBuildsForProjectCommandOutput,
} from "./commands/ListBuildsForProjectCommand";
import {
  ListCommandExecutionsForSandboxCommandInput,
  ListCommandExecutionsForSandboxCommandOutput,
} from "./commands/ListCommandExecutionsForSandboxCommand";
import {
  ListCuratedEnvironmentImagesCommandInput,
  ListCuratedEnvironmentImagesCommandOutput,
} from "./commands/ListCuratedEnvironmentImagesCommand";
import {
  ListFleetsCommandInput,
  ListFleetsCommandOutput,
} from "./commands/ListFleetsCommand";
import {
  ListProjectsCommandInput,
  ListProjectsCommandOutput,
} from "./commands/ListProjectsCommand";
import {
  ListReportGroupsCommandInput,
  ListReportGroupsCommandOutput,
} from "./commands/ListReportGroupsCommand";
import {
  ListReportsCommandInput,
  ListReportsCommandOutput,
} from "./commands/ListReportsCommand";
import {
  ListReportsForReportGroupCommandInput,
  ListReportsForReportGroupCommandOutput,
} from "./commands/ListReportsForReportGroupCommand";
import {
  ListSandboxesCommandInput,
  ListSandboxesCommandOutput,
} from "./commands/ListSandboxesCommand";
import {
  ListSandboxesForProjectCommandInput,
  ListSandboxesForProjectCommandOutput,
} from "./commands/ListSandboxesForProjectCommand";
import {
  ListSharedProjectsCommandInput,
  ListSharedProjectsCommandOutput,
} from "./commands/ListSharedProjectsCommand";
import {
  ListSharedReportGroupsCommandInput,
  ListSharedReportGroupsCommandOutput,
} from "./commands/ListSharedReportGroupsCommand";
import {
  ListSourceCredentialsCommandInput,
  ListSourceCredentialsCommandOutput,
} from "./commands/ListSourceCredentialsCommand";
import {
  PutResourcePolicyCommandInput,
  PutResourcePolicyCommandOutput,
} from "./commands/PutResourcePolicyCommand";
import {
  RetryBuildBatchCommandInput,
  RetryBuildBatchCommandOutput,
} from "./commands/RetryBuildBatchCommand";
import {
  RetryBuildCommandInput,
  RetryBuildCommandOutput,
} from "./commands/RetryBuildCommand";
import {
  StartBuildBatchCommandInput,
  StartBuildBatchCommandOutput,
} from "./commands/StartBuildBatchCommand";
import {
  StartBuildCommandInput,
  StartBuildCommandOutput,
} from "./commands/StartBuildCommand";
import {
  StartCommandExecutionCommandInput,
  StartCommandExecutionCommandOutput,
} from "./commands/StartCommandExecutionCommand";
import {
  StartSandboxCommandInput,
  StartSandboxCommandOutput,
} from "./commands/StartSandboxCommand";
import {
  StartSandboxConnectionCommandInput,
  StartSandboxConnectionCommandOutput,
} from "./commands/StartSandboxConnectionCommand";
import {
  StopBuildBatchCommandInput,
  StopBuildBatchCommandOutput,
} from "./commands/StopBuildBatchCommand";
import {
  StopBuildCommandInput,
  StopBuildCommandOutput,
} from "./commands/StopBuildCommand";
import {
  StopSandboxCommandInput,
  StopSandboxCommandOutput,
} from "./commands/StopSandboxCommand";
import {
  UpdateFleetCommandInput,
  UpdateFleetCommandOutput,
} from "./commands/UpdateFleetCommand";
import {
  UpdateProjectCommandInput,
  UpdateProjectCommandOutput,
} from "./commands/UpdateProjectCommand";
import {
  UpdateProjectVisibilityCommandInput,
  UpdateProjectVisibilityCommandOutput,
} from "./commands/UpdateProjectVisibilityCommand";
import {
  UpdateReportGroupCommandInput,
  UpdateReportGroupCommandOutput,
} from "./commands/UpdateReportGroupCommand";
import {
  UpdateWebhookCommandInput,
  UpdateWebhookCommandOutput,
} from "./commands/UpdateWebhookCommand";
import {
  ClientInputEndpointParameters,
  ClientResolvedEndpointParameters,
  EndpointParameters,
} from "./endpoint/EndpointParameters";
import { RuntimeExtension, RuntimeExtensionsConfig } from "./runtimeExtensions";
export { __Client };
export type ServiceInputTypes =
  | BatchDeleteBuildsCommandInput
  | BatchGetBuildBatchesCommandInput
  | BatchGetBuildsCommandInput
  | BatchGetCommandExecutionsCommandInput
  | BatchGetFleetsCommandInput
  | BatchGetProjectsCommandInput
  | BatchGetReportGroupsCommandInput
  | BatchGetReportsCommandInput
  | BatchGetSandboxesCommandInput
  | CreateFleetCommandInput
  | CreateProjectCommandInput
  | CreateReportGroupCommandInput
  | CreateWebhookCommandInput
  | DeleteBuildBatchCommandInput
  | DeleteFleetCommandInput
  | DeleteProjectCommandInput
  | DeleteReportCommandInput
  | DeleteReportGroupCommandInput
  | DeleteResourcePolicyCommandInput
  | DeleteSourceCredentialsCommandInput
  | DeleteWebhookCommandInput
  | DescribeCodeCoveragesCommandInput
  | DescribeTestCasesCommandInput
  | GetReportGroupTrendCommandInput
  | GetResourcePolicyCommandInput
  | ImportSourceCredentialsCommandInput
  | InvalidateProjectCacheCommandInput
  | ListBuildBatchesCommandInput
  | ListBuildBatchesForProjectCommandInput
  | ListBuildsCommandInput
  | ListBuildsForProjectCommandInput
  | ListCommandExecutionsForSandboxCommandInput
  | ListCuratedEnvironmentImagesCommandInput
  | ListFleetsCommandInput
  | ListProjectsCommandInput
  | ListReportGroupsCommandInput
  | ListReportsCommandInput
  | ListReportsForReportGroupCommandInput
  | ListSandboxesCommandInput
  | ListSandboxesForProjectCommandInput
  | ListSharedProjectsCommandInput
  | ListSharedReportGroupsCommandInput
  | ListSourceCredentialsCommandInput
  | PutResourcePolicyCommandInput
  | RetryBuildBatchCommandInput
  | RetryBuildCommandInput
  | StartBuildBatchCommandInput
  | StartBuildCommandInput
  | StartCommandExecutionCommandInput
  | StartSandboxCommandInput
  | StartSandboxConnectionCommandInput
  | StopBuildBatchCommandInput
  | StopBuildCommandInput
  | StopSandboxCommandInput
  | UpdateFleetCommandInput
  | UpdateProjectCommandInput
  | UpdateProjectVisibilityCommandInput
  | UpdateReportGroupCommandInput
  | UpdateWebhookCommandInput;
export type ServiceOutputTypes =
  | BatchDeleteBuildsCommandOutput
  | BatchGetBuildBatchesCommandOutput
  | BatchGetBuildsCommandOutput
  | BatchGetCommandExecutionsCommandOutput
  | BatchGetFleetsCommandOutput
  | BatchGetProjectsCommandOutput
  | BatchGetReportGroupsCommandOutput
  | BatchGetReportsCommandOutput
  | BatchGetSandboxesCommandOutput
  | CreateFleetCommandOutput
  | CreateProjectCommandOutput
  | CreateReportGroupCommandOutput
  | CreateWebhookCommandOutput
  | DeleteBuildBatchCommandOutput
  | DeleteFleetCommandOutput
  | DeleteProjectCommandOutput
  | DeleteReportCommandOutput
  | DeleteReportGroupCommandOutput
  | DeleteResourcePolicyCommandOutput
  | DeleteSourceCredentialsCommandOutput
  | DeleteWebhookCommandOutput
  | DescribeCodeCoveragesCommandOutput
  | DescribeTestCasesCommandOutput
  | GetReportGroupTrendCommandOutput
  | GetResourcePolicyCommandOutput
  | ImportSourceCredentialsCommandOutput
  | InvalidateProjectCacheCommandOutput
  | ListBuildBatchesCommandOutput
  | ListBuildBatchesForProjectCommandOutput
  | ListBuildsCommandOutput
  | ListBuildsForProjectCommandOutput
  | ListCommandExecutionsForSandboxCommandOutput
  | ListCuratedEnvironmentImagesCommandOutput
  | ListFleetsCommandOutput
  | ListProjectsCommandOutput
  | ListReportGroupsCommandOutput
  | ListReportsCommandOutput
  | ListReportsForReportGroupCommandOutput
  | ListSandboxesCommandOutput
  | ListSandboxesForProjectCommandOutput
  | ListSharedProjectsCommandOutput
  | ListSharedReportGroupsCommandOutput
  | ListSourceCredentialsCommandOutput
  | PutResourcePolicyCommandOutput
  | RetryBuildBatchCommandOutput
  | RetryBuildCommandOutput
  | StartBuildBatchCommandOutput
  | StartBuildCommandOutput
  | StartCommandExecutionCommandOutput
  | StartSandboxCommandOutput
  | StartSandboxConnectionCommandOutput
  | StopBuildBatchCommandOutput
  | StopBuildCommandOutput
  | StopSandboxCommandOutput
  | UpdateFleetCommandOutput
  | UpdateProjectCommandOutput
  | UpdateProjectVisibilityCommandOutput
  | UpdateReportGroupCommandOutput
  | UpdateWebhookCommandOutput;
export interface ClientDefaults
  extends Partial<__SmithyConfiguration<__HttpHandlerOptions>> {
  requestHandler?: __HttpHandlerUserInput;
  sha256?: __ChecksumConstructor | __HashConstructor;
  urlParser?: __UrlParser;
  bodyLengthChecker?: __BodyLengthCalculator;
  streamCollector?: __StreamCollector;
  base64Decoder?: __Decoder;
  base64Encoder?: __Encoder;
  utf8Decoder?: __Decoder;
  utf8Encoder?: __Encoder;
  runtime?: string;
  disableHostPrefix?: boolean;
  serviceId?: string;
  useDualstackEndpoint?: boolean | __Provider<boolean>;
  useFipsEndpoint?: boolean | __Provider<boolean>;
  region?: string | __Provider<string>;
  profile?: string;
  defaultUserAgentProvider?: Provider<__UserAgent>;
  credentialDefaultProvider?: (input: any) => AwsCredentialIdentityProvider;
  maxAttempts?: number | __Provider<number>;
  retryMode?: string | __Provider<string>;
  logger?: __Logger;
  extensions?: RuntimeExtension[];
  defaultsMode?: __DefaultsMode | __Provider<__DefaultsMode>;
}
export type CodeBuildClientConfigType = Partial<
  __SmithyConfiguration<__HttpHandlerOptions>
> &
  ClientDefaults &
  UserAgentInputConfig &
  RetryInputConfig &
  RegionInputConfig &
  HostHeaderInputConfig &
  EndpointInputConfig<EndpointParameters> &
  HttpAuthSchemeInputConfig &
  ClientInputEndpointParameters;
export interface CodeBuildClientConfig extends CodeBuildClientConfigType {}
export type CodeBuildClientResolvedConfigType =
  __SmithyResolvedConfiguration<__HttpHandlerOptions> &
    Required<ClientDefaults> &
    RuntimeExtensionsConfig &
    UserAgentResolvedConfig &
    RetryResolvedConfig &
    RegionResolvedConfig &
    HostHeaderResolvedConfig &
    EndpointResolvedConfig<EndpointParameters> &
    HttpAuthSchemeResolvedConfig &
    ClientResolvedEndpointParameters;
export interface CodeBuildClientResolvedConfig
  extends CodeBuildClientResolvedConfigType {}
export declare class CodeBuildClient extends __Client<
  __HttpHandlerOptions,
  ServiceInputTypes,
  ServiceOutputTypes,
  CodeBuildClientResolvedConfig
> {
  readonly config: CodeBuildClientResolvedConfig;
  constructor(
    ...[configuration]: __CheckOptionalClientConfig<CodeBuildClientConfig>
  );
  destroy(): void;
}
