import { HostHeaderInputConfig, HostHeaderResolvedConfig } from "@aws-sdk/middleware-host-header";
import { UserAgentInputConfig, UserAgentResolvedConfig } from "@aws-sdk/middleware-user-agent";
import { RegionInputConfig, RegionResolvedConfig } from "@smithy/config-resolver";
import { EndpointInputConfig, EndpointResolvedConfig } from "@smithy/middleware-endpoint";
import { RetryInputConfig, RetryResolvedConfig } from "@smithy/middleware-retry";
import { HttpHandlerUserInput as __HttpHandlerUserInput } from "@smithy/protocol-http";
import { Client as __Client, DefaultsMode as __DefaultsMode, SmithyConfiguration as __SmithyConfiguration, SmithyResolvedConfiguration as __SmithyResolvedConfiguration } from "@smithy/smithy-client";
import { AwsCredentialIdentityProvider, BodyLengthCalculator as __BodyLengthCalculator, CheckOptionalClientConfig as __CheckOptionalClientConfig, ChecksumConstructor as __ChecksumConstructor, Decoder as __Decoder, Encoder as __Encoder, HashConstructor as __HashConstructor, HttpHandlerOptions as __HttpHandlerOptions, Logger as __Logger, Provider as __Provider, Provider, StreamCollector as __StreamCollector, UrlParser as __UrlParser, UserAgent as __UserAgent } from "@smithy/types";
import { HttpAuthSchemeInputConfig, HttpAuthSchemeResolvedConfig } from "./auth/httpAuthSchemeProvider";
import { BatchDeleteBuildsCommandInput, BatchDeleteBuildsCommandOutput } from "./commands/BatchDeleteBuildsCommand";
import { BatchGetBuildBatchesCommandInput, BatchGetBuildBatchesCommandOutput } from "./commands/BatchGetBuildBatchesCommand";
import { BatchGetBuildsCommandInput, BatchGetBuildsCommandOutput } from "./commands/BatchGetBuildsCommand";
import { BatchGetCommandExecutionsCommandInput, BatchGetCommandExecutionsCommandOutput } from "./commands/BatchGetCommandExecutionsCommand";
import { BatchGetFleetsCommandInput, BatchGetFleetsCommandOutput } from "./commands/BatchGetFleetsCommand";
import { BatchGetProjectsCommandInput, BatchGetProjectsCommandOutput } from "./commands/BatchGetProjectsCommand";
import { BatchGetReportGroupsCommandInput, BatchGetReportGroupsCommandOutput } from "./commands/BatchGetReportGroupsCommand";
import { BatchGetReportsCommandInput, BatchGetReportsCommandOutput } from "./commands/BatchGetReportsCommand";
import { BatchGetSandboxesCommandInput, BatchGetSandboxesCommandOutput } from "./commands/BatchGetSandboxesCommand";
import { CreateFleetCommandInput, CreateFleetCommandOutput } from "./commands/CreateFleetCommand";
import { CreateProjectCommandInput, CreateProjectCommandOutput } from "./commands/CreateProjectCommand";
import { CreateReportGroupCommandInput, CreateReportGroupCommandOutput } from "./commands/CreateReportGroupCommand";
import { CreateWebhookCommandInput, CreateWebhookCommandOutput } from "./commands/CreateWebhookCommand";
import { DeleteBuildBatchCommandInput, DeleteBuildBatchCommandOutput } from "./commands/DeleteBuildBatchCommand";
import { DeleteFleetCommandInput, DeleteFleetCommandOutput } from "./commands/DeleteFleetCommand";
import { DeleteProjectCommandInput, DeleteProjectCommandOutput } from "./commands/DeleteProjectCommand";
import { DeleteReportCommandInput, DeleteReportCommandOutput } from "./commands/DeleteReportCommand";
import { DeleteReportGroupCommandInput, DeleteReportGroupCommandOutput } from "./commands/DeleteReportGroupCommand";
import { DeleteResourcePolicyCommandInput, DeleteResourcePolicyCommandOutput } from "./commands/DeleteResourcePolicyCommand";
import { DeleteSourceCredentialsCommandInput, DeleteSourceCredentialsCommandOutput } from "./commands/DeleteSourceCredentialsCommand";
import { DeleteWebhookCommandInput, DeleteWebhookCommandOutput } from "./commands/DeleteWebhookCommand";
import { DescribeCodeCoveragesCommandInput, DescribeCodeCoveragesCommandOutput } from "./commands/DescribeCodeCoveragesCommand";
import { DescribeTestCasesCommandInput, DescribeTestCasesCommandOutput } from "./commands/DescribeTestCasesCommand";
import { GetReportGroupTrendCommandInput, GetReportGroupTrendCommandOutput } from "./commands/GetReportGroupTrendCommand";
import { GetResourcePolicyCommandInput, GetResourcePolicyCommandOutput } from "./commands/GetResourcePolicyCommand";
import { ImportSourceCredentialsCommandInput, ImportSourceCredentialsCommandOutput } from "./commands/ImportSourceCredentialsCommand";
import { InvalidateProjectCacheCommandInput, InvalidateProjectCacheCommandOutput } from "./commands/InvalidateProjectCacheCommand";
import { ListBuildBatchesCommandInput, ListBuildBatchesCommandOutput } from "./commands/ListBuildBatchesCommand";
import { ListBuildBatchesForProjectCommandInput, ListBuildBatchesForProjectCommandOutput } from "./commands/ListBuildBatchesForProjectCommand";
import { ListBuildsCommandInput, ListBuildsCommandOutput } from "./commands/ListBuildsCommand";
import { ListBuildsForProjectCommandInput, ListBuildsForProjectCommandOutput } from "./commands/ListBuildsForProjectCommand";
import { ListCommandExecutionsForSandboxCommandInput, ListCommandExecutionsForSandboxCommandOutput } from "./commands/ListCommandExecutionsForSandboxCommand";
import { ListCuratedEnvironmentImagesCommandInput, ListCuratedEnvironmentImagesCommandOutput } from "./commands/ListCuratedEnvironmentImagesCommand";
import { ListFleetsCommandInput, ListFleetsCommandOutput } from "./commands/ListFleetsCommand";
import { ListProjectsCommandInput, ListProjectsCommandOutput } from "./commands/ListProjectsCommand";
import { ListReportGroupsCommandInput, ListReportGroupsCommandOutput } from "./commands/ListReportGroupsCommand";
import { ListReportsCommandInput, ListReportsCommandOutput } from "./commands/ListReportsCommand";
import { ListReportsForReportGroupCommandInput, ListReportsForReportGroupCommandOutput } from "./commands/ListReportsForReportGroupCommand";
import { ListSandboxesCommandInput, ListSandboxesCommandOutput } from "./commands/ListSandboxesCommand";
import { ListSandboxesForProjectCommandInput, ListSandboxesForProjectCommandOutput } from "./commands/ListSandboxesForProjectCommand";
import { ListSharedProjectsCommandInput, ListSharedProjectsCommandOutput } from "./commands/ListSharedProjectsCommand";
import { ListSharedReportGroupsCommandInput, ListSharedReportGroupsCommandOutput } from "./commands/ListSharedReportGroupsCommand";
import { ListSourceCredentialsCommandInput, ListSourceCredentialsCommandOutput } from "./commands/ListSourceCredentialsCommand";
import { PutResourcePolicyCommandInput, PutResourcePolicyCommandOutput } from "./commands/PutResourcePolicyCommand";
import { RetryBuildBatchCommandInput, RetryBuildBatchCommandOutput } from "./commands/RetryBuildBatchCommand";
import { RetryBuildCommandInput, RetryBuildCommandOutput } from "./commands/RetryBuildCommand";
import { StartBuildBatchCommandInput, StartBuildBatchCommandOutput } from "./commands/StartBuildBatchCommand";
import { StartBuildCommandInput, StartBuildCommandOutput } from "./commands/StartBuildCommand";
import { StartCommandExecutionCommandInput, StartCommandExecutionCommandOutput } from "./commands/StartCommandExecutionCommand";
import { StartSandboxCommandInput, StartSandboxCommandOutput } from "./commands/StartSandboxCommand";
import { StartSandboxConnectionCommandInput, StartSandboxConnectionCommandOutput } from "./commands/StartSandboxConnectionCommand";
import { StopBuildBatchCommandInput, StopBuildBatchCommandOutput } from "./commands/StopBuildBatchCommand";
import { StopBuildCommandInput, StopBuildCommandOutput } from "./commands/StopBuildCommand";
import { StopSandboxCommandInput, StopSandboxCommandOutput } from "./commands/StopSandboxCommand";
import { UpdateFleetCommandInput, UpdateFleetCommandOutput } from "./commands/UpdateFleetCommand";
import { UpdateProjectCommandInput, UpdateProjectCommandOutput } from "./commands/UpdateProjectCommand";
import { UpdateProjectVisibilityCommandInput, UpdateProjectVisibilityCommandOutput } from "./commands/UpdateProjectVisibilityCommand";
import { UpdateReportGroupCommandInput, UpdateReportGroupCommandOutput } from "./commands/UpdateReportGroupCommand";
import { UpdateWebhookCommandInput, UpdateWebhookCommandOutput } from "./commands/UpdateWebhookCommand";
import { ClientInputEndpointParameters, ClientResolvedEndpointParameters, EndpointParameters } from "./endpoint/EndpointParameters";
import { RuntimeExtension, RuntimeExtensionsConfig } from "./runtimeExtensions";
export { __Client };
/**
 * @public
 */
export type ServiceInputTypes = BatchDeleteBuildsCommandInput | BatchGetBuildBatchesCommandInput | BatchGetBuildsCommandInput | BatchGetCommandExecutionsCommandInput | BatchGetFleetsCommandInput | BatchGetProjectsCommandInput | BatchGetReportGroupsCommandInput | BatchGetReportsCommandInput | BatchGetSandboxesCommandInput | CreateFleetCommandInput | CreateProjectCommandInput | CreateReportGroupCommandInput | CreateWebhookCommandInput | DeleteBuildBatchCommandInput | DeleteFleetCommandInput | DeleteProjectCommandInput | DeleteReportCommandInput | DeleteReportGroupCommandInput | DeleteResourcePolicyCommandInput | DeleteSourceCredentialsCommandInput | DeleteWebhookCommandInput | DescribeCodeCoveragesCommandInput | DescribeTestCasesCommandInput | GetReportGroupTrendCommandInput | GetResourcePolicyCommandInput | ImportSourceCredentialsCommandInput | InvalidateProjectCacheCommandInput | ListBuildBatchesCommandInput | ListBuildBatchesForProjectCommandInput | ListBuildsCommandInput | ListBuildsForProjectCommandInput | ListCommandExecutionsForSandboxCommandInput | ListCuratedEnvironmentImagesCommandInput | ListFleetsCommandInput | ListProjectsCommandInput | ListReportGroupsCommandInput | ListReportsCommandInput | ListReportsForReportGroupCommandInput | ListSandboxesCommandInput | ListSandboxesForProjectCommandInput | ListSharedProjectsCommandInput | ListSharedReportGroupsCommandInput | ListSourceCredentialsCommandInput | PutResourcePolicyCommandInput | RetryBuildBatchCommandInput | RetryBuildCommandInput | StartBuildBatchCommandInput | StartBuildCommandInput | StartCommandExecutionCommandInput | StartSandboxCommandInput | StartSandboxConnectionCommandInput | StopBuildBatchCommandInput | StopBuildCommandInput | StopSandboxCommandInput | UpdateFleetCommandInput | UpdateProjectCommandInput | UpdateProjectVisibilityCommandInput | UpdateReportGroupCommandInput | UpdateWebhookCommandInput;
/**
 * @public
 */
export type ServiceOutputTypes = BatchDeleteBuildsCommandOutput | BatchGetBuildBatchesCommandOutput | BatchGetBuildsCommandOutput | BatchGetCommandExecutionsCommandOutput | BatchGetFleetsCommandOutput | BatchGetProjectsCommandOutput | BatchGetReportGroupsCommandOutput | BatchGetReportsCommandOutput | BatchGetSandboxesCommandOutput | CreateFleetCommandOutput | CreateProjectCommandOutput | CreateReportGroupCommandOutput | CreateWebhookCommandOutput | DeleteBuildBatchCommandOutput | DeleteFleetCommandOutput | DeleteProjectCommandOutput | DeleteReportCommandOutput | DeleteReportGroupCommandOutput | DeleteResourcePolicyCommandOutput | DeleteSourceCredentialsCommandOutput | DeleteWebhookCommandOutput | DescribeCodeCoveragesCommandOutput | DescribeTestCasesCommandOutput | GetReportGroupTrendCommandOutput | GetResourcePolicyCommandOutput | ImportSourceCredentialsCommandOutput | InvalidateProjectCacheCommandOutput | ListBuildBatchesCommandOutput | ListBuildBatchesForProjectCommandOutput | ListBuildsCommandOutput | ListBuildsForProjectCommandOutput | ListCommandExecutionsForSandboxCommandOutput | ListCuratedEnvironmentImagesCommandOutput | ListFleetsCommandOutput | ListProjectsCommandOutput | ListReportGroupsCommandOutput | ListReportsCommandOutput | ListReportsForReportGroupCommandOutput | ListSandboxesCommandOutput | ListSandboxesForProjectCommandOutput | ListSharedProjectsCommandOutput | ListSharedReportGroupsCommandOutput | ListSourceCredentialsCommandOutput | PutResourcePolicyCommandOutput | RetryBuildBatchCommandOutput | RetryBuildCommandOutput | StartBuildBatchCommandOutput | StartBuildCommandOutput | StartCommandExecutionCommandOutput | StartSandboxCommandOutput | StartSandboxConnectionCommandOutput | StopBuildBatchCommandOutput | StopBuildCommandOutput | StopSandboxCommandOutput | UpdateFleetCommandOutput | UpdateProjectCommandOutput | UpdateProjectVisibilityCommandOutput | UpdateReportGroupCommandOutput | UpdateWebhookCommandOutput;
/**
 * @public
 */
export interface ClientDefaults extends Partial<__SmithyConfiguration<__HttpHandlerOptions>> {
    /**
     * The HTTP handler to use or its constructor options. Fetch in browser and Https in Nodejs.
     */
    requestHandler?: __HttpHandlerUserInput;
    /**
     * A constructor for a class implementing the {@link @smithy/types#ChecksumConstructor} interface
     * that computes the SHA-256 HMAC or checksum of a string or binary buffer.
     * @internal
     */
    sha256?: __ChecksumConstructor | __HashConstructor;
    /**
     * The function that will be used to convert strings into HTTP endpoints.
     * @internal
     */
    urlParser?: __UrlParser;
    /**
     * A function that can calculate the length of a request body.
     * @internal
     */
    bodyLengthChecker?: __BodyLengthCalculator;
    /**
     * A function that converts a stream into an array of bytes.
     * @internal
     */
    streamCollector?: __StreamCollector;
    /**
     * The function that will be used to convert a base64-encoded string to a byte array.
     * @internal
     */
    base64Decoder?: __Decoder;
    /**
     * The function that will be used to convert binary data to a base64-encoded string.
     * @internal
     */
    base64Encoder?: __Encoder;
    /**
     * The function that will be used to convert a UTF8-encoded string to a byte array.
     * @internal
     */
    utf8Decoder?: __Decoder;
    /**
     * The function that will be used to convert binary data to a UTF-8 encoded string.
     * @internal
     */
    utf8Encoder?: __Encoder;
    /**
     * The runtime environment.
     * @internal
     */
    runtime?: string;
    /**
     * Disable dynamically changing the endpoint of the client based on the hostPrefix
     * trait of an operation.
     */
    disableHostPrefix?: boolean;
    /**
     * Unique service identifier.
     * @internal
     */
    serviceId?: string;
    /**
     * Enables IPv6/IPv4 dualstack endpoint.
     */
    useDualstackEndpoint?: boolean | __Provider<boolean>;
    /**
     * Enables FIPS compatible endpoints.
     */
    useFipsEndpoint?: boolean | __Provider<boolean>;
    /**
     * The AWS region to which this client will send requests
     */
    region?: string | __Provider<string>;
    /**
     * Setting a client profile is similar to setting a value for the
     * AWS_PROFILE environment variable. Setting a profile on a client
     * in code only affects the single client instance, unlike AWS_PROFILE.
     *
     * When set, and only for environments where an AWS configuration
     * file exists, fields configurable by this file will be retrieved
     * from the specified profile within that file.
     * Conflicting code configuration and environment variables will
     * still have higher priority.
     *
     * For client credential resolution that involves checking the AWS
     * configuration file, the client's profile (this value) will be
     * used unless a different profile is set in the credential
     * provider options.
     *
     */
    profile?: string;
    /**
     * The provider populating default tracking information to be sent with `user-agent`, `x-amz-user-agent` header
     * @internal
     */
    defaultUserAgentProvider?: Provider<__UserAgent>;
    /**
     * Default credentials provider; Not available in browser runtime.
     * @deprecated
     * @internal
     */
    credentialDefaultProvider?: (input: any) => AwsCredentialIdentityProvider;
    /**
     * Value for how many times a request will be made at most in case of retry.
     */
    maxAttempts?: number | __Provider<number>;
    /**
     * Specifies which retry algorithm to use.
     * @see https://docs.aws.amazon.com/AWSJavaScriptSDK/v3/latest/Package/-smithy-util-retry/Enum/RETRY_MODES/
     *
     */
    retryMode?: string | __Provider<string>;
    /**
     * Optional logger for logging debug/info/warn/error.
     */
    logger?: __Logger;
    /**
     * Optional extensions
     */
    extensions?: RuntimeExtension[];
    /**
     * The {@link @smithy/smithy-client#DefaultsMode} that will be used to determine how certain default configuration options are resolved in the SDK.
     */
    defaultsMode?: __DefaultsMode | __Provider<__DefaultsMode>;
}
/**
 * @public
 */
export type CodeBuildClientConfigType = Partial<__SmithyConfiguration<__HttpHandlerOptions>> & ClientDefaults & UserAgentInputConfig & RetryInputConfig & RegionInputConfig & HostHeaderInputConfig & EndpointInputConfig<EndpointParameters> & HttpAuthSchemeInputConfig & ClientInputEndpointParameters;
/**
 * @public
 *
 *  The configuration interface of CodeBuildClient class constructor that set the region, credentials and other options.
 */
export interface CodeBuildClientConfig extends CodeBuildClientConfigType {
}
/**
 * @public
 */
export type CodeBuildClientResolvedConfigType = __SmithyResolvedConfiguration<__HttpHandlerOptions> & Required<ClientDefaults> & RuntimeExtensionsConfig & UserAgentResolvedConfig & RetryResolvedConfig & RegionResolvedConfig & HostHeaderResolvedConfig & EndpointResolvedConfig<EndpointParameters> & HttpAuthSchemeResolvedConfig & ClientResolvedEndpointParameters;
/**
 * @public
 *
 *  The resolved configuration interface of CodeBuildClient class. This is resolved and normalized from the {@link CodeBuildClientConfig | constructor configuration interface}.
 */
export interface CodeBuildClientResolvedConfig extends CodeBuildClientResolvedConfigType {
}
/**
 * <fullname>CodeBuild</fullname>
 *          <p>CodeBuild is a fully managed build service in the cloud. CodeBuild compiles your source code,
 *             runs unit tests, and produces artifacts that are ready to deploy. CodeBuild eliminates the
 *             need to provision, manage, and scale your own build servers. It provides prepackaged
 *             build environments for the most popular programming languages and build tools, such as
 *             Apache Maven, Gradle, and more. You can also fully customize build environments in CodeBuild
 *             to use your own build tools. CodeBuild scales automatically to meet peak build requests. You
 *             pay only for the build time you consume. For more information about CodeBuild, see the <i>
 *                <a href="https://docs.aws.amazon.com/codebuild/latest/userguide/welcome.html">CodeBuild User
 *                     Guide</a>.</i>
 *          </p>
 * @public
 */
export declare class CodeBuildClient extends __Client<__HttpHandlerOptions, ServiceInputTypes, ServiceOutputTypes, CodeBuildClientResolvedConfig> {
    /**
     * The resolved configuration of CodeBuildClient class. This is resolved and normalized from the {@link CodeBuildClientConfig | constructor configuration interface}.
     */
    readonly config: CodeBuildClientResolvedConfig;
    constructor(...[configuration]: __CheckOptionalClientConfig<CodeBuildClientConfig>);
    /**
     * Destroy underlying resources, like sockets. It's usually not necessary to do this.
     * However in Node.js, it's best to explicitly shut down the client's agent when it is no longer needed.
     * Otherwise, sockets might stay open for quite a long time before the server terminates them.
     */
    destroy(): void;
}
