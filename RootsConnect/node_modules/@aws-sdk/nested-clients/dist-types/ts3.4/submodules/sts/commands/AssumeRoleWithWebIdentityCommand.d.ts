import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  AssumeRoleWithWebIdentityRequest,
  AssumeRoleWithWebIdentityResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  STSClientResolvedConfig,
} from "../STSClient";
export { __MetadataBearer };
export { $Command };
export interface AssumeRoleWithWebIdentityCommandInput
  extends AssumeRoleWithWebIdentityRequest {}
export interface AssumeRoleWithWebIdentityCommandOutput
  extends AssumeRoleWithWebIdentityResponse,
    __MetadataBearer {}
declare const AssumeRoleWithWebIdentityCommand_base: {
  new (
    input: AssumeRoleWithWebIdentityCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    AssumeRoleWithWebIdentityCommandInput,
    AssumeRoleWithWebIdentityCommandOutput,
    STSClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: AssumeRoleWithWebIdentityCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    AssumeRoleWithWebIdentityCommandInput,
    AssumeRoleWithWebIdentityCommandOutput,
    STSClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class AssumeRoleWithWebIdentityCommand extends AssumeRoleWithWebIdentityCommand_base {
  protected static __types: {
    api: {
      input: AssumeRoleWithWebIdentityRequest;
      output: AssumeRoleWithWebIdentityResponse;
    };
    sdk: {
      input: AssumeRoleWithWebIdentityCommandInput;
      output: AssumeRoleWithWebIdentityCommandOutput;
    };
  };
}
