import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_CreateDefaultSubnetCommand, se_CreateDefaultSubnetCommand } from "../protocols/Aws_ec2";
export { $Command };
export class CreateDefaultSubnetCommand extends $Command
    .classBuilder()
    .ep({
    ...commonParams,
})
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonEC2", "CreateDefaultSubnet", {})
    .n("EC2Client", "CreateDefaultSubnetCommand")
    .f(void 0, void 0)
    .ser(se_CreateDefaultSubnetCommand)
    .de(de_CreateDefaultSubnetCommand)
    .build() {
}
