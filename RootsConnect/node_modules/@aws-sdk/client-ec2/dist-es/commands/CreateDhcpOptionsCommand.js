import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_CreateDhcpOptionsCommand, se_CreateDhcpOptionsCommand } from "../protocols/Aws_ec2";
export { $Command };
export class CreateDhcpOptionsCommand extends $Command
    .classBuilder()
    .ep({
    ...commonParams,
})
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("AmazonEC2", "CreateDhcpOptions", {})
    .n("EC2Client", "CreateDhcpOptionsCommand")
    .f(void 0, void 0)
    .ser(se_CreateDhcpOptionsCommand)
    .de(de_CreateDhcpOptionsCommand)
    .build() {
}
