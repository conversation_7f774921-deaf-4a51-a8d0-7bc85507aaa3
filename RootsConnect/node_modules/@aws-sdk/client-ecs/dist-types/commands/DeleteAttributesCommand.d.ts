import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { ECSClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes } from "../ECSClient";
import { DeleteAttributesRequest, DeleteAttributesResponse } from "../models/models_0";
/**
 * @public
 */
export type { __MetadataBearer };
export { $Command };
/**
 * @public
 *
 * The input for {@link DeleteAttributesCommand}.
 */
export interface DeleteAttributesCommandInput extends DeleteAttributesRequest {
}
/**
 * @public
 *
 * The output of {@link DeleteAttributesCommand}.
 */
export interface DeleteAttributesCommandOutput extends DeleteAttributesResponse, __MetadataBearer {
}
declare const DeleteAttributesCommand_base: {
    new (input: DeleteAttributesCommandInput): import("@smithy/smithy-client").CommandImpl<DeleteAttributesCommandInput, DeleteAttributesCommandOutput, ECSClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    new (input: DeleteAttributesCommandInput): import("@smithy/smithy-client").CommandImpl<DeleteAttributesCommandInput, DeleteAttributesCommandOutput, ECSClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
/**
 * <p>Deletes one or more custom attributes from an Amazon ECS resource.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { ECSClient, DeleteAttributesCommand } from "@aws-sdk/client-ecs"; // ES Modules import
 * // const { ECSClient, DeleteAttributesCommand } = require("@aws-sdk/client-ecs"); // CommonJS import
 * const client = new ECSClient(config);
 * const input = { // DeleteAttributesRequest
 *   cluster: "STRING_VALUE",
 *   attributes: [ // Attributes // required
 *     { // Attribute
 *       name: "STRING_VALUE", // required
 *       value: "STRING_VALUE",
 *       targetType: "container-instance",
 *       targetId: "STRING_VALUE",
 *     },
 *   ],
 * };
 * const command = new DeleteAttributesCommand(input);
 * const response = await client.send(command);
 * // { // DeleteAttributesResponse
 * //   attributes: [ // Attributes
 * //     { // Attribute
 * //       name: "STRING_VALUE", // required
 * //       value: "STRING_VALUE",
 * //       targetType: "container-instance",
 * //       targetId: "STRING_VALUE",
 * //     },
 * //   ],
 * // };
 *
 * ```
 *
 * @param DeleteAttributesCommandInput - {@link DeleteAttributesCommandInput}
 * @returns {@link DeleteAttributesCommandOutput}
 * @see {@link DeleteAttributesCommandInput} for command's `input` shape.
 * @see {@link DeleteAttributesCommandOutput} for command's `response` shape.
 * @see {@link ECSClientResolvedConfig | config} for ECSClient's `config` shape.
 *
 * @throws {@link ClusterNotFoundException} (client fault)
 *  <p>The specified cluster wasn't found. You can view your available clusters with <a href="https://docs.aws.amazon.com/AmazonECS/latest/APIReference/API_ListClusters.html">ListClusters</a>. Amazon ECS clusters are Region specific.</p>
 *
 * @throws {@link InvalidParameterException} (client fault)
 *  <p>The specified parameter isn't valid. Review the available parameters for the API
 * 			request.</p>
 *          <p>For more information about service event errors, see <a href="https://docs.aws.amazon.com/AmazonECS/latest/developerguide/service-event-messages-list.html">Amazon ECS service
 * 				event messages</a>. </p>
 *
 * @throws {@link TargetNotFoundException} (client fault)
 *  <p>The specified target wasn't found. You can view your available container instances
 * 			with <a href="https://docs.aws.amazon.com/AmazonECS/latest/APIReference/API_ListContainerInstances.html">ListContainerInstances</a>. Amazon ECS container instances are cluster-specific and
 * 			Region-specific.</p>
 *
 * @throws {@link ECSServiceException}
 * <p>Base exception class for all service exceptions from ECS service.</p>
 *
 *
 * @example To delete a custom attribute from an Amazon ECS instance
 * ```javascript
 * // This example deletes an attribute named stack from a container instance.
 * const input = {
 *   attributes: [
 *     {
 *       name: "stack",
 *       targetId: "aws:ecs:us-west-2:130757420319:container-instance/1c3be8ed-df30-47b4-8f1e-6e68ebd01f34"
 *     }
 *   ]
 * };
 * const command = new DeleteAttributesCommand(input);
 * const response = await client.send(command);
 * /* response is
 * {
 *   attributes: [
 *     {
 *       name: "stack",
 *       targetId: "aws:ecs:us-west-2:130757420319:container-instance/1c3be8ed-df30-47b4-8f1e-6e68ebd01f34",
 *       value: "production"
 *     }
 *   ]
 * }
 * *\/
 * ```
 *
 * @public
 */
export declare class DeleteAttributesCommand extends DeleteAttributesCommand_base {
    /** @internal type navigation helper, not in runtime. */
    protected static __types: {
        api: {
            input: DeleteAttributesRequest;
            output: DeleteAttributesResponse;
        };
        sdk: {
            input: DeleteAttributesCommandInput;
            output: DeleteAttributesCommandOutput;
        };
    };
}
