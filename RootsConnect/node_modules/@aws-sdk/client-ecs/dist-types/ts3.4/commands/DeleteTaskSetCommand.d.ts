import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  ECSClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../ECSClient";
import {
  DeleteTaskSetRequest,
  DeleteTaskSetResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface DeleteTaskSetCommandInput extends DeleteTaskSetRequest {}
export interface DeleteTaskSetCommandOutput
  extends DeleteTaskSetResponse,
    __MetadataBearer {}
declare const DeleteTaskSetCommand_base: {
  new (
    input: DeleteTaskSetCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteTaskSetCommandInput,
    DeleteTaskSetCommandOutput,
    ECSClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteTaskSetCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteTaskSetCommandInput,
    DeleteTaskSetCommandOutput,
    ECSClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteTaskSetCommand extends DeleteTaskSetCommand_base {
  protected static __types: {
    api: {
      input: DeleteTaskSetRequest;
      output: DeleteTaskSetResponse;
    };
    sdk: {
      input: DeleteTaskSetCommandInput;
      output: DeleteTaskSetCommandOutput;
    };
  };
}
