import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ECSClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../ECSClient";
import {
  DeregisterTaskDefinitionRequest,
  DeregisterTaskDefinitionResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface DeregisterTaskDefinitionCommandInput
  extends DeregisterTaskDefinitionRequest {}
export interface DeregisterTaskDefinitionCommandOutput
  extends DeregisterTaskDefinitionResponse,
    __MetadataBearer {}
declare const DeregisterTaskDefinitionCommand_base: {
  new (
    input: DeregisterTaskDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeregisterTaskDefinitionCommandInput,
    DeregisterTaskDefinitionCommandOutput,
    ECSClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeregisterTaskDefinitionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeregisterTaskDefinitionCommandInput,
    DeregisterTaskDefinitionCommandOutput,
    ECSClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeregisterTaskDefinitionCommand extends DeregisterTaskDefinitionCommand_base {
  protected static __types: {
    api: {
      input: DeregisterTaskDefinitionRequest;
      output: DeregisterTaskDefinitionResponse;
    };
    sdk: {
      input: DeregisterTaskDefinitionCommandInput;
      output: DeregisterTaskDefinitionCommandOutput;
    };
  };
}
