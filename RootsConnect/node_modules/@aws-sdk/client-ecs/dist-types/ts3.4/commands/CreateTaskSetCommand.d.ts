import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  ECSClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../ECSClient";
import {
  CreateTaskSetRequest,
  CreateTaskSetResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface CreateTaskSetCommandInput extends CreateTaskSetRequest {}
export interface CreateTaskSetCommandOutput
  extends CreateTaskSetResponse,
    __MetadataBearer {}
declare const CreateTaskSetCommand_base: {
  new (
    input: CreateTaskSetCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateTaskSetCommandInput,
    CreateTaskSetCommandOutput,
    ECSClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateTaskSetCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateTaskSetCommandInput,
    CreateTaskSetCommandOutput,
    ECSClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateTaskSetCommand extends CreateTaskSetCommand_base {
  protected static __types: {
    api: {
      input: CreateTaskSetRequest;
      output: CreateTaskSetResponse;
    };
    sdk: {
      input: CreateTaskSetCommandInput;
      output: CreateTaskSetCommandOutput;
    };
  };
}
