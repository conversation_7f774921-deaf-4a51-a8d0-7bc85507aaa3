import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ECSClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../ECSClient";
import {
  DeleteAccountSettingRequest,
  DeleteAccountSettingResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface DeleteAccountSettingCommandInput
  extends DeleteAccountSettingRequest {}
export interface DeleteAccountSettingCommandOutput
  extends DeleteAccountSettingResponse,
    __MetadataBearer {}
declare const DeleteAccountSettingCommand_base: {
  new (
    input: DeleteAccountSettingCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteAccountSettingCommandInput,
    DeleteAccountSettingCommandOutput,
    ECSClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteAccountSettingCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteAccountSettingCommandInput,
    DeleteAccountSettingCommandOutput,
    ECSClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteAccountSettingCommand extends DeleteAccountSettingCommand_base {
  protected static __types: {
    api: {
      input: DeleteAccountSettingRequest;
      output: DeleteAccountSettingResponse;
    };
    sdk: {
      input: DeleteAccountSettingCommandInput;
      output: DeleteAccountSettingCommandOutput;
    };
  };
}
