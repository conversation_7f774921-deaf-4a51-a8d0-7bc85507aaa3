import {
  AwsSdkSigV4AuthInputConfig,
  AwsSdkSigV4AuthResolvedConfig,
  AwsSdkSigV4PreviouslyResolved,
} from "@aws-sdk/core";
import {
  HandlerExecutionContext,
  HttpAuthScheme,
  HttpAuthSchemeParameters,
  HttpAuthSchemeParametersProvider,
  HttpAuthSchemeProvider,
  Provider,
} from "@smithy/types";
import { AmplifyClientResolvedConfig } from "../AmplifyClient";
export interface AmplifyHttpAuthSchemeParameters
  extends HttpAuthSchemeParameters {
  region?: string;
}
export interface AmplifyHttpAuthSchemeParametersProvider
  extends HttpAuthSchemeParametersProvider<
    AmplifyClientResolvedConfig,
    HandlerExecutionContext,
    AmplifyHttpAuthSchemeParameters,
    object
  > {}
export declare const defaultAmplifyHttpAuthSchemeParametersProvider: (
  config: AmplifyClientResolvedConfig,
  context: HandlerExecutionContext,
  input: object
) => Promise<AmplifyHttpAuthSchemeParameters>;
export interface AmplifyHttpAuthSchemeProvider
  extends HttpAuthSchemeProvider<AmplifyHttpAuthSchemeParameters> {}
export declare const defaultAmplifyHttpAuthSchemeProvider: AmplifyHttpAuthSchemeProvider;
export interface HttpAuthSchemeInputConfig extends AwsSdkSigV4AuthInputConfig {
  authSchemePreference?: string[] | Provider<string[]>;
  httpAuthSchemes?: HttpAuthScheme[];
  httpAuthSchemeProvider?: AmplifyHttpAuthSchemeProvider;
}
export interface HttpAuthSchemeResolvedConfig
  extends AwsSdkSigV4AuthResolvedConfig {
  readonly authSchemePreference: Provider<string[]>;
  readonly httpAuthSchemes: HttpAuthScheme[];
  readonly httpAuthSchemeProvider: AmplifyHttpAuthSchemeProvider;
}
export declare const resolveHttpAuthSchemeConfig: <T>(
  config: T & HttpAuthSchemeInputConfig & AwsSdkSigV4PreviouslyResolved
) => T & HttpAuthSchemeResolvedConfig;
