/**
 * <p>Amplify enables developers to develop and deploy cloud-powered mobile and web apps.
 *             Amplify Hosting provides a continuous delivery and hosting service for web applications.
 *             For more information, see the <a href="https://docs.aws.amazon.com/amplify/latest/userguide/welcome.html">Amplify Hosting User Guide</a>. The
 *             Amplify Framework is a comprehensive set of SDKs, libraries, tools, and documentation
 *             for client app development. For more information, see the <a href="https://docs.amplify.aws/">Amplify Framework.</a>
 *          </p>
 *
 * @packageDocumentation
 */
export * from "./AmplifyClient";
export * from "./Amplify";
export { ClientInputEndpointParameters } from "./endpoint/EndpointParameters";
export type { RuntimeExtension } from "./runtimeExtensions";
export type { AmplifyExtensionConfiguration } from "./extensionConfiguration";
export * from "./commands";
export * from "./pagination";
export * from "./models";
export { AmplifyServiceException } from "./models/AmplifyServiceException";
