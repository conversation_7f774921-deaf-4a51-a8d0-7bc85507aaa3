import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  ElasticLoadBalancingV2ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../ElasticLoadBalancingV2Client";
import {
  CreateTrustStoreInput,
  CreateTrustStoreOutput,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface CreateTrustStoreCommandInput extends CreateTrustStoreInput {}
export interface CreateTrustStoreCommandOutput
  extends CreateTrustStoreOutput,
    __MetadataBearer {}
declare const CreateTrustStoreCommand_base: {
  new (
    input: CreateTrustStoreCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateTrustStoreCommandInput,
    CreateTrustStoreCommandOutput,
    ElasticLoadBalancingV2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateTrustStoreCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateTrustStoreCommandInput,
    CreateTrustStoreCommandOutput,
    ElasticLoadBalancingV2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateTrustStoreCommand extends CreateTrustStoreCommand_base {
  protected static __types: {
    api: {
      input: CreateTrustStoreInput;
      output: CreateTrustStoreOutput;
    };
    sdk: {
      input: CreateTrustStoreCommandInput;
      output: CreateTrustStoreCommandOutput;
    };
  };
}
