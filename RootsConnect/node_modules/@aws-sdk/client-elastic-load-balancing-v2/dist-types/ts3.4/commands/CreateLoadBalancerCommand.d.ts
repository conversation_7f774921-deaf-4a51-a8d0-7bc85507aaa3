import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  ElasticLoadBalancingV2ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../ElasticLoadBalancingV2Client";
import {
  CreateLoadBalancerInput,
  CreateLoadBalancerOutput,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface CreateLoadBalancerCommandInput
  extends CreateLoadBalancerInput {}
export interface CreateLoadBalancerCommandOutput
  extends CreateLoadBalancerOutput,
    __MetadataBearer {}
declare const CreateLoadBalancerCommand_base: {
  new (
    input: CreateLoadBalancerCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateLoadBalancerCommandInput,
    CreateLoadBalancerCommandOutput,
    ElasticLoadBalancingV2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateLoadBalancerCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateLoadBalancerCommandInput,
    CreateLoadBalancerCommandOutput,
    ElasticLoadBalancingV2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateLoadBalancerCommand extends CreateLoadBalancerCommand_base {
  protected static __types: {
    api: {
      input: CreateLoadBalancerInput;
      output: CreateLoadBalancerOutput;
    };
    sdk: {
      input: CreateLoadBalancerCommandInput;
      output: CreateLoadBalancerCommandOutput;
    };
  };
}
