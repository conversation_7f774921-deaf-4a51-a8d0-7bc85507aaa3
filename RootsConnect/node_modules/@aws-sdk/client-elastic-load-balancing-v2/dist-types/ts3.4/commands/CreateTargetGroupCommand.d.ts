import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  ElasticLoadBalancingV2ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../ElasticLoadBalancingV2Client";
import {
  CreateTargetGroupInput,
  CreateTargetGroupOutput,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface CreateTargetGroupCommandInput extends CreateTargetGroupInput {}
export interface CreateTargetGroupCommandOutput
  extends CreateTargetGroupOutput,
    __MetadataBearer {}
declare const CreateTargetGroupCommand_base: {
  new (
    input: CreateTargetGroupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateTargetGroupCommandInput,
    CreateTargetGroupCommandOutput,
    ElasticLoadBalancingV2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateTargetGroupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateTargetGroupCommandInput,
    CreateTargetGroupCommandOutput,
    ElasticLoadBalancingV2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateTargetGroupCommand extends CreateTargetGroupCommand_base {
  protected static __types: {
    api: {
      input: CreateTargetGroupInput;
      output: CreateTargetGroupOutput;
    };
    sdk: {
      input: CreateTargetGroupCommandInput;
      output: CreateTargetGroupCommandOutput;
    };
  };
}
