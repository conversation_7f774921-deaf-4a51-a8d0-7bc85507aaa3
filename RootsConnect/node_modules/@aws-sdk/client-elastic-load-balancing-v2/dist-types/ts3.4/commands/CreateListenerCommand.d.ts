import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  ElasticLoadBalancingV2ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../ElasticLoadBalancingV2Client";
import { CreateListenerInput, CreateListenerOutput } from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface CreateListenerCommandInput extends CreateListenerInput {}
export interface CreateListenerCommandOutput
  extends CreateListenerOutput,
    __MetadataBearer {}
declare const CreateListenerCommand_base: {
  new (
    input: CreateListenerCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateListenerCommandInput,
    CreateListenerCommandOutput,
    ElasticLoadBalancingV2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: CreateListenerCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateListenerCommandInput,
    CreateListenerCommandOutput,
    ElasticLoadBalancingV2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateListenerCommand extends CreateListenerCommand_base {
  protected static __types: {
    api: {
      input: CreateListenerInput;
      output: CreateListenerOutput;
    };
    sdk: {
      input: CreateListenerCommandInput;
      output: CreateListenerCommandOutput;
    };
  };
}
