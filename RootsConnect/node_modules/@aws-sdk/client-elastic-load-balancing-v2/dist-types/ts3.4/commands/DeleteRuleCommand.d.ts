import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  ElasticLoadBalancingV2ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../ElasticLoadBalancingV2Client";
import { DeleteRuleInput, DeleteRuleOutput } from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface DeleteRuleCommandInput extends DeleteRuleInput {}
export interface DeleteRuleCommandOutput
  extends DeleteRuleOutput,
    __MetadataBearer {}
declare const DeleteRuleCommand_base: {
  new (
    input: DeleteRuleCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteRuleCommandInput,
    DeleteRuleCommandOutput,
    ElasticLoadBalancingV2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteRuleCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteRuleCommandInput,
    DeleteRuleCommandOutput,
    ElasticLoadBalancingV2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteRuleCommand extends DeleteRuleCommand_base {
  protected static __types: {
    api: {
      input: DeleteRuleInput;
      output: {};
    };
    sdk: {
      input: DeleteRuleCommandInput;
      output: DeleteRuleCommandOutput;
    };
  };
}
