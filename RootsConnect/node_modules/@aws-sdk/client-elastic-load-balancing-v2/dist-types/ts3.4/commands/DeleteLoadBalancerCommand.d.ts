import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  ElasticLoadBalancingV2ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../ElasticLoadBalancingV2Client";
import {
  DeleteLoadBalancerInput,
  DeleteLoadBalancerOutput,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface DeleteLoadBalancerCommandInput
  extends DeleteLoadBalancerInput {}
export interface DeleteLoadBalancerCommandOutput
  extends DeleteLoadBalancerOutput,
    __MetadataBearer {}
declare const DeleteLoadBalancerCommand_base: {
  new (
    input: DeleteLoadBalancerCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteLoadBalancerCommandInput,
    DeleteLoadBalancerCommandOutput,
    ElasticLoadBalancingV2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DeleteLoadBalancerCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteLoadBalancerCommandInput,
    DeleteLoadBalancerCommandOutput,
    ElasticLoadBalancingV2ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteLoadBalancerCommand extends DeleteLoadBalancerCommand_base {
  protected static __types: {
    api: {
      input: DeleteLoadBalancerInput;
      output: {};
    };
    sdk: {
      input: DeleteLoadBalancerCommandInput;
      output: DeleteLoadBalancerCommandOutput;
    };
  };
}
