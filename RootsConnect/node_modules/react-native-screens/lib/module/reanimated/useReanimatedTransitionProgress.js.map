{"version": 3, "names": ["React", "ReanimatedTransitionProgressContext", "useReanimatedTransitionProgress", "progress", "useContext", "undefined", "Error"], "sourceRoot": "../../../src", "sources": ["reanimated/useReanimatedTransitionProgress.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,mCAAmC,MAAM,uCAAuC;AAEvF,eAAe,SAASC,+BAA+BA,CAAA,EAAG;EACxD,MAAMC,QAAQ,GAAGH,KAAK,CAACI,UAAU,CAACH,mCAAmC,CAAC;EAEtE,IAAIE,QAAQ,KAAKE,SAAS,EAAE;IAC1B,MAAM,IAAIC,KAAK,CACb,mGACF,CAAC;EACH;EAEA,OAAOH,QAAQ;AACjB", "ignoreList": []}