{"version": 3, "names": ["Object", "defineProperty", "exports", "value", "GHContext", "_react", "_interopRequireDefault", "require", "e", "__esModule", "default", "React", "createContext", "props", "createElement", "Fragment", "children"], "sourceRoot": "../../../../src", "sources": ["native-stack/contexts/GHContext.tsx"], "mappings": ";AAAA,YAAY;;AAACA,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,KAAA;AAAA;AAAAD,OAAA,CAAAE,SAAA;AAEb,IAAAC,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAiD,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAGjD;AACO,MAAMJ,SAAS,GAAAF,OAAA,CAAAE,SAAA,gBAAGO,cAAK,CAACC,aAAa,CACzCC,KAA8C,iBAAKR,MAAA,CAAAK,OAAA,CAAAI,aAAA,CAAAT,MAAA,CAAAK,OAAA,CAAAK,QAAA,QAAGF,KAAK,CAACG,QAAW,CAC1E,CAAC", "ignoreList": []}