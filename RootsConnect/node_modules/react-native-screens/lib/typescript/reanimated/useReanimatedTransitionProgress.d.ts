export default function useReanimatedTransitionProgress(): {
    progress: import("react-native-reanimated/lib/typescript/Animated").SharedValue<number>;
    closing: import("react-native-reanimated/lib/typescript/Animated").SharedValue<number>;
    goingForward: import("react-native-reanimated/lib/typescript/Animated").SharedValue<number>;
};
//# sourceMappingURL=useReanimatedTransitionProgress.d.ts.map