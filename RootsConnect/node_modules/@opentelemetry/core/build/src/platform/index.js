"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getStringListFromEnv = exports.getNumberFromEnv = exports.getStringFromEnv = exports.getBooleanFromEnv = exports.unrefTimer = exports.otperformance = exports._globalThis = exports.SDK_INFO = void 0;
/*
 * Copyright The OpenTelemetry Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var node_1 = require("./node");
Object.defineProperty(exports, "SDK_INFO", { enumerable: true, get: function () { return node_1.SDK_INFO; } });
Object.defineProperty(exports, "_globalThis", { enumerable: true, get: function () { return node_1._globalThis; } });
Object.defineProperty(exports, "otperformance", { enumerable: true, get: function () { return node_1.otperformance; } });
Object.defineProperty(exports, "unrefTimer", { enumerable: true, get: function () { return node_1.unrefTimer; } });
Object.defineProperty(exports, "getBooleanFromEnv", { enumerable: true, get: function () { return node_1.getBooleanFromEnv; } });
Object.defineProperty(exports, "getStringFromEnv", { enumerable: true, get: function () { return node_1.getStringFromEnv; } });
Object.defineProperty(exports, "getNumberFromEnv", { enumerable: true, get: function () { return node_1.getNumberFromEnv; } });
Object.defineProperty(exports, "getStringListFromEnv", { enumerable: true, get: function () { return node_1.getStringListFromEnv; } });
//# sourceMappingURL=index.js.map