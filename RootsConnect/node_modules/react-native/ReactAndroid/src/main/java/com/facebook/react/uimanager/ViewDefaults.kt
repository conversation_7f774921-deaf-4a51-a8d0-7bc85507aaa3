/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.facebook.react.uimanager

/** Default property values for Views to be shared between Views and ShadowViews. */
public object ViewDefaults {
  public const val FONT_SIZE_SP: Float = 14.0f
  public const val LINE_HEIGHT: Int = 0
  public const val NUMBER_OF_LINES: Int = Int.MAX_VALUE
}
