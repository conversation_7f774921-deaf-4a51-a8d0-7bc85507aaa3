/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @generated SignedSource<<5c034c449098cae39fc203c0a33c011e>>
 *
 * This file was translated from Flow by scripts/build-types/index.js.
 * Original file: packages/react-native/Libraries/BatchedBridge/BatchedBridge.js
 */

import MessageQueue from "./MessageQueue";
declare const BatchedBridge: MessageQueue;
declare const $$BatchedBridge: typeof BatchedBridge;
declare type $$BatchedBridge = typeof $$BatchedBridge;
export default $$BatchedBridge;
