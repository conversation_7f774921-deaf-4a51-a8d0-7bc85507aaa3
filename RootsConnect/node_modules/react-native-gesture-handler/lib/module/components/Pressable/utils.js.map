{"version": 3, "names": ["numberAsInset", "value", "left", "right", "top", "bottom", "addInsets", "a", "b", "touchDataToPressEvent", "data", "timestamp", "targetId", "identifier", "id", "locationX", "x", "locationY", "y", "pageX", "absoluteX", "pageY", "absoluteY", "target", "touches", "changedTouches", "gestureToPressEvent", "event", "handlerTag", "isTouchWithinInset", "dimensions", "inset", "touch", "width", "height", "gestureToPressableEvent", "Date", "now", "pressEvent", "nativeEvent", "force", "undefined", "gestureTouchToPressableEvent", "touchesList", "allTouches", "map", "changedTouchesList", "at"], "sourceRoot": "../../../../src", "sources": ["components/Pressable/utils.ts"], "mappings": ";;AAYA,MAAMA,aAAa,GAAIC,KAAa,KAAc;EAChDC,IAAI,EAAED,KAAK;EACXE,KAAK,EAAEF,KAAK;EACZG,GAAG,EAAEH,KAAK;EACVI,MAAM,EAAEJ;AACV,CAAC,CAAC;AAEF,MAAMK,SAAS,GAAGA,CAACC,CAAS,EAAEC,CAAS,MAAc;EACnDN,IAAI,EAAE,CAACK,CAAC,CAACL,IAAI,IAAI,CAAC,KAAKM,CAAC,CAACN,IAAI,IAAI,CAAC,CAAC;EACnCC,KAAK,EAAE,CAACI,CAAC,CAACJ,KAAK,IAAI,CAAC,KAAKK,CAAC,CAACL,KAAK,IAAI,CAAC,CAAC;EACtCC,GAAG,EAAE,CAACG,CAAC,CAACH,GAAG,IAAI,CAAC,KAAKI,CAAC,CAACJ,GAAG,IAAI,CAAC,CAAC;EAChCC,MAAM,EAAE,CAACE,CAAC,CAACF,MAAM,IAAI,CAAC,KAAKG,CAAC,CAACH,MAAM,IAAI,CAAC;AAC1C,CAAC,CAAC;AAEF,MAAMI,qBAAqB,GAAGA,CAC5BC,IAAe,EACfC,SAAiB,EACjBC,QAAgB,MACS;EACzBC,UAAU,EAAEH,IAAI,CAACI,EAAE;EACnBC,SAAS,EAAEL,IAAI,CAACM,CAAC;EACjBC,SAAS,EAAEP,IAAI,CAACQ,CAAC;EACjBC,KAAK,EAAET,IAAI,CAACU,SAAS;EACrBC,KAAK,EAAEX,IAAI,CAACY,SAAS;EACrBC,MAAM,EAAEX,QAAQ;EAChBD,SAAS,EAAEA,SAAS;EACpBa,OAAO,EAAE,EAAE;EAAE;EACbC,cAAc,EAAE,EAAE,CAAE;AACtB,CAAC,CAAC;AAEF,MAAMC,mBAAmB,GAAGA,CAC1BC,KAEC,EACDhB,SAAiB,EACjBC,QAAgB,MACS;EACzBC,UAAU,EAAEc,KAAK,CAACC,UAAU;EAC5Bb,SAAS,EAAEY,KAAK,CAACX,CAAC;EAClBC,SAAS,EAAEU,KAAK,CAACT,CAAC;EAClBC,KAAK,EAAEQ,KAAK,CAACP,SAAS;EACtBC,KAAK,EAAEM,KAAK,CAACL,SAAS;EACtBC,MAAM,EAAEX,QAAQ;EAChBD,SAAS,EAAEA,SAAS;EACpBa,OAAO,EAAE,EAAE;EAAE;EACbC,cAAc,EAAE,EAAE,CAAE;AACtB,CAAC,CAAC;AAEF,MAAMI,kBAAkB,GAAGA,CACzBC,UAA6C,EAC7CC,KAAa,EACbC,KAAiB,KAEjB,CAACA,KAAK,EAAEhB,CAAC,IAAI,CAAC,IAAI,CAACe,KAAK,CAAC5B,KAAK,IAAI,CAAC,IAAI2B,UAAU,CAACG,KAAK,IACvD,CAACD,KAAK,EAAEd,CAAC,IAAI,CAAC,IAAI,CAACa,KAAK,CAAC1B,MAAM,IAAI,CAAC,IAAIyB,UAAU,CAACI,MAAM,IACzD,CAACF,KAAK,EAAEhB,CAAC,IAAI,CAAC,IAAI,EAAEe,KAAK,CAAC7B,IAAI,IAAI,CAAC,CAAC,IACpC,CAAC8B,KAAK,EAAEd,CAAC,IAAI,CAAC,IAAI,EAAEa,KAAK,CAAC3B,GAAG,IAAI,CAAC,CAAC;AAErC,MAAM+B,uBAAuB,GAC3BR,KAEC,IACkB;EACnB,MAAMhB,SAAS,GAAGyB,IAAI,CAACC,GAAG,CAAC,CAAC;;EAE5B;EACA,MAAMzB,QAAQ,GAAG,CAAC;EAElB,MAAM0B,UAAU,GAAGZ,mBAAmB,CAACC,KAAK,EAAEhB,SAAS,EAAEC,QAAQ,CAAC;EAElE,OAAO;IACL2B,WAAW,EAAE;MACXf,OAAO,EAAE,CAACc,UAAU,CAAC;MACrBb,cAAc,EAAE,CAACa,UAAU,CAAC;MAC5BzB,UAAU,EAAEyB,UAAU,CAACzB,UAAU;MACjCE,SAAS,EAAEY,KAAK,CAACX,CAAC;MAClBC,SAAS,EAAEU,KAAK,CAACT,CAAC;MAClBC,KAAK,EAAEQ,KAAK,CAACP,SAAS;MACtBC,KAAK,EAAEM,KAAK,CAACL,SAAS;MACtBC,MAAM,EAAEX,QAAQ;MAChBD,SAAS,EAAEA,SAAS;MACpB6B,KAAK,EAAEC;IACT;EACF,CAAC;AACH,CAAC;AAED,MAAMC,4BAA4B,GAChCf,KAAwB,IACL;EACnB,MAAMhB,SAAS,GAAGyB,IAAI,CAACC,GAAG,CAAC,CAAC;;EAE5B;EACA,MAAMzB,QAAQ,GAAG,CAAC;EAElB,MAAM+B,WAAW,GAAGhB,KAAK,CAACiB,UAAU,CAACC,GAAG,CAAEb,KAAgB,IACxDvB,qBAAqB,CAACuB,KAAK,EAAErB,SAAS,EAAEC,QAAQ,CAClD,CAAC;EACD,MAAMkC,kBAAkB,GAAGnB,KAAK,CAACF,cAAc,CAACoB,GAAG,CAAEb,KAAgB,IACnEvB,qBAAqB,CAACuB,KAAK,EAAErB,SAAS,EAAEC,QAAQ,CAClD,CAAC;EAED,OAAO;IACL2B,WAAW,EAAE;MACXf,OAAO,EAAEmB,WAAW;MACpBlB,cAAc,EAAEqB,kBAAkB;MAClCjC,UAAU,EAAEc,KAAK,CAACC,UAAU;MAC5Bb,SAAS,EAAEY,KAAK,CAACiB,UAAU,CAACG,EAAE,CAAC,CAAC,CAAC,EAAE/B,CAAC,IAAI,CAAC,CAAC;MAC1CC,SAAS,EAAEU,KAAK,CAACiB,UAAU,CAACG,EAAE,CAAC,CAAC,CAAC,EAAE7B,CAAC,IAAI,CAAC,CAAC;MAC1CC,KAAK,EAAEQ,KAAK,CAACiB,UAAU,CAACG,EAAE,CAAC,CAAC,CAAC,EAAE3B,SAAS,IAAI,CAAC,CAAC;MAC9CC,KAAK,EAAEM,KAAK,CAACiB,UAAU,CAACG,EAAE,CAAC,CAAC,CAAC,EAAEzB,SAAS,IAAI,CAAC,CAAC;MAC9CC,MAAM,EAAEX,QAAQ;MAChBD,SAAS,EAAEA,SAAS;MACpB6B,KAAK,EAAEC;IACT;EACF,CAAC;AACH,CAAC;AAED,SACEzC,aAAa,EACbM,SAAS,EACTuB,kBAAkB,EAClBM,uBAAuB,EACvBO,4BAA4B", "ignoreList": []}