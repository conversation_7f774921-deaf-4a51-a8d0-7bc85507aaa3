{"version": 3, "names": ["_interfaces", "require", "_EventManager", "_interopRequireDefault", "_PointerType", "e", "__esModule", "default", "KeyboardEventManager", "EventManager", "activationKeys", "cancelationKeys", "isPressed", "keyDownCallback", "event", "indexOf", "key", "dispatchEvent", "EventTypes", "CANCEL", "DOWN", "keyUp<PERSON><PERSON><PERSON>", "UP", "eventType", "target", "HTMLElement", "adaptedEvent", "mapEvent", "onPointerUp", "onPointerDown", "onPointerCancel", "registerListeners", "view", "addEventListener", "unregisterListeners", "removeEventListener", "viewRect", "getBoundingClientRect", "viewportPosition", "x", "width", "y", "height", "relativePosition", "offsetX", "offsetY", "pointerId", "pointerType", "PointerType", "KEY", "time", "timeStamp", "exports"], "sourceRoot": "../../../../src", "sources": ["web/tools/KeyboardEventManager.ts"], "mappings": ";;;;;;AAAA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,aAAA,GAAAC,sBAAA,CAAAF,OAAA;AACA,IAAAG,YAAA,GAAAH,OAAA;AAAgD,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEjC,MAAMG,oBAAoB,SAASC,qBAAY,CAAc;EAClEC,cAAc,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC;EAC/BC,eAAe,GAAG,CAAC,KAAK,CAAC;EACzBC,SAAS,GAAG,KAAK;EAEjBC,eAAe,GAAIC,KAAoB,IAAW;IACxD,IAAI,IAAI,CAACH,eAAe,CAACI,OAAO,CAACD,KAAK,CAACE,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,IAAI,CAACJ,SAAS,EAAE;MACpE,IAAI,CAACK,aAAa,CAACH,KAAK,EAAEI,sBAAU,CAACC,MAAM,CAAC;MAC5C;IACF;IAEA,IAAI,IAAI,CAACT,cAAc,CAACK,OAAO,CAACD,KAAK,CAACE,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;MACjD;IACF;IAEA,IAAI,CAACC,aAAa,CAACH,KAAK,EAAEI,sBAAU,CAACE,IAAI,CAAC;EAC5C,CAAC;EAEOC,aAAa,GAAIP,KAAoB,IAAW;IACtD,IAAI,IAAI,CAACJ,cAAc,CAACK,OAAO,CAACD,KAAK,CAACE,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAACJ,SAAS,EAAE;MACpE;IACF;IAEA,IAAI,CAACK,aAAa,CAACH,KAAK,EAAEI,sBAAU,CAACI,EAAE,CAAC;EAC1C,CAAC;EAEOL,aAAaA,CAACH,KAAoB,EAAES,SAAqB,EAAE;IACjE,IAAI,EAAET,KAAK,CAACU,MAAM,YAAYC,WAAW,CAAC,EAAE;MAC1C;IACF;IAEA,MAAMC,YAAY,GAAG,IAAI,CAACC,QAAQ,CAACb,KAAK,EAAES,SAAS,CAAC;IAEpD,QAAQA,SAAS;MACf,KAAKL,sBAAU,CAACI,EAAE;QAChB,IAAI,CAACV,SAAS,GAAG,KAAK;QACtB,IAAI,CAACgB,WAAW,CAACF,YAAY,CAAC;QAC9B;MACF,KAAKR,sBAAU,CAACE,IAAI;QAClB,IAAI,CAACR,SAAS,GAAG,IAAI;QACrB,IAAI,CAACiB,aAAa,CAACH,YAAY,CAAC;QAChC;MACF,KAAKR,sBAAU,CAACC,MAAM;QACpB,IAAI,CAACP,SAAS,GAAG,KAAK;QACtB,IAAI,CAACkB,eAAe,CAACJ,YAAY,CAAC;QAClC;IACJ;EACF;EAEOK,iBAAiBA,CAAA,EAAS;IAC/B,IAAI,CAACC,IAAI,CAACC,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACpB,eAAe,CAAC;IAC3D,IAAI,CAACmB,IAAI,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACZ,aAAa,CAAC;EACzD;EAEOa,mBAAmBA,CAAA,EAAS;IACjC,IAAI,CAACF,IAAI,CAACG,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACtB,eAAe,CAAC;IAC9D,IAAI,CAACmB,IAAI,CAACG,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACd,aAAa,CAAC;EAC5D;EAEUM,QAAQA,CAChBb,KAAoB,EACpBS,SAAqB,EACP;IACd,MAAMa,QAAQ,GAAItB,KAAK,CAACU,MAAM,CAAiBa,qBAAqB,CAAC,CAAC;IAEtE,MAAMC,gBAAgB,GAAG;MACvBC,CAAC,EAAEH,QAAQ,EAAEG,CAAC,GAAGH,QAAQ,EAAEI,KAAK,GAAG,CAAC;MACpCC,CAAC,EAAEL,QAAQ,EAAEK,CAAC,GAAGL,QAAQ,EAAEM,MAAM,GAAG;IACtC,CAAC;IAED,MAAMC,gBAAgB,GAAG;MACvBJ,CAAC,EAAEH,QAAQ,EAAEI,KAAK,GAAG,CAAC;MACtBC,CAAC,EAAEL,QAAQ,EAAEM,MAAM,GAAG;IACxB,CAAC;IAED,OAAO;MACLH,CAAC,EAAED,gBAAgB,CAACC,CAAC;MACrBE,CAAC,EAAEH,gBAAgB,CAACG,CAAC;MACrBG,OAAO,EAAED,gBAAgB,CAACJ,CAAC;MAC3BM,OAAO,EAAEF,gBAAgB,CAACF,CAAC;MAC3BK,SAAS,EAAE,CAAC;MACZvB,SAAS,EAAEA,SAAS;MACpBwB,WAAW,EAAEC,wBAAW,CAACC,GAAG;MAC5BC,IAAI,EAAEpC,KAAK,CAACqC;IACd,CAAC;EACH;AACF;AAACC,OAAA,CAAA7C,OAAA,GAAAC,oBAAA", "ignoreList": []}