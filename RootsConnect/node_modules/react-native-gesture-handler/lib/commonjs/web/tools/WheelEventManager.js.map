{"version": 3, "names": ["_EventManager", "_interopRequireDefault", "require", "_interfaces", "_PointerType", "e", "__esModule", "default", "WheelEventManager", "EventManager", "wheelDelta", "x", "y", "reset<PERSON><PERSON><PERSON>", "_event", "wheelCallback", "event", "deltaX", "deltaY", "adaptedEvent", "mapEvent", "onWheel", "registerListeners", "view", "addEventListener", "unregisterListeners", "removeEventListener", "clientX", "clientY", "offsetX", "offsetY", "pointerId", "eventType", "EventTypes", "MOVE", "pointerType", "PointerType", "OTHER", "time", "timeStamp", "wheelDeltaY", "resetManager", "exports"], "sourceRoot": "../../../../src", "sources": ["web/tools/WheelEventManager.ts"], "mappings": ";;;;;;AAAA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AACA,IAAAE,YAAA,GAAAF,OAAA;AAAgD,SAAAD,uBAAAI,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEjC,MAAMG,iBAAiB,SAASC,qBAAY,CAAc;EAC/DC,UAAU,GAAG;IAAEC,CAAC,EAAE,CAAC;IAAEC,CAAC,EAAE;EAAE,CAAC;EAE3BC,UAAU,GAAIC,MAAoB,IAAK;IAC7C,IAAI,CAACJ,UAAU,GAAG;MAAEC,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE,CAAC;EAClC,CAAC;EAEOG,aAAa,GAAIC,KAAiB,IAAK;IAC7C,IAAI,CAACN,UAAU,CAACC,CAAC,IAAIK,KAAK,CAACC,MAAM;IACjC,IAAI,CAACP,UAAU,CAACE,CAAC,IAAII,KAAK,CAACE,MAAM;IAEjC,MAAMC,YAAY,GAAG,IAAI,CAACC,QAAQ,CAACJ,KAAK,CAAC;IACzC,IAAI,CAACK,OAAO,CAACF,YAAY,CAAC;EAC5B,CAAC;EAEMG,iBAAiBA,CAAA,EAAS;IAC/B,IAAI,CAACC,IAAI,CAACC,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACX,UAAU,CAAC;IAC1D,IAAI,CAACU,IAAI,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACT,aAAa,CAAC;EACzD;EAEOU,mBAAmBA,CAAA,EAAS;IACjC,IAAI,CAACF,IAAI,CAACG,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAACb,UAAU,CAAC;IAC7D,IAAI,CAACU,IAAI,CAACG,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACX,aAAa,CAAC;EAC5D;EAEUK,QAAQA,CAACJ,KAAiB,EAAgB;IAClD,OAAO;MACLL,CAAC,EAAEK,KAAK,CAACW,OAAO,GAAG,IAAI,CAACjB,UAAU,CAACC,CAAC;MACpCC,CAAC,EAAEI,KAAK,CAACY,OAAO,GAAG,IAAI,CAAClB,UAAU,CAACE,CAAC;MACpCiB,OAAO,EAAEb,KAAK,CAACa,OAAO,GAAGb,KAAK,CAACC,MAAM;MACrCa,OAAO,EAAEd,KAAK,CAACc,OAAO,GAAGd,KAAK,CAACE,MAAM;MACrCa,SAAS,EAAE,CAAC,CAAC;MACbC,SAAS,EAAEC,sBAAU,CAACC,IAAI;MAC1BC,WAAW,EAAEC,wBAAW,CAACC,KAAK;MAC9BC,IAAI,EAAEtB,KAAK,CAACuB,SAAS;MACrB;MACAC,WAAW,EAAExB,KAAK,CAACwB;IACrB,CAAC;EACH;EAEOC,YAAYA,CAAA,EAAS;IAC1B,KAAK,CAACA,YAAY,CAAC,CAAC;EACtB;AACF;AAACC,OAAA,CAAAnC,OAAA,GAAAC,iBAAA", "ignoreList": []}