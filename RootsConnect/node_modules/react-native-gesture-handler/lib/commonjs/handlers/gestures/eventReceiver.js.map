{"version": 3, "names": ["_reactNative", "require", "_State", "_TouchEventType", "_handlersRegistry", "_gestureStateManager", "gestureHandlerEventSubscription", "gestureHandlerStateChangeEventSubscription", "gestureStateManagers", "Map", "lastUpdateEvent", "isStateChangeEvent", "event", "oldState", "isTouchEvent", "eventType", "onGestureHandlerEvent", "handler", "<PERSON><PERSON><PERSON><PERSON>", "handlerTag", "State", "UNDETERMINED", "state", "BEGAN", "handlers", "onBegin", "ACTIVE", "onStart", "END", "onEnd", "onFinalize", "undefined", "FAILED", "CANCELLED", "delete", "has", "set", "GestureStateManager", "create", "manager", "get", "TouchEventType", "TOUCHES_DOWN", "onTouchesDown", "TOUCHES_MOVE", "onTouchesMove", "TOUCHES_UP", "onTouchesUp", "TOUCHES_CANCELLED", "onTouchesCancelled", "onUpdate", "onChange", "changeEventCalculator", "<PERSON><PERSON><PERSON><PERSON>", "findOldGestureHandler", "nativeEvent", "onGestureStateChange", "onGestureEvent", "startListening", "stopListening", "DeviceEventEmitter", "addListener", "remove"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/eventReceiver.ts"], "mappings": ";;;;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,eAAA,GAAAF,OAAA;AAMA,IAAAG,iBAAA,GAAAH,OAAA;AAEA,IAAAI,oBAAA,GAAAJ,OAAA;AAKA,IAAIK,+BAA2D,GAAG,IAAI;AACtE,IAAIC,0CAAsE,GACxE,IAAI;AAEN,MAAMC,oBAA0D,GAAG,IAAIC,GAAG,CAGxE,CAAC;AAEH,MAAMC,eAAmD,GAAG,EAAE;AAE9D,SAASC,kBAAkBA,CACzBC,KAAuE,EACrC;EAClC;EACA,OAAOA,KAAK,CAACC,QAAQ,IAAI,IAAI;AAC/B;AAEA,SAASC,YAAYA,CACnBF,KAAuE,EAC3C;EAC5B,OAAOA,KAAK,CAACG,SAAS,IAAI,IAAI;AAChC;AAEO,SAASC,qBAAqBA,CACnCJ,KAAuE,EACvE;EACA,MAAMK,OAAO,GAAG,IAAAC,6BAAW,EAACN,KAAK,CAACO,UAAU,CAE3C;EAED,IAAIF,OAAO,EAAE;IACX,IAAIN,kBAAkB,CAACC,KAAK,CAAC,EAAE;MAC7B,IACEA,KAAK,CAACC,QAAQ,KAAKO,YAAK,CAACC,YAAY,IACrCT,KAAK,CAACU,KAAK,KAAKF,YAAK,CAACG,KAAK,EAC3B;QACAN,OAAO,CAACO,QAAQ,CAACC,OAAO,GAAGb,KAAK,CAAC;MACnC,CAAC,MAAM,IACL,CAACA,KAAK,CAACC,QAAQ,KAAKO,YAAK,CAACG,KAAK,IAC7BX,KAAK,CAACC,QAAQ,KAAKO,YAAK,CAACC,YAAY,KACvCT,KAAK,CAACU,KAAK,KAAKF,YAAK,CAACM,MAAM,EAC5B;QACAT,OAAO,CAACO,QAAQ,CAACG,OAAO,GAAGf,KAAK,CAAC;QACjCF,eAAe,CAACO,OAAO,CAACO,QAAQ,CAACL,UAAU,CAAC,GAAGP,KAAK;MACtD,CAAC,MAAM,IAAIA,KAAK,CAACC,QAAQ,KAAKD,KAAK,CAACU,KAAK,IAAIV,KAAK,CAACU,KAAK,KAAKF,YAAK,CAACQ,GAAG,EAAE;QACtE,IAAIhB,KAAK,CAACC,QAAQ,KAAKO,YAAK,CAACM,MAAM,EAAE;UACnCT,OAAO,CAACO,QAAQ,CAACK,KAAK,GAAGjB,KAAK,EAAE,IAAI,CAAC;QACvC;QACAK,OAAO,CAACO,QAAQ,CAACM,UAAU,GAAGlB,KAAK,EAAE,IAAI,CAAC;QAC1CF,eAAe,CAACO,OAAO,CAACO,QAAQ,CAACL,UAAU,CAAC,GAAGY,SAAS;MAC1D,CAAC,MAAM,IACL,CAACnB,KAAK,CAACU,KAAK,KAAKF,YAAK,CAACY,MAAM,IAAIpB,KAAK,CAACU,KAAK,KAAKF,YAAK,CAACa,SAAS,KAChErB,KAAK,CAACC,QAAQ,KAAKD,KAAK,CAACU,KAAK,EAC9B;QACA,IAAIV,KAAK,CAACC,QAAQ,KAAKO,YAAK,CAACM,MAAM,EAAE;UACnCT,OAAO,CAACO,QAAQ,CAACK,KAAK,GAAGjB,KAAK,EAAE,KAAK,CAAC;QACxC;QACAK,OAAO,CAACO,QAAQ,CAACM,UAAU,GAAGlB,KAAK,EAAE,KAAK,CAAC;QAC3CJ,oBAAoB,CAAC0B,MAAM,CAACtB,KAAK,CAACO,UAAU,CAAC;QAC7CT,eAAe,CAACO,OAAO,CAACO,QAAQ,CAACL,UAAU,CAAC,GAAGY,SAAS;MAC1D;IACF,CAAC,MAAM,IAAIjB,YAAY,CAACF,KAAK,CAAC,EAAE;MAC9B,IAAI,CAACJ,oBAAoB,CAAC2B,GAAG,CAACvB,KAAK,CAACO,UAAU,CAAC,EAAE;QAC/CX,oBAAoB,CAAC4B,GAAG,CACtBxB,KAAK,CAACO,UAAU,EAChBkB,wCAAmB,CAACC,MAAM,CAAC1B,KAAK,CAACO,UAAU,CAC7C,CAAC;MACH;;MAEA;MACA,MAAMoB,OAAO,GAAG/B,oBAAoB,CAACgC,GAAG,CAAC5B,KAAK,CAACO,UAAU,CAAE;MAE3D,QAAQP,KAAK,CAACG,SAAS;QACrB,KAAK0B,8BAAc,CAACC,YAAY;UAC9BzB,OAAO,CAACO,QAAQ,EAAEmB,aAAa,GAAG/B,KAAK,EAAE2B,OAAO,CAAC;UACjD;QACF,KAAKE,8BAAc,CAACG,YAAY;UAC9B3B,OAAO,CAACO,QAAQ,EAAEqB,aAAa,GAAGjC,KAAK,EAAE2B,OAAO,CAAC;UACjD;QACF,KAAKE,8BAAc,CAACK,UAAU;UAC5B7B,OAAO,CAACO,QAAQ,EAAEuB,WAAW,GAAGnC,KAAK,EAAE2B,OAAO,CAAC;UAC/C;QACF,KAAKE,8BAAc,CAACO,iBAAiB;UACnC/B,OAAO,CAACO,QAAQ,EAAEyB,kBAAkB,GAAGrC,KAAK,EAAE2B,OAAO,CAAC;UACtD;MACJ;IACF,CAAC,MAAM;MACLtB,OAAO,CAACO,QAAQ,CAAC0B,QAAQ,GAAGtC,KAAK,CAAC;MAElC,IAAIK,OAAO,CAACO,QAAQ,CAAC2B,QAAQ,IAAIlC,OAAO,CAACO,QAAQ,CAAC4B,qBAAqB,EAAE;QACvEnC,OAAO,CAACO,QAAQ,CAAC2B,QAAQ,GACvBlC,OAAO,CAACO,QAAQ,CAAC4B,qBAAqB,GACpCxC,KAAK,EACLF,eAAe,CAACO,OAAO,CAACO,QAAQ,CAACL,UAAU,CAC7C,CACF,CAAC;QAEDT,eAAe,CAACO,OAAO,CAACO,QAAQ,CAACL,UAAU,CAAC,GAAGP,KAAK;MACtD;IACF;EACF,CAAC,MAAM;IACL,MAAMyC,UAAU,GAAG,IAAAC,uCAAqB,EAAC1C,KAAK,CAACO,UAAU,CAAC;IAC1D,IAAIkC,UAAU,EAAE;MACd,MAAME,WAAW,GAAG;QAAEA,WAAW,EAAE3C;MAAM,CAAC;MAC1C,IAAID,kBAAkB,CAACC,KAAK,CAAC,EAAE;QAC7ByC,UAAU,CAACG,oBAAoB,CAACD,WAAW,CAAC;MAC9C,CAAC,MAAM;QACLF,UAAU,CAACI,cAAc,CAACF,WAAW,CAAC;MACxC;MACA;IACF;EACF;AACF;AAEO,SAASG,cAAcA,CAAA,EAAG;EAC/BC,aAAa,CAAC,CAAC;EAEfrD,+BAA+B,GAAGsD,+BAAkB,CAACC,WAAW,CAC9D,uBAAuB,EACvB7C,qBACF,CAAC;EAEDT,0CAA0C,GAAGqD,+BAAkB,CAACC,WAAW,CACzE,6BAA6B,EAC7B7C,qBACF,CAAC;AACH;AAEO,SAAS2C,aAAaA,CAAA,EAAG;EAC9B,IAAIrD,+BAA+B,EAAE;IACnCA,+BAA+B,CAACwD,MAAM,CAAC,CAAC;IACxCxD,+BAA+B,GAAG,IAAI;EACxC;EAEA,IAAIC,0CAA0C,EAAE;IAC9CA,0CAA0C,CAACuD,MAAM,CAAC,CAAC;IACnDvD,0CAA0C,GAAG,IAAI;EACnD;AACF", "ignoreList": []}