{"version": 3, "names": ["_gesture", "require", "TapGesture", "BaseGesture", "config", "constructor", "handler<PERSON>ame", "shouldCancelWhenOutside", "minPointers", "numberOfTaps", "count", "maxDistance", "maxDist", "maxDuration", "duration", "maxDurationMs", "max<PERSON><PERSON><PERSON>", "delay", "max<PERSON>elay<PERSON>", "maxDeltaX", "delta", "maxDeltaY", "exports"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/tapGesture.ts"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AAIO,MAAMC,UAAU,SAASC,oBAAW,CAAgC;EAClEC,MAAM,GAAyC,CAAC,CAAC;EAExDC,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,CAAC;IAEP,IAAI,CAACC,WAAW,GAAG,mBAAmB;IACtC,IAAI,CAACC,uBAAuB,CAAC,IAAI,CAAC;EACpC;;EAEA;AACF;AACA;AACA;AACA;EACEC,WAAWA,CAACA,WAAmB,EAAE;IAC/B,IAAI,CAACJ,MAAM,CAACI,WAAW,GAAGA,WAAW;IACrC,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEC,YAAYA,CAACC,KAAa,EAAE;IAC1B,IAAI,CAACN,MAAM,CAACK,YAAY,GAAGC,KAAK;IAChC,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEC,WAAWA,CAACC,OAAe,EAAE;IAC3B,IAAI,CAACR,MAAM,CAACQ,OAAO,GAAGA,OAAO;IAC7B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEC,WAAWA,CAACC,QAAgB,EAAE;IAC5B,IAAI,CAACV,MAAM,CAACW,aAAa,GAAGD,QAAQ;IACpC,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEE,QAAQA,CAACC,KAAa,EAAE;IACtB,IAAI,CAACb,MAAM,CAACc,UAAU,GAAGD,KAAK;IAC9B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEE,SAASA,CAACC,KAAa,EAAE;IACvB,IAAI,CAAChB,MAAM,CAACe,SAAS,GAAGC,KAAK;IAC7B,OAAO,IAAI;EACb;;EAEA;AACF;AACA;AACA;AACA;EACEC,SAASA,CAACD,KAAa,EAAE;IACvB,IAAI,CAAChB,MAAM,CAACiB,SAAS,GAAGD,KAAK;IAC7B,OAAO,IAAI;EACb;AACF;AAACE,OAAA,CAAApB,UAAA,GAAAA,UAAA", "ignoreList": []}