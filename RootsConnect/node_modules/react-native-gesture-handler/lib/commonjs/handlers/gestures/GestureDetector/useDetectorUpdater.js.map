{"version": 3, "names": ["_react", "require", "_attachHandlers", "_updateHandlers", "_needsToReattach", "_dropHandlers", "_utils", "_findNodeHandle", "_interopRequireDefault", "e", "__esModule", "default", "useDetectorUpdater", "state", "preparedGesture", "gestures<PERSON>oAtta<PERSON>", "gestureConfig", "webEventHandlersRef", "forceRender", "useForceRender", "updateAttachedGestures", "useCallback", "skipConfigUpdate", "viewTag", "findNodeHandle", "viewRef", "didUnderlyingViewChange", "previousViewTag", "needsToReattach", "validateDetectorChildren", "dropHandlers", "attachHandlers", "forceRebuildReanimatedEvent", "updateHandlers"], "sourceRoot": "../../../../../src", "sources": ["handlers/gestures/GestureDetector/useDetectorUpdater.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AASA,IAAAC,eAAA,GAAAD,OAAA;AACA,IAAAE,eAAA,GAAAF,OAAA;AACA,IAAAG,gBAAA,GAAAH,OAAA;AACA,IAAAI,aAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AACA,IAAAM,eAAA,GAAAC,sBAAA,CAAAP,OAAA;AAAqD,SAAAO,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAErD;AACA;AACA;AACO,SAASG,kBAAkBA,CAChCC,KAA2B,EAC3BC,eAAqC,EACrCC,gBAA+B,EAC/BC,aAA4C,EAC5CC,mBAAqD,EACrD;EACA,MAAMC,WAAW,GAAG,IAAAC,qBAAc,EAAC,CAAC;EACpC,MAAMC,sBAAsB,GAAG,IAAAC,kBAAW;EACxC;EACCC,gBAA0B,IAAK;IAC9B;IACA,MAAMC,OAAO,GAAG,IAAAC,uBAAc,EAACX,KAAK,CAACY,OAAO,CAAW;IACvD,MAAMC,uBAAuB,GAAGH,OAAO,KAAKV,KAAK,CAACc,eAAe;IAEjE,IACED,uBAAuB,IACvB,IAAAE,gCAAe,EAACd,eAAe,EAAEC,gBAAgB,CAAC,EAClD;MACA,IAAAc,+BAAwB,EAAChB,KAAK,CAACY,OAAO,CAAC;MACvC,IAAAK,0BAAY,EAAChB,eAAe,CAAC;MAC7B,IAAAiB,8BAAc,EAAC;QACbjB,eAAe;QACfE,aAAa;QACbD,gBAAgB;QAChBE,mBAAmB;QACnBM;MACF,CAAC,CAAC;MAEF,IAAIG,uBAAuB,EAAE;QAC3Bb,KAAK,CAACc,eAAe,GAAGJ,OAAO;QAC/BV,KAAK,CAACmB,2BAA2B,GAAG,IAAI;QACxCd,WAAW,CAAC,CAAC;MACf;IACF,CAAC,MAAM,IAAI,CAACI,gBAAgB,EAAE;MAC5B,IAAAW,8BAAc,EAACnB,eAAe,EAAEE,aAAa,EAAED,gBAAgB,CAAC;IAClE;EACF,CAAC,EACD,CACEG,WAAW,EACXF,aAAa,EACbD,gBAAgB,EAChBD,eAAe,EACfD,KAAK,EACLI,mBAAmB,CAEvB,CAAC;EAED,OAAOG,sBAAsB;AAC/B", "ignoreList": []}