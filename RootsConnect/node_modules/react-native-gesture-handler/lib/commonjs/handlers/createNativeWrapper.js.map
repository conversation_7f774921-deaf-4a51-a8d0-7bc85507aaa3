{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "React", "_NativeViewGestureHandler", "_jsxRuntime", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "NATIVE_WRAPPER_PROPS_FILTER", "nativeViewProps", "createNativeWrapper", "Component", "config", "ComponentWrapper", "forwardRef", "props", "ref", "gestureHandlerProps", "childProps", "keys", "reduce", "res", "key", "<PERSON><PERSON><PERSON><PERSON>", "includes", "enabled", "hitSlop", "testID", "_ref", "useRef", "_gestureHandlerRef", "useImperativeHandle", "node", "current", "handlerTag", "jsx", "NativeViewGestureHandler", "children", "displayName", "render", "name"], "sourceRoot": "../../../src", "sources": ["handlers/createNativeWrapper.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AAA+B,IAAAC,KAAA,GAAAH,MAAA;AAG/B,IAAAI,yBAAA,GAAAF,OAAA;AAIoC,IAAAG,WAAA,GAAAH,OAAA;AAAA,SAAAD,wBAAAK,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAP,uBAAA,YAAAA,CAAAK,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA,GACA,MAAMkB,2BAA2B,GAAG,CAClC,GAAGC,yCAAe,EAClB,uBAAuB,EACvB,6BAA6B,CACrB;AAEK,SAASC,mBAAmBA,CACzCC,SAAiC,EACjCC,MAA+C,GAAG,CAAC,CAAC,EACpD;EACA,MAAMC,gBAAgB,gBAAG3B,KAAK,CAAC4B,UAAU,CAGvC,CAACC,KAAK,EAAEC,GAAG,KAAK;IAChB;IACA,MAAM;MAAEC,mBAAmB;MAAEC;IAAW,CAAC,GAAGb,MAAM,CAACc,IAAI,CAACJ,KAAK,CAAC,CAACK,MAAM,CACnE,CAACC,GAAG,EAAEC,GAAG,KAAK;MACZ;MACA,MAAMC,WAA8B,GAAGf,2BAA2B;MAClE,IAAIe,WAAW,CAACC,QAAQ,CAACF,GAAG,CAAC,EAAE;QAC7B;QACAD,GAAG,CAACJ,mBAAmB,CAACK,GAAG,CAAC,GAAGP,KAAK,CAACO,GAAG,CAAC;MAC3C,CAAC,MAAM;QACL;QACAD,GAAG,CAACH,UAAU,CAACI,GAAG,CAAC,GAAGP,KAAK,CAACO,GAAG,CAAC;MAClC;MACA,OAAOD,GAAG;IACZ,CAAC,EACD;MACEJ,mBAAmB,EAAE;QAAE,GAAGL;MAAO,CAAC;MAAE;MACpCM,UAAU,EAAE;QACVO,OAAO,EAAEV,KAAK,CAACU,OAAO;QACtBC,OAAO,EAAEX,KAAK,CAACW,OAAO;QACtBC,MAAM,EAAEZ,KAAK,CAACY;MAChB;IACF,CACF,CAAC;IACD,MAAMC,IAAI,GAAG,IAAAC,aAAM,EAAyB,IAAI,CAAC;IACjD,MAAMC,kBAAkB,GAAG,IAAAD,aAAM,EAAyB,IAAI,CAAC;IAC/D,IAAAE,0BAAmB,EACjBf,GAAG;IACH;IACA,MAAM;MACJ,MAAMgB,IAAI,GAAGF,kBAAkB,CAACG,OAAO;MACvC;MACA,IAAIL,IAAI,CAACK,OAAO,IAAID,IAAI,EAAE;QACxB;QACAJ,IAAI,CAACK,OAAO,CAACC,UAAU,GAAGF,IAAI,CAACE,UAAU;QACzC,OAAON,IAAI,CAACK,OAAO;MACrB;MACA,OAAO,IAAI;IACb,CAAC,EACD,CAACL,IAAI,EAAEE,kBAAkB,CAC3B,CAAC;IACD,oBACE,IAAA1C,WAAA,CAAA+C,GAAA,EAAChD,yBAAA,CAAAiD,wBAAwB;MAAA,GACnBnB,mBAAmB;MACvB;MACAD,GAAG,EAAEc,kBAAmB;MAAAO,QAAA,eACxB,IAAAjD,WAAA,CAAA+C,GAAA,EAACxB,SAAS;QAAA,GAAKO,UAAU;QAAEF,GAAG,EAAEY;MAAK,CAAE;IAAC,CAChB,CAAC;EAE/B,CAAC,CAAC;;EAEF;EACAf,gBAAgB,CAACyB,WAAW,GAC1B3B,SAAS,EAAE2B,WAAW;EACtB;EACA3B,SAAS,EAAE4B,MAAM,EAAEC,IAAI,IACtB,OAAO7B,SAAS,KAAK,QAAQ,IAAIA,SAAU,IAC5C,kBAAkB;EAEpB,OAAOE,gBAAgB;AACzB", "ignoreList": []}