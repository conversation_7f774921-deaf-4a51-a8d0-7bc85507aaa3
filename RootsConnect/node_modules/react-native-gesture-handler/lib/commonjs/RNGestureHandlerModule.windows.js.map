{"version": 3, "names": ["_PanGestureHandler", "_interopRequireDefault", "require", "_TapGestureHandler", "_LongPressGestureHandler", "_PinchGestureHandler", "_RotationGestureHandler", "_FlingGestureHandler", "_NativeViewGestureHandler", "_ManualGestureHandler", "e", "__esModule", "default", "Gestures", "exports", "NativeViewGestureHandler", "PanGestureHandler", "TapGestureHandler", "LongPressGestureHandler", "PinchGestureHandler", "RotationGestureHandler", "FlingGestureHandler", "ManualGestureHandler", "_default", "handleSetJSResponder", "_tag", "_blockNativeResponder", "handleClearJSResponder", "createGestureHandler", "_handler<PERSON>ame", "_handlerTag", "_config", "attachGestureHandler", "_new<PERSON>iew", "_actionType", "_propsRef", "updateGestureHandler", "_newConfig", "getGestureHandlerNode", "dropGestureHandler", "flushOperations"], "sourceRoot": "../../src", "sources": ["RNGestureHandlerModule.windows.ts"], "mappings": ";;;;;;AAKA,IAAAA,kBAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,wBAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,oBAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,uBAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,oBAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,yBAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,qBAAA,GAAAR,sBAAA,CAAAC,OAAA;AAAuE,SAAAD,uBAAAS,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AARvE;;AAWO,MAAMG,QAAQ,GAAAC,OAAA,CAAAD,QAAA,GAAG;EACtBE,wBAAwB,EAAxBA,iCAAwB;EACxBC,iBAAiB,EAAjBA,0BAAiB;EACjBC,iBAAiB,EAAjBA,0BAAiB;EACjBC,uBAAuB,EAAvBA,gCAAuB;EACvBC,mBAAmB,EAAnBA,4BAAmB;EACnBC,sBAAsB,EAAtBA,+BAAsB;EACtBC,mBAAmB,EAAnBA,4BAAmB;EACnBC,oBAAoB,EAApBA;AACF,CAAC;AAAC,IAAAC,QAAA,GAAAT,OAAA,CAAAF,OAAA,GAEa;EACbY,oBAAoBA,CAACC,IAAY,EAAEC,qBAA8B,EAAE;IACjE;EAAA,CACD;EACDC,sBAAsBA,CAAA,EAAG;IACvB;EAAA,CACD;EACDC,oBAAoBA,CAClBC,YAAmC,EACnCC,WAAmB,EACnBC,OAAU,EACV;IACA;EAAA,CACD;EACDC,oBAAoBA,CAClBF,WAAmB;EACnB;EACAG,QAAa,EACbC,WAAuB,EACvBC,SAAmC,EACnC;IACA;EAAA,CACD;EACDC,oBAAoBA,CAACN,WAAmB,EAAEO,UAAkB,EAAE;IAC5D;EAAA,CACD;EACDC,qBAAqBA,CAACR,WAAmB,EAAE;IACzC;EAAA,CACD;EACDS,kBAAkBA,CAACT,WAAmB,EAAE;IACtC;EAAA,CACD;EACDU,eAAeA,CAAA,EAAG;IAChB;EAAA;AAEJ,CAAC", "ignoreList": []}