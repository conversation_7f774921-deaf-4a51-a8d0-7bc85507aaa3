{"version": 3, "names": ["_Gesture<PERSON><PERSON>ler", "_interopRequireDefault", "require", "e", "__esModule", "default", "IndiscreteGestureHandler", "Gesture<PERSON>andler", "shouldEnableGestureOnSetup", "updateGestureConfig", "minPointers", "maxPointers", "props", "isGestureEnabledForEvent", "_recognizer", "pointer<PERSON><PERSON><PERSON>", "failed", "validPointerCount", "success", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["web_hammer/IndiscreteGestureHandler.ts"], "mappings": ";;;;;;AAAA,IAAAA,eAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA8C,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE9C;AACA;AACA;AACA,MAAeG,wBAAwB,SAASC,uBAAc,CAAC;EAC7D,IAAIC,0BAA0BA,CAAA,EAAG;IAC/B,OAAO,KAAK;EACd;EAEAC,mBAAmBA,CAAC;IAAEC,WAAW,GAAG,CAAC;IAAEC,WAAW,GAAG,CAAC;IAAE,GAAGC;EAAM,CAAC,EAAE;IAClE,OAAO,KAAK,CAACH,mBAAmB,CAAC;MAC/BC,WAAW;MACXC,WAAW;MACX,GAAGC;IACL,CAAC,CAAC;EACJ;EAEAC,wBAAwBA,CACtB;IAAEH,WAAW;IAAEC;EAAiB,CAAC,EACjCG,WAAgB,EAChB;IAAEH,WAAW,EAAEI;EAAmB,CAAC,EACnC;IACA,IAAIA,aAAa,GAAGJ,WAAW,EAAE;MAC/B,OAAO;QAAEK,MAAM,EAAE;MAAK,CAAC;IACzB;IACA,MAAMC,iBAAiB,GAAGF,aAAa,IAAIL,WAAW;IACtD,OAAO;MACLQ,OAAO,EAAED;IACX,CAAC;EACH;AACF;AAAC,IAAAE,QAAA,GAAAC,OAAA,CAAAf,OAAA,GACcC,wBAAwB", "ignoreList": []}