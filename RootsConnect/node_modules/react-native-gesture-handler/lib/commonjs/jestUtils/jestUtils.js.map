{"version": 3, "names": ["_invariant", "_interopRequireDefault", "require", "_reactNative", "_FlingGestureHandler", "_ForceTouchGestureHandler", "_gesture", "_handlersRegistry", "_LongPressGestureHandler", "_NativeViewGestureHandler", "_PanGestureHandler", "_PinchGestureHandler", "_RotationGestureHandler", "_TapGestureHandler", "_State", "_utils", "e", "__esModule", "default", "fireEvent", "_element", "_name", "_data", "_e", "handlersDefaultEvents", "flingHandlerName", "x", "y", "absoluteX", "absoluteY", "numberOfPointers", "forceTouchHandlerName", "force", "longPressHandlerName", "duration", "nativeViewHandlerName", "pointerInside", "panHandlerName", "translationX", "translationY", "velocityX", "velocityY", "stylusData", "undefined", "pinchHandlerName", "focalX", "focalY", "scale", "velocity", "rotationHandlerName", "anchorX", "anchorY", "rotation", "tapHandlerName", "isGesture", "componentOrGesture", "BaseGesture", "wrapWithNativeEvent", "event", "nativeEvent", "fillOldStateChanges", "previousEvent", "currentEvent", "isFirstEvent", "oldState", "State", "UNDETERMINED", "isGestureStateEvent", "state", "validateStateTransitions", "stringify", "JSON", "errorMsgWithBothEvents", "description", "errorMsgWithCurrentEvent", "invariant", "hasProperty", "BEGAN", "fillMissingDefaultsFor", "handlerType", "handlerTag", "isDiscreteHandler", "fillMissingStatesTransitions", "events", "_events", "lastEvent", "length", "firstEvent", "shouldDuplicateFirstEvent", "hasState", "duplicated", "unshift", "shouldDuplicateLastEvent", "END", "FAILED", "CANCELLED", "push", "isWithoutState", "noEventsLeft", "trueFn", "fillEventsForCurrentState", "shouldConsumeEvent", "shouldTransitionToNextState", "peekCurrentEvent", "peekNextEvent", "consumeCurrentEvent", "shift", "nextEvent", "currentRequiredState", "REQUIRED_EVENTS", "currentStateIdx", "eventData", "shouldUseEvent", "transformedEvents", "ACTIVE", "hasAllStates", "iterations", "nextRequiredState", "getHandlerData", "gesture", "emitEvent", "eventName", "args", "DeviceEventEmitter", "emit", "handler<PERSON>ame", "enabled", "config", "gestureHandlerComponent", "props", "fireGestureHandler", "eventList", "_", "map", "withPrevAndCurrent", "lastSentEvent", "hasChangedState", "getByGestureTestId", "testID", "handler", "findHandlerByTestID", "Error"], "sourceRoot": "../../../src", "sources": ["jestUtils/jestUtils.ts"], "mappings": ";;;;;;;AAAA,IAAAA,UAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,oBAAA,GAAAF,OAAA;AAIA,IAAAG,yBAAA,GAAAH,OAAA;AAWA,IAAAI,QAAA,GAAAJ,OAAA;AAOA,IAAAK,iBAAA,GAAAL,OAAA;AACA,IAAAM,wBAAA,GAAAN,OAAA;AAcA,IAAAO,yBAAA,GAAAP,OAAA;AAIA,IAAAQ,kBAAA,GAAAR,OAAA;AAIA,IAAAS,oBAAA,GAAAT,OAAA;AAIA,IAAAU,uBAAA,GAAAV,OAAA;AAIA,IAAAW,kBAAA,GAAAX,OAAA;AAIA,IAAAY,MAAA,GAAAZ,OAAA;AACA,IAAAa,MAAA,GAAAb,OAAA;AAA2D,SAAAD,uBAAAe,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE3D;AACA,IAAIG,SAAS,GAAGA,CACdC,QAA2B,EAC3BC,KAAa,EACb,GAAGC,KAAY,KACZ;EACH;AAAA,CACD;AAED,IAAI;EACF;EACAH,SAAS,GAAGjB,OAAO,CAAC,+BAA+B,CAAC,CAACiB,SAAS;AAChE,CAAC,CAAC,OAAOI,EAAE,EAAE;EACX;AAAA;AA0BF,MAAMC,qBAA2C,GAAG;EAClD,CAACC,qCAAgB,GAAG;IAClBC,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,gBAAgB,EAAE;EACpB,CAAC;EACD,CAACC,+CAAqB,GAAG;IACvBL,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZG,KAAK,EAAE,CAAC;IACRF,gBAAgB,EAAE;EACpB,CAAC;EACD,CAACG,6CAAoB,GAAG;IACtBP,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZK,QAAQ,EAAE,GAAG;IACbJ,gBAAgB,EAAE;EACpB,CAAC;EACD,CAACK,+CAAqB,GAAG;IACvBC,aAAa,EAAE,IAAI;IACnBN,gBAAgB,EAAE;EACpB,CAAC;EACD,CAACO,iCAAc,GAAG;IAChBX,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZS,YAAY,EAAE,GAAG;IACjBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZX,gBAAgB,EAAE,CAAC;IACnBY,UAAU,EAAEC;EACd,CAAC;EACD,CAACC,qCAAgB,GAAG;IAClBC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,CAAC;IACRC,QAAQ,EAAE,CAAC;IACXlB,gBAAgB,EAAE;EACpB,CAAC;EACD,CAACmB,2CAAmB,GAAG;IACrBC,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,IAAI;IACdJ,QAAQ,EAAE,CAAC;IACXlB,gBAAgB,EAAE;EACpB,CAAC;EACD,CAACuB,iCAAc,GAAG;IAChB3B,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE,CAAC;IACJC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,gBAAgB,EAAE;EACpB;AACF,CAAC;AAED,SAASwB,SAASA,CAChBC,kBAAmD,EAChB;EACnC,OAAOA,kBAAkB,YAAYC,oBAAW;AAClD;AAKA,SAASC,mBAAmBA,CAC1BC,KAA8B,EACE;EAChC,OAAO;IAAEC,WAAW,EAAED;EAAM,CAAC;AAC/B;AAEA,SAASE,mBAAmBA,CAC1BC,aAA6C,EAC7CC,YAAuD,EAC9B;EACzB,MAAMC,YAAY,GAAGF,aAAa,KAAK,IAAI;EAC3C,IAAIE,YAAY,EAAE;IAChB,OAAO;MACLC,QAAQ,EAAEC,YAAK,CAACC,YAAY;MAC5B,GAAGJ;IACL,CAAC;EACH;EAEA,MAAMK,mBAAmB,GAAGN,aAAa,CAACO,KAAK,KAAKN,YAAY,CAACM,KAAK;EACtE,IAAID,mBAAmB,EAAE;IACvB,OAAO;MACLH,QAAQ,EAAEH,aAAa,EAAEO,KAAK;MAC9B,GAAGN;IACL,CAAC;EACH,CAAC,MAAM;IACL,OAAOA,YAAY;EACrB;AACF;AAKA,SAASO,wBAAwBA,CAC/BR,aAAqC,EACrCC,YAA6B,EAC7B;EACA,SAASQ,SAASA,CAACZ,KAAqC,EAAE;IACxD,OAAOa,IAAI,CAACD,SAAS,CAACZ,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;EACvC;EACA,SAASc,sBAAsBA,CAACC,WAAmB,EAAE;IACnD,OAAO,GAAGA,WAAW,oBAAoBH,SAAS,CAChDR,YACF,CAAC,qBAAqBQ,SAAS,CAACT,aAAa,CAAC,EAAE;EAClD;EAEA,SAASa,wBAAwBA,CAACD,WAAmB,EAAE;IACrD,OAAO,GAAGA,WAAW,oBAAoBH,SAAS,CAACR,YAAY,CAAC,EAAE;EACpE;EAEA,IAAAa,kBAAS,EACP,IAAAC,kBAAW,EAACd,YAAY,EAAE,OAAO,CAAC,EAClCY,wBAAwB,CAAC,6BAA6B,CACxD,CAAC;EAED,MAAMX,YAAY,GAAGF,aAAa,KAAK,IAAI;EAC3C,IAAIE,YAAY,EAAE;IAChB,IAAAY,kBAAS,EACPb,YAAY,CAACM,KAAK,KAAKH,YAAK,CAACY,KAAK,EAClCH,wBAAwB,CAAC,mCAAmC,CAC9D,CAAC;EACH;EAEA,IAAIb,aAAa,KAAK,IAAI,EAAE;IAC1B,IAAIA,aAAa,CAACO,KAAK,KAAKN,YAAY,CAACM,KAAK,EAAE;MAC9C,IAAAO,kBAAS,EACP,IAAAC,kBAAW,EAACd,YAAY,EAAE,UAAU,CAAC,EACrCY,wBAAwB,CACtB,sDACF,CACF,CAAC;MACD,IAAAC,kBAAS,EACPb,YAAY,CAACE,QAAQ,KAAKH,aAAa,CAACO,KAAK,EAC7CI,sBAAsB,CACpB,0EACF,CACF,CAAC;IACH;EACF;EAEA,OAAOV,YAAY;AACrB;AAOA,SAASgB,sBAAsBA,CAAC;EAC9BC,WAAW;EACXC;AACW,CAAC,EAEU;EACtB,OAAQtB,KAAK,IAAK;IAChB,OAAO;MACL,GAAGlC,qBAAqB,CAACuD,WAAW,CAAC;MACrC,GAAGrB,KAAK;MACRsB;IACF,CAAC;EACH,CAAC;AACH;AAEA,SAASC,iBAAiBA,CAACF,WAAyB,EAAE;EACpD,OACEA,WAAW,KAAK,mBAAmB,IACnCA,WAAW,KAAK,yBAAyB;AAE7C;AAEA,SAASG,4BAA4BA,CACnCC,MAA4B,EAC5BF,iBAA0B,EACJ;EAEtB,MAAMG,OAAO,GAAG,CAAC,GAAGD,MAAM,CAAC;EAC3B,MAAME,SAAS,GAAGD,OAAO,CAACA,OAAO,CAACE,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI;EACrD,MAAMC,UAAU,GAAGH,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI;EAErC,MAAMI,yBAAyB,GAC7B,CAACP,iBAAiB,IAAI,CAACQ,QAAQ,CAACxB,YAAK,CAACY,KAAK,CAAC,CAACU,UAAU,CAAC;EAC1D,IAAIC,yBAAyB,EAAE;IAC7B,MAAME,UAAU,GAAG;MAAE,GAAGH,UAAU;MAAEnB,KAAK,EAAEH,YAAK,CAACY;IAAM,CAAC;IACxD;IACA,OAAOa,UAAU,CAAC1B,QAAQ;IAC1BoB,OAAO,CAACO,OAAO,CAACD,UAAU,CAAC;EAC7B;EAEA,MAAME,wBAAwB,GAC5B,CAACH,QAAQ,CAACxB,YAAK,CAAC4B,GAAG,CAAC,CAACR,SAAS,CAAC,IAC/B,CAACI,QAAQ,CAACxB,YAAK,CAAC6B,MAAM,CAAC,CAACT,SAAS,CAAC,IAClC,CAACI,QAAQ,CAACxB,YAAK,CAAC8B,SAAS,CAAC,CAACV,SAAS,CAAC;EAEvC,IAAIO,wBAAwB,EAAE;IAC5B,MAAMF,UAAU,GAAG;MAAE,GAAGL,SAAS;MAAEjB,KAAK,EAAEH,YAAK,CAAC4B;IAAI,CAAC;IACrD;IACA,OAAOH,UAAU,CAAC1B,QAAQ;IAC1BoB,OAAO,CAACY,IAAI,CAACN,UAAU,CAAC;EAC1B;EAEA,SAASO,cAAcA,CAACvC,KAAY,EAAE;IACpC,OAAOA,KAAK,KAAK,IAAI,IAAI,CAAC,IAAAkB,kBAAW,EAAClB,KAAK,EAAE,OAAO,CAAC;EACvD;EACA,SAAS+B,QAAQA,CAACrB,KAAY,EAAE;IAC9B,OAAQV,KAAY,IAAKA,KAAK,KAAK,IAAI,IAAIA,KAAK,CAACU,KAAK,KAAKA,KAAK;EAClE;EACA,SAAS8B,YAAYA,CAACxC,KAAY,EAAE;IAClC,OAAOA,KAAK,KAAK,IAAI;EACvB;EAEA,SAASyC,MAAMA,CAAA,EAAG;IAChB,OAAO,IAAI;EACb;EAKA,SAASC,yBAAyBA,CAAC;IACjCC,kBAAkB,GAAGF,MAAM;IAC3BG,2BAA2B,GAAGH;EAC1B,CAAC,EAAE;IACP,SAASI,gBAAgBA,CAAA,EAAU;MACjC,OAAOnB,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI;IAC3B;IACA,SAASoB,aAAaA,CAAA,EAAU;MAC9B,OAAOpB,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI;IAC3B;IACA,SAASqB,mBAAmBA,CAAA,EAAG;MAC7BrB,OAAO,CAACsB,KAAK,CAAC,CAAC;IACjB;IACA,MAAM5C,YAAY,GAAGyC,gBAAgB,CAAC,CAAC;IACvC,MAAMI,SAAS,GAAGH,aAAa,CAAC,CAAC;IACjC,MAAMI,oBAAoB,GAAGC,eAAe,CAACC,eAAe,CAAC;IAE7D,IAAIC,SAAS,GAAG,CAAC,CAAC;IAClB,MAAMC,cAAc,GAAGX,kBAAkB,CAACvC,YAAY,CAAC;IACvD,IAAIkD,cAAc,EAAE;MAClBD,SAAS,GAAGjD,YAAa;MACzB2C,mBAAmB,CAAC,CAAC;IACvB;IACAQ,iBAAiB,CAACjB,IAAI,CAAC;MAAE5B,KAAK,EAAEwC,oBAAoB;MAAE,GAAGG;IAAU,CAAC,CAAC;IACrE,IAAIT,2BAA2B,CAACK,SAAS,CAAC,EAAE;MAC1CG,eAAe,EAAE;IACnB;EACF;EAEA,MAAMD,eAAe,GAAG,CAAC5C,YAAK,CAACY,KAAK,EAAEZ,YAAK,CAACiD,MAAM,EAAEjD,YAAK,CAAC4B,GAAG,CAAC;EAE9D,IAAIiB,eAAe,GAAG,CAAC;EACvB,MAAMG,iBAAuC,GAAG,EAAE;EAClD,IAAIE,YAAY;EAChB,IAAIC,UAAU,GAAG,CAAC;EAClB,GAAG;IACD,MAAMC,iBAAiB,GAAGR,eAAe,CAACC,eAAe,CAAC;IAC1D,IAAIO,iBAAiB,KAAKpD,YAAK,CAACY,KAAK,EAAE;MACrCuB,yBAAyB,CAAC;QACxBC,kBAAkB,EAAGrF,CAAQ,IAC3BiF,cAAc,CAACjF,CAAC,CAAC,IAAIyE,QAAQ,CAACxB,YAAK,CAACY,KAAK,CAAC,CAAC7D,CAAC;MAChD,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIqG,iBAAiB,KAAKpD,YAAK,CAACiD,MAAM,EAAE;MAC7C,MAAMb,kBAAkB,GAAIrF,CAAQ,IAClCiF,cAAc,CAACjF,CAAC,CAAC,IAAIyE,QAAQ,CAACxB,YAAK,CAACiD,MAAM,CAAC,CAAClG,CAAC,CAAC;MAChD,MAAMsF,2BAA2B,GAAIK,SAAgB,IACnDT,YAAY,CAACS,SAAS,CAAC,IACvBlB,QAAQ,CAACxB,YAAK,CAAC4B,GAAG,CAAC,CAACc,SAAS,CAAC,IAC9BlB,QAAQ,CAACxB,YAAK,CAAC6B,MAAM,CAAC,CAACa,SAAS,CAAC,IACjClB,QAAQ,CAACxB,YAAK,CAAC8B,SAAS,CAAC,CAACY,SAAS,CAAC;MAEtCP,yBAAyB,CAAC;QACxBC,kBAAkB;QAClBC;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIe,iBAAiB,KAAKpD,YAAK,CAAC4B,GAAG,EAAE;MAC1CO,yBAAyB,CAAC,CAAC,CAAC,CAAC;IAC/B;IACAe,YAAY,GAAGL,eAAe,KAAKD,eAAe,CAACvB,MAAM;IAEzD,IAAAX,kBAAS,EACPyC,UAAU,EAAE,IAAI,GAAG,EACnB,+FACF,CAAC;EACH,CAAC,QAAQ,CAACD,YAAY;EAEtB,OAAOF,iBAAiB;AAC1B;AAYA,SAASK,cAAcA,CACrB/D,kBAAmD,EACtC;EACb,IAAID,SAAS,CAACC,kBAAkB,CAAC,EAAE;IACjC,MAAMgE,OAAO,GAAGhE,kBAAkB;IAClC,OAAO;MACLiE,SAAS,EAAEA,CAACC,SAAS,EAAEC,IAAI,KAAK;QAC9BC,+BAAkB,CAACC,IAAI,CAACH,SAAS,EAAEC,IAAI,CAAC/D,WAAW,CAAC;MACtD,CAAC;MACDoB,WAAW,EAAEwC,OAAO,CAACM,WAA2B;MAChD7C,UAAU,EAAEuC,OAAO,CAACvC,UAAU;MAC9B8C,OAAO,EAAEP,OAAO,CAACQ,MAAM,CAACD;IAC1B,CAAC;EACH;EACA,MAAME,uBAAuB,GAAGzE,kBAAkB;EAClD,OAAO;IACLiE,SAAS,EAAEA,CAACC,SAAS,EAAEC,IAAI,KAAK;MAC9BvG,SAAS,CAAC6G,uBAAuB,EAAEP,SAAS,EAAEC,IAAI,CAAC;IACrD,CAAC;IACD3C,WAAW,EAAEiD,uBAAuB,CAACC,KAAK,CAAClD,WAA2B;IACtEC,UAAU,EAAEgD,uBAAuB,CAACC,KAAK,CAACjD,UAAoB;IAC9D8C,OAAO,EAAEE,uBAAuB,CAACC,KAAK,CAACH;EACzC,CAAC;AACH;;AAqBA;;AAaO,SAASI,kBAAkBA,CAChC3E,kBAAmD,EACnD4E,SAAsE,GAAG,EAAE,EACrE;EACN,MAAM;IAAEX,SAAS;IAAEzC,WAAW;IAAEC,UAAU;IAAE8C;EAAQ,CAAC,GACnDR,cAAc,CAAC/D,kBAAkB,CAAC;EAEpC,IAAIuE,OAAO,KAAK,KAAK,EAAE;IACrB;EACF;EAEA,IAAIM,CAAC,GAAGlD,4BAA4B,CAClCiD,SAAS,EACTlD,iBAAiB,CAACF,WAAW,CAC/B,CAAC;EACDqD,CAAC,GAAGA,CAAC,CAACC,GAAG,CAACvD,sBAAsB,CAAC;IAAEE,UAAU;IAAED;EAAY,CAAC,CAAC,CAAC;EAC9DqD,CAAC,GAAG,IAAAE,yBAAkB,EAACF,CAAC,EAAExE,mBAAmB,CAAC;EAC9CwE,CAAC,GAAG,IAAAE,yBAAkB,EAACF,CAAC,EAAE/D,wBAAwB,CAAC;EACnD;EACA+D,CAAC,GAAGA,CAAC,CAACC,GAAG,CAAC5E,mBAAmB,CAAC;EAE9B,MAAM0B,MAAM,GAAGiD,CAAgD;EAE/D,MAAM7C,UAAU,GAAGJ,MAAM,CAACuB,KAAK,CAAC,CAAE;EAElCc,SAAS,CAAC,6BAA6B,EAAEjC,UAAU,CAAC;EACpD,IAAIgD,aAAa,GAAGhD,UAAU;EAC9B,KAAK,MAAM7B,KAAK,IAAIyB,MAAM,EAAE;IAC1B,MAAMqD,eAAe,GACnBD,aAAa,CAAC5E,WAAW,CAACS,KAAK,KAAKV,KAAK,CAACC,WAAW,CAACS,KAAK;IAE7D,IAAIoE,eAAe,EAAE;MACnBhB,SAAS,CAAC,6BAA6B,EAAE9D,KAAK,CAAC;IACjD,CAAC,MAAM;MACL8D,SAAS,CAAC,uBAAuB,EAAE9D,KAAK,CAAC;IAC3C;IACA6E,aAAa,GAAG7E,KAAK;EACvB;AACF;AAEO,SAAS+E,kBAAkBA,CAACC,MAAc,EAAE;EACjD,MAAMC,OAAO,GAAG,IAAAC,qCAAmB,EAACF,MAAM,CAAC;EAC3C,IAAIC,OAAO,KAAK,IAAI,EAAE;IACpB,MAAM,IAAIE,KAAK,CAAC,qBAAqBH,MAAM,mBAAmB,CAAC;EACjE;EACA,OAAOC,OAAO;AAChB", "ignoreList": []}