{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_EnableNewWebImplementation", "_Gestures", "_InteractionManager", "_NodeManager", "HammerNodeManager", "_interopRequireWildcard", "_GestureHandlerWebDelegate", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "shouldPreventDrop", "_default", "exports", "handleSetJSResponder", "tag", "blockNativeResponder", "console", "warn", "handleClearJSResponder", "createGestureHandler", "handler<PERSON>ame", "handlerTag", "config", "isNewWebImplementationEnabled", "Gestures", "Error", "GestureClass", "NodeManager", "GestureHandlerWebDelegate", "InteractionManager", "instance", "configureInteractions", "<PERSON><PERSON><PERSON><PERSON>", "HammerGestures", "updateGestureHandler", "attachGestureHandler", "newView", "_actionType", "propsRef", "Element", "React", "Component", "handler", "constructor", "name", "init", "<PERSON><PERSON><PERSON><PERSON>", "newConfig", "updateGestureConfig", "getGestureHandlerNode", "dropGestureHandler", "flushOperations"], "sourceRoot": "../../src", "sources": ["RNGestureHandlerModule.web.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAGA,IAAAC,2BAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AAEA,IAAAG,mBAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,YAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,iBAAA,GAAAC,uBAAA,CAAAN,OAAA;AACA,IAAAO,0BAAA,GAAAP,OAAA;AAAkF,SAAAM,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,CAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAV,uBAAAS,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAK,UAAA,GAAAL,CAAA,KAAAU,OAAA,EAAAV,CAAA;AAElF;AACA;AACA;AACA;AACA;AACA,IAAImB,iBAAiB,GAAG,KAAK;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAX,OAAA,GAEf;EACbY,oBAAoBA,CAACC,GAAW,EAAEC,oBAA6B,EAAE;IAC/DC,OAAO,CAACC,IAAI,CAAC,wBAAwB,EAAEH,GAAG,EAAEC,oBAAoB,CAAC;EACnE,CAAC;EACDG,sBAAsBA,CAAA,EAAG;IACvBF,OAAO,CAACC,IAAI,CAAC,0BAA0B,CAAC;EAC1C,CAAC;EACDE,oBAAoBA,CAClBC,WAAkC,EAClCC,UAAkB,EAClBC,MAAS,EACT;IACA,IAAI,IAAAC,yDAA6B,EAAC,CAAC,EAAE;MACnC,IAAI,EAAEH,WAAW,IAAII,kBAAQ,CAAC,EAAE;QAC9B,MAAM,IAAIC,KAAK,CACb,iCAAiCL,WAAW,2BAC9C,CAAC;MACH;MAEA,MAAMM,YAAY,GAAGF,kBAAQ,CAACJ,WAAW,CAAC;MAC1CO,oBAAW,CAACR,oBAAoB,CAC9BE,UAAU,EACV,IAAIK,YAAY,CAAC,IAAIE,oDAAyB,CAAC,CAAC,CAClD,CAAC;MACDC,2BAAkB,CAACC,QAAQ,CAACC,qBAAqB,CAC/CJ,oBAAW,CAACK,UAAU,CAACX,UAAU,CAAC,EAClCC,MACF,CAAC;IACH,CAAC,MAAM;MACL,IAAI,EAAEF,WAAW,IAAIa,wBAAc,CAAC,EAAE;QACpC,MAAM,IAAIR,KAAK,CACb,iCAAiCL,WAAW,2BAC9C,CAAC;MACH;;MAEA;MACA;MACA,MAAMM,YAAY,GAAGO,wBAAc,CAACb,WAAW,CAAC;MAChD;MACAhC,iBAAiB,CAAC+B,oBAAoB,CAACE,UAAU,EAAE,IAAIK,YAAY,CAAC,CAAC,CAAC;IACxE;IAEA,IAAI,CAACQ,oBAAoB,CAACb,UAAU,EAAEC,MAA2B,CAAC;EACpE,CAAC;EACDa,oBAAoBA,CAClBd,UAAkB;EAClB;EACAe,OAAY,EACZC,WAAuB,EACvBC,QAAkC,EAClC;IACA,IAAI,EAAEF,OAAO,YAAYG,OAAO,IAAIH,OAAO,YAAYI,cAAK,CAACC,SAAS,CAAC,EAAE;MACvE/B,iBAAiB,GAAG,IAAI;MAExB,MAAMgC,OAAO,GAAG,IAAAnB,yDAA6B,EAAC,CAAC,GAC3CI,oBAAW,CAACK,UAAU,CAACX,UAAU,CAAC,GAClCjC,iBAAiB,CAAC4C,UAAU,CAACX,UAAU,CAAC;MAE5C,MAAMD,WAAW,GAAGsB,OAAO,CAACC,WAAW,CAACC,IAAI;MAE5C,MAAM,IAAInB,KAAK,CACb,GAAGL,WAAW,aAAaC,UAAU,iDACvC,CAAC;IACH;IAEA,IAAI,IAAAE,yDAA6B,EAAC,CAAC,EAAE;MACnC;MACAI,oBAAW,CAACK,UAAU,CAACX,UAAU,CAAC,CAACwB,IAAI,CAACT,OAAO,EAAEE,QAAQ,CAAC;IAC5D,CAAC,MAAM;MACL;MACAlD,iBAAiB,CAAC4C,UAAU,CAACX,UAAU,CAAC,CAACyB,OAAO,CAACV,OAAO,EAAEE,QAAQ,CAAC;IACrE;EACF,CAAC;EACDJ,oBAAoBA,CAACb,UAAkB,EAAE0B,SAAiB,EAAE;IAC1D,IAAI,IAAAxB,yDAA6B,EAAC,CAAC,EAAE;MACnCI,oBAAW,CAACK,UAAU,CAACX,UAAU,CAAC,CAAC2B,mBAAmB,CAACD,SAAS,CAAC;MAEjElB,2BAAkB,CAACC,QAAQ,CAACC,qBAAqB,CAC/CJ,oBAAW,CAACK,UAAU,CAACX,UAAU,CAAC,EAClC0B,SACF,CAAC;IACH,CAAC,MAAM;MACL3D,iBAAiB,CAAC4C,UAAU,CAACX,UAAU,CAAC,CAAC2B,mBAAmB,CAACD,SAAS,CAAC;IACzE;EACF,CAAC;EACDE,qBAAqBA,CAAC5B,UAAkB,EAAE;IACxC,IAAI,IAAAE,yDAA6B,EAAC,CAAC,EAAE;MACnC,OAAOI,oBAAW,CAACK,UAAU,CAACX,UAAU,CAAC;IAC3C,CAAC,MAAM;MACL,OAAOjC,iBAAiB,CAAC4C,UAAU,CAACX,UAAU,CAAC;IACjD;EACF,CAAC;EACD6B,kBAAkBA,CAAC7B,UAAkB,EAAE;IACrC,IAAIX,iBAAiB,EAAE;MACrB;IACF;IAEA,IAAI,IAAAa,yDAA6B,EAAC,CAAC,EAAE;MACnCI,oBAAW,CAACuB,kBAAkB,CAAC7B,UAAU,CAAC;IAC5C,CAAC,MAAM;MACLjC,iBAAiB,CAAC8D,kBAAkB,CAAC7B,UAAU,CAAC;IAClD;EACF,CAAC;EACD;EACA8B,eAAeA,CAAA,EAAG,CAAC;AACrB,CAAC", "ignoreList": []}