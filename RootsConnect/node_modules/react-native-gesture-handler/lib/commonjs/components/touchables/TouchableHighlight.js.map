{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "React", "_GenericTouchable", "_reactNative", "_jsxRuntime", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "TouchableHighlight", "Component", "defaultProps", "GenericTouchable", "activeOpacity", "delayPressOut", "underlayColor", "constructor", "props", "state", "extraChildStyle", "extraUnderlayStyle", "showUnderlay", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setState", "opacity", "backgroundColor", "onShowUnderlay", "onPress", "onPressIn", "onPressOut", "onLongPress", "<PERSON><PERSON><PERSON><PERSON>", "onHideUnderlay", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "jsx", "View", "child", "Children", "only", "cloneElement", "style", "StyleSheet", "compose", "onStateChange", "_from", "to", "TOUCHABLE_STATE", "BEGAN", "UNDETERMINED", "MOVED_OUTSIDE", "render", "rest", "exports"], "sourceRoot": "../../../../src", "sources": ["components/touchables/TouchableHighlight.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AAA+B,IAAAC,KAAA,GAAAH,MAAA;AAE/B,IAAAI,iBAAA,GAAAH,uBAAA,CAAAC,OAAA;AAEA,IAAAG,YAAA,GAAAH,OAAA;AAMsB,IAAAI,WAAA,GAAAJ,OAAA;AAAA,SAAAD,wBAAAM,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAR,uBAAA,YAAAA,CAAAM,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAWtB;AACA;AACA;;AAIA;AACA;AACA;AACA;AACA;AACe,MAAMkB,kBAAkB,SAASC,gBAAS,CAGvD;EACA,OAAOC,YAAY,GAAG;IACpB,GAAGC,yBAAgB,CAACD,YAAY;IAChCE,aAAa,EAAE,IAAI;IACnBC,aAAa,EAAE,GAAG;IAClBC,aAAa,EAAE;EACjB,CAAC;EAEDC,WAAWA,CAACC,KAA8B,EAAE;IAC1C,KAAK,CAACA,KAAK,CAAC;IACZ,IAAI,CAACC,KAAK,GAAG;MACXC,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE;IACtB,CAAC;EACH;;EAEA;EACAC,YAAY,GAAGA,CAAA,KAAM;IACnB,IAAI,CAAC,IAAI,CAACC,eAAe,CAAC,CAAC,EAAE;MAC3B;IACF;IACA,IAAI,CAACC,QAAQ,CAAC;MACZJ,eAAe,EAAE;QACfK,OAAO,EAAE,IAAI,CAACP,KAAK,CAACJ;MACtB,CAAC;MACDO,kBAAkB,EAAE;QAClBK,eAAe,EAAE,IAAI,CAACR,KAAK,CAACF;MAC9B;IACF,CAAC,CAAC;IACF,IAAI,CAACE,KAAK,CAACS,cAAc,GAAG,CAAC;EAC/B,CAAC;EAEDJ,eAAe,GAAGA,CAAA,KAChB,IAAI,CAACL,KAAK,CAACU,OAAO,IAClB,IAAI,CAACV,KAAK,CAACW,SAAS,IACpB,IAAI,CAACX,KAAK,CAACY,UAAU,IACrB,IAAI,CAACZ,KAAK,CAACa,WAAW;EAExBC,YAAY,GAAGA,CAAA,KAAM;IACnB,IAAI,CAACR,QAAQ,CAAC;MACZJ,eAAe,EAAE,IAAI;MACrBC,kBAAkB,EAAE;IACtB,CAAC,CAAC;IACF,IAAI,CAACH,KAAK,CAACe,cAAc,GAAG,CAAC;EAC/B,CAAC;EAEDC,cAAcA,CAAA,EAAG;IACf,IAAI,CAAC,IAAI,CAAChB,KAAK,CAACiB,QAAQ,EAAE;MACxB,oBAAO,IAAA7C,WAAA,CAAA8C,GAAA,EAAC/C,YAAA,CAAAgD,IAAI,IAAE,CAAC;IACjB;IAEA,MAAMC,KAAK,GAAGnD,KAAK,CAACoD,QAAQ,CAACC,IAAI,CAC/B,IAAI,CAACtB,KAAK,CAACiB,QACb,CAAkC,CAAC,CAAC;IACpC,oBAAOhD,KAAK,CAACsD,YAAY,CAACH,KAAK,EAAE;MAC/BI,KAAK,EAAEC,uBAAU,CAACC,OAAO,CAACN,KAAK,CAACpB,KAAK,CAACwB,KAAK,EAAE,IAAI,CAACvB,KAAK,CAACC,eAAe;IACzE,CAAC,CAAC;EACJ;EAEAyB,aAAa,GAAGA,CAACC,KAAa,EAAEC,EAAU,KAAK;IAC7C,IAAIA,EAAE,KAAKC,iCAAe,CAACC,KAAK,EAAE;MAChC,IAAI,CAAC3B,YAAY,CAAC,CAAC;IACrB,CAAC,MAAM,IACLyB,EAAE,KAAKC,iCAAe,CAACE,YAAY,IACnCH,EAAE,KAAKC,iCAAe,CAACG,aAAa,EACpC;MACA,IAAI,CAACnB,YAAY,CAAC,CAAC;IACrB;EACF,CAAC;EAEDoB,MAAMA,CAAA,EAAG;IACP,MAAM;MAAEV,KAAK,GAAG,CAAC,CAAC;MAAE,GAAGW;IAAK,CAAC,GAAG,IAAI,CAACnC,KAAK;IAC1C,MAAM;MAAEG;IAAmB,CAAC,GAAG,IAAI,CAACF,KAAK;IACzC,oBACE,IAAA7B,WAAA,CAAA8C,GAAA,EAAChD,iBAAA,CAAAa,OAAgB;MAAA,GACXoD,IAAI;MACRX,KAAK,EAAE,CAACA,KAAK,EAAErB,kBAAkB,CAAE;MACnCwB,aAAa,EAAE,IAAI,CAACA,aAAc;MAAAV,QAAA,EACjC,IAAI,CAACD,cAAc,CAAC;IAAC,CACN,CAAC;EAEvB;AACF;AAACoB,OAAA,CAAArD,OAAA,GAAAS,kBAAA", "ignoreList": []}