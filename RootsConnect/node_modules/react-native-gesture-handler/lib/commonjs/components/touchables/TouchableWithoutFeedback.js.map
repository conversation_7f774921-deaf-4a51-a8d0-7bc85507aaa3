{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_GenericTouchable", "_interopRequireDefault", "_jsxRuntime", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "TouchableWithoutFeedback", "forwardRef", "delayLongPress", "extraButtonProps", "rippleColor", "exclusive", "rest", "ref", "jsx", "_default", "exports"], "sourceRoot": "../../../../src", "sources": ["components/touchables/TouchableWithoutFeedback.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AAEA,IAAAC,iBAAA,GAAAC,sBAAA,CAAAF,OAAA;AAAkD,IAAAG,WAAA,GAAAH,OAAA;AAAA,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAL,wBAAAK,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAT,uBAAA,YAAAA,CAAAK,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAGlD;AACA;AACA;;AAGA;AACA;AACA;AACA,MAAMgB,wBAAwB,gBAAGzB,KAAK,CAAC0B,UAAU,CAI/C,CACE;EACEC,cAAc,GAAG,GAAG;EACpBC,gBAAgB,GAAG;IACjBC,WAAW,EAAE,aAAa;IAC1BC,SAAS,EAAE;EACb,CAAC;EACD,GAAGC;AACL,CAAC,EAEDC,GAAG,kBAEH,IAAA3B,WAAA,CAAA4B,GAAA,EAAC9B,iBAAA,CAAAK,OAAgB;EACfwB,GAAG,EAAEA,GAAI;EACTL,cAAc,EAAEA,cAAe;EAC/BC,gBAAgB,EAAEA,gBAAiB;EAAA,GAC/BG;AAAI,CACT,CAEL,CAAC;AAAC,IAAAG,QAAA,GAAAC,OAAA,CAAA3B,OAAA,GAEaiB,wBAAwB", "ignoreList": []}