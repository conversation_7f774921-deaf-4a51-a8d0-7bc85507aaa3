import { CborV<PERSON>ueType, Float32, Uint8, Uint32 } from "./cbor-types";
/**
 * @internal
 * @param bytes - to be set as the decode source.
 *
 * Sets the decode bytearray source and its data view.
 */
export declare function setPayload(bytes: Uint8Array): void;
/**
 * @internal
 * Decodes the data between the two indices.
 */
export declare function decode(at: Uint32, to: Uint32): CborValueType;
/**
 * @internal
 */
export declare function bytesToFloat16(a: Uint8, b: Uint8): Float32;
