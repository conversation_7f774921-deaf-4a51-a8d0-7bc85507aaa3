"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CcApiContextProviderPlugin = void 0;
const client_cloudcontrol_1 = require("@aws-sdk/client-cloudcontrol");
const private_1 = require("../api/aws-auth/private");
const toolkit_error_1 = require("../toolkit/toolkit-error");
const util_1 = require("../util");
class CcApiContextProviderPlugin {
    aws;
    constructor(aws) {
        this.aws = aws;
    }
    /**
     * This returns a data object with the value from CloudControl API result.
     *
     * See the documentation in the Cloud Assembly Schema for the semantics of
     * each query parameter.
     */
    async getValue(args) {
        // Validate input
        if (args.exactIdentifier && args.propertyMatch) {
            throw new toolkit_error_1.ContextProviderError(`Provider protocol error: specify either exactIdentifier or propertyMatch, but not both (got ${JSON.stringify(args)})`);
        }
        if (args.ignoreErrorOnMissingContext && args.dummyValue === undefined) {
            throw new toolkit_error_1.ContextProviderError(`Provider protocol error: if ignoreErrorOnMissingContext is set, a dummyValue must be supplied (got ${JSON.stringify(args)})`);
        }
        if (args.dummyValue !== undefined && (!Array.isArray(args.dummyValue) || !args.dummyValue.every(isObject))) {
            throw new toolkit_error_1.ContextProviderError(`Provider protocol error: dummyValue must be an array of objects (got ${JSON.stringify(args.dummyValue)})`);
        }
        // Do the lookup
        const cloudControl = (await (0, private_1.initContextProviderSdk)(this.aws, args)).cloudControl();
        try {
            let resources;
            if (args.exactIdentifier) {
                // use getResource to get the exact identifier
                resources = await this.getResource(cloudControl, args.typeName, args.exactIdentifier);
            }
            else if (args.propertyMatch) {
                // use listResource
                resources = await this.listResources(cloudControl, args.typeName, args.propertyMatch, args.expectedMatchCount);
            }
            else {
                throw new toolkit_error_1.ContextProviderError(`Provider protocol error: neither exactIdentifier nor propertyMatch is specified in ${JSON.stringify(args)}.`);
            }
            return resources.map((r) => (0, util_1.getResultObj)(r.properties, r.identifier, args.propertiesToReturn));
        }
        catch (err) {
            if (toolkit_error_1.ContextProviderError.isNoResultsFoundError(err) && args.ignoreErrorOnMissingContext) {
                // We've already type-checked dummyValue.
                return args.dummyValue;
            }
            throw err;
        }
    }
    /**
     * Calls getResource from CC API to get the resource.
     * See https://docs.aws.amazon.com/cli/latest/reference/cloudcontrol/get-resource.html
     *
     * Will always return exactly one resource, or fail.
     */
    async getResource(cc, typeName, exactIdentifier) {
        try {
            const result = await cc.getResource({
                TypeName: typeName,
                Identifier: exactIdentifier,
            });
            if (!result.ResourceDescription) {
                throw new toolkit_error_1.ContextProviderError('Unexpected CloudControl API behavior: returned empty response');
            }
            return [foundResourceFromCcApi(result.ResourceDescription)];
        }
        catch (err) {
            if (err instanceof client_cloudcontrol_1.ResourceNotFoundException || err.name === 'ResourceNotFoundException') {
                throw new toolkit_error_1.NoResultsFoundError(`No resource of type ${typeName} with identifier: ${exactIdentifier}`);
            }
            if (!toolkit_error_1.ContextProviderError.isContextProviderError(err)) {
                throw toolkit_error_1.ContextProviderError.withCause(`Encountered CC API error while getting ${typeName} resource ${exactIdentifier}`, err);
            }
            throw err;
        }
    }
    /**
     * Calls listResources from CC API to get the resources and apply args.propertyMatch to find the resources.
     * See https://docs.aws.amazon.com/cli/latest/reference/cloudcontrol/list-resources.html
     *
     * Will return 0 or more resources.
     *
     * Does not currently paginate through more than one result page.
     */
    async listResources(cc, typeName, propertyMatch, expectedMatchCount) {
        try {
            const result = await cc.listResources({
                TypeName: typeName,
            });
            const found = (result.ResourceDescriptions ?? [])
                .map(foundResourceFromCcApi)
                .filter((r) => {
                return Object.entries(propertyMatch).every(([propPath, expected]) => {
                    const actual = (0, util_1.findJsonValue)(r.properties, propPath);
                    return propertyMatchesFilter(actual, expected);
                });
            });
            if ((expectedMatchCount === 'at-least-one' || expectedMatchCount === 'exactly-one') && found.length === 0) {
                throw new toolkit_error_1.NoResultsFoundError(`Could not find any resources matching ${JSON.stringify(propertyMatch)}; expected ${expectedMatchCountText(expectedMatchCount)}.`);
            }
            if ((expectedMatchCount === 'at-most-one' || expectedMatchCount === 'exactly-one') && found.length > 1) {
                throw new toolkit_error_1.ContextProviderError(`Found ${found.length} resources matching ${JSON.stringify(propertyMatch)}; expected ${expectedMatchCountText(expectedMatchCount)}. Please narrow the search criteria`);
            }
            return found;
        }
        catch (err) {
            if (!toolkit_error_1.ContextProviderError.isContextProviderError(err)) {
                throw toolkit_error_1.ContextProviderError.withCause(`Encountered CC API error while listing ${typeName} resources matching ${JSON.stringify(propertyMatch)}`, err);
            }
            throw err;
        }
    }
}
exports.CcApiContextProviderPlugin = CcApiContextProviderPlugin;
/**
 * Convert a CC API response object into a nicer object (parse the JSON)
 */
function foundResourceFromCcApi(desc) {
    return {
        identifier: desc.Identifier ?? '*MISSING*',
        properties: JSON.parse(desc.Properties ?? '{}'),
    };
}
/**
 * Whether the given property value matches the given filter
 *
 * For now we just check for strict equality, but we can implement pattern matching and fuzzy matching here later
 */
function propertyMatchesFilter(actual, expected) {
    return expected === actual;
}
function isObject(x) {
    return typeof x === 'object' && x !== null && !Array.isArray(x);
}
function expectedMatchCountText(expectation) {
    switch (expectation) {
        case 'at-least-one':
            return 'at least one';
        case 'at-most-one':
            return 'at most one';
        case 'exactly-one':
            return 'exactly one';
        case 'any':
            return 'any number';
        default:
            return expectation;
    }
}
//# sourceMappingURL=data:application/json;base64,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