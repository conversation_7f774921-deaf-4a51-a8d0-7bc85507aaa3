"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CachedCloudAssembly = void 0;
require("../../private/dispose-polyfill");
const borrowed_assembly_1 = require("./private/borrowed-assembly");
/**
 * A CloudAssemblySource that is caching its result once produced.
 *
 * Most Toolkit interactions should use a cached source. Not caching is
 * relevant when the source changes frequently and it is to expensive to predict
 * if the source has changed.
 *
 * The `CachedCloudAssembly` is both itself a readable CloudAssembly, as well as
 * a Cloud Assembly Source. The lifetimes of cloud assemblies produced by this
 * source are coupled to the lifetime of the `CachedCloudAssembly`. In other
 * words: the `dispose()` functions of those cloud assemblies don't do anything;
 * only the `dispose()` function of the `CachedCloudAssembly` will be used.
 *
 * NOTE: if we are concerned about borrowed assemblies outliving the parent
 * (i.e. the parent getting disposed while someone is still working with the
 * borrowed copies), we could consider referencing counting here. That seems
 * unnecessarily complicated for now, we will just assume that everyone is
 * being a good citizen an borrowed copies are only used by the toolkit and
 * immediately disposed of.
 *
 * Because `dispose()` is a no-op on the borrowed assembly, you can omit it
 * without changing behavior, but that would turn into a leak if we ever introduced
 * reference counting. Failing to dispose the result if a `produce()` call of a
 * `CachedCloudAssembly` is considered a bug.
 */
class CachedCloudAssembly {
    asm;
    constructor(asm) {
        this.asm = asm;
    }
    get cloudAssembly() {
        return this.asm.cloudAssembly;
    }
    async produce() {
        return new borrowed_assembly_1.BorrowedAssembly(this.asm.cloudAssembly);
    }
    _unlock() {
        return this.asm._unlock();
    }
    dispose() {
        return this.asm.dispose();
    }
    [Symbol.asyncDispose]() {
        return this.dispose();
    }
}
exports.CachedCloudAssembly = CachedCloudAssembly;
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY2FjaGVkLXNvdXJjZS5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbImNhY2hlZC1zb3VyY2UudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7O0FBQUEsMENBQXdDO0FBRXhDLG1FQUErRDtBQUcvRDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0dBd0JHO0FBQ0gsTUFBYSxtQkFBbUI7SUFDdEIsR0FBRyxDQUF5QjtJQUVwQyxZQUFtQixHQUEyQjtRQUM1QyxJQUFJLENBQUMsR0FBRyxHQUFHLEdBQUcsQ0FBQztJQUNqQixDQUFDO0lBRUQsSUFBVyxhQUFhO1FBQ3RCLE9BQU8sSUFBSSxDQUFDLEdBQUcsQ0FBQyxhQUFhLENBQUM7SUFDaEMsQ0FBQztJQUVNLEtBQUssQ0FBQyxPQUFPO1FBQ2xCLE9BQU8sSUFBSSxvQ0FBZ0IsQ0FBQyxJQUFJLENBQUMsR0FBRyxDQUFDLGFBQWEsQ0FBQyxDQUFDO0lBQ3RELENBQUM7SUFFTSxPQUFPO1FBQ1osT0FBTyxJQUFJLENBQUMsR0FBRyxDQUFDLE9BQU8sRUFBRSxDQUFDO0lBQzVCLENBQUM7SUFFTSxPQUFPO1FBQ1osT0FBTyxJQUFJLENBQUMsR0FBRyxDQUFDLE9BQU8sRUFBRSxDQUFDO0lBQzVCLENBQUM7SUFFTSxDQUFDLE1BQU0sQ0FBQyxZQUFZLENBQUM7UUFDMUIsT0FBTyxJQUFJLENBQUMsT0FBTyxFQUFFLENBQUM7SUFDeEIsQ0FBQztDQUNGO0FBMUJELGtEQTBCQyIsInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnLi4vLi4vcHJpdmF0ZS9kaXNwb3NlLXBvbHlmaWxsJztcbmltcG9ydCB0eXBlICogYXMgY3hhcGkgZnJvbSAnQGF3cy1jZGsvY3gtYXBpJztcbmltcG9ydCB7IEJvcnJvd2VkQXNzZW1ibHkgfSBmcm9tICcuL3ByaXZhdGUvYm9ycm93ZWQtYXNzZW1ibHknO1xuaW1wb3J0IHR5cGUgeyBJQ2xvdWRBc3NlbWJseVNvdXJjZSwgSVJlYWRhYmxlQ2xvdWRBc3NlbWJseSB9IGZyb20gJy4vdHlwZXMnO1xuXG4vKipcbiAqIEEgQ2xvdWRBc3NlbWJseVNvdXJjZSB0aGF0IGlzIGNhY2hpbmcgaXRzIHJlc3VsdCBvbmNlIHByb2R1Y2VkLlxuICpcbiAqIE1vc3QgVG9vbGtpdCBpbnRlcmFjdGlvbnMgc2hvdWxkIHVzZSBhIGNhY2hlZCBzb3VyY2UuIE5vdCBjYWNoaW5nIGlzXG4gKiByZWxldmFudCB3aGVuIHRoZSBzb3VyY2UgY2hhbmdlcyBmcmVxdWVudGx5IGFuZCBpdCBpcyB0byBleHBlbnNpdmUgdG8gcHJlZGljdFxuICogaWYgdGhlIHNvdXJjZSBoYXMgY2hhbmdlZC5cbiAqXG4gKiBUaGUgYENhY2hlZENsb3VkQXNzZW1ibHlgIGlzIGJvdGggaXRzZWxmIGEgcmVhZGFibGUgQ2xvdWRBc3NlbWJseSwgYXMgd2VsbCBhc1xuICogYSBDbG91ZCBBc3NlbWJseSBTb3VyY2UuIFRoZSBsaWZldGltZXMgb2YgY2xvdWQgYXNzZW1ibGllcyBwcm9kdWNlZCBieSB0aGlzXG4gKiBzb3VyY2UgYXJlIGNvdXBsZWQgdG8gdGhlIGxpZmV0aW1lIG9mIHRoZSBgQ2FjaGVkQ2xvdWRBc3NlbWJseWAuIEluIG90aGVyXG4gKiB3b3JkczogdGhlIGBkaXNwb3NlKClgIGZ1bmN0aW9ucyBvZiB0aG9zZSBjbG91ZCBhc3NlbWJsaWVzIGRvbid0IGRvIGFueXRoaW5nO1xuICogb25seSB0aGUgYGRpc3Bvc2UoKWAgZnVuY3Rpb24gb2YgdGhlIGBDYWNoZWRDbG91ZEFzc2VtYmx5YCB3aWxsIGJlIHVzZWQuXG4gKlxuICogTk9URTogaWYgd2UgYXJlIGNvbmNlcm5lZCBhYm91dCBib3Jyb3dlZCBhc3NlbWJsaWVzIG91dGxpdmluZyB0aGUgcGFyZW50XG4gKiAoaS5lLiB0aGUgcGFyZW50IGdldHRpbmcgZGlzcG9zZWQgd2hpbGUgc29tZW9uZSBpcyBzdGlsbCB3b3JraW5nIHdpdGggdGhlXG4gKiBib3Jyb3dlZCBjb3BpZXMpLCB3ZSBjb3VsZCBjb25zaWRlciByZWZlcmVuY2luZyBjb3VudGluZyBoZXJlLiBUaGF0IHNlZW1zXG4gKiB1bm5lY2Vzc2FyaWx5IGNvbXBsaWNhdGVkIGZvciBub3csIHdlIHdpbGwganVzdCBhc3N1bWUgdGhhdCBldmVyeW9uZSBpc1xuICogYmVpbmcgYSBnb29kIGNpdGl6ZW4gYW4gYm9ycm93ZWQgY29waWVzIGFyZSBvbmx5IHVzZWQgYnkgdGhlIHRvb2xraXQgYW5kXG4gKiBpbW1lZGlhdGVseSBkaXNwb3NlZCBvZi5cbiAqXG4gKiBCZWNhdXNlIGBkaXNwb3NlKClgIGlzIGEgbm8tb3Agb24gdGhlIGJvcnJvd2VkIGFzc2VtYmx5LCB5b3UgY2FuIG9taXQgaXRcbiAqIHdpdGhvdXQgY2hhbmdpbmcgYmVoYXZpb3IsIGJ1dCB0aGF0IHdvdWxkIHR1cm4gaW50byBhIGxlYWsgaWYgd2UgZXZlciBpbnRyb2R1Y2VkXG4gKiByZWZlcmVuY2UgY291bnRpbmcuIEZhaWxpbmcgdG8gZGlzcG9zZSB0aGUgcmVzdWx0IGlmIGEgYHByb2R1Y2UoKWAgY2FsbCBvZiBhXG4gKiBgQ2FjaGVkQ2xvdWRBc3NlbWJseWAgaXMgY29uc2lkZXJlZCBhIGJ1Zy5cbiAqL1xuZXhwb3J0IGNsYXNzIENhY2hlZENsb3VkQXNzZW1ibHkgaW1wbGVtZW50cyBJQ2xvdWRBc3NlbWJseVNvdXJjZSwgSVJlYWRhYmxlQ2xvdWRBc3NlbWJseSB7XG4gIHByaXZhdGUgYXNtOiBJUmVhZGFibGVDbG91ZEFzc2VtYmx5O1xuXG4gIHB1YmxpYyBjb25zdHJ1Y3Rvcihhc206IElSZWFkYWJsZUNsb3VkQXNzZW1ibHkpIHtcbiAgICB0aGlzLmFzbSA9IGFzbTtcbiAgfVxuXG4gIHB1YmxpYyBnZXQgY2xvdWRBc3NlbWJseSgpOiBjeGFwaS5DbG91ZEFzc2VtYmx5IHtcbiAgICByZXR1cm4gdGhpcy5hc20uY2xvdWRBc3NlbWJseTtcbiAgfVxuXG4gIHB1YmxpYyBhc3luYyBwcm9kdWNlKCk6IFByb21pc2U8SVJlYWRhYmxlQ2xvdWRBc3NlbWJseT4ge1xuICAgIHJldHVybiBuZXcgQm9ycm93ZWRBc3NlbWJseSh0aGlzLmFzbS5jbG91ZEFzc2VtYmx5KTtcbiAgfVxuXG4gIHB1YmxpYyBfdW5sb2NrKCkge1xuICAgIHJldHVybiB0aGlzLmFzbS5fdW5sb2NrKCk7XG4gIH1cblxuICBwdWJsaWMgZGlzcG9zZSgpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICByZXR1cm4gdGhpcy5hc20uZGlzcG9zZSgpO1xuICB9XG5cbiAgcHVibGljIFtTeW1ib2wuYXN5bmNEaXNwb3NlXSgpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICByZXR1cm4gdGhpcy5kaXNwb3NlKCk7XG4gIH1cbn1cbiJdfQ==