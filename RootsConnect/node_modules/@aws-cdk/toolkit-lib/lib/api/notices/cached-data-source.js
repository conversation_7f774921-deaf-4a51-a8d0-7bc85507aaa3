"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CachedDataSource = void 0;
const fs = require("fs-extra");
const toolkit_error_1 = require("../../toolkit/toolkit-error");
const TIME_TO_LIVE_SUCCESS = 60 * 60 * 1000; // 1 hour
const TIME_TO_LIVE_ERROR = 1 * 60 * 1000; // 1 minute
class CachedDataSource {
    ioHelper;
    fileName;
    dataSource;
    skipCache;
    constructor(ioHelper, fileName, dataSource, skipCache) {
        this.ioHelper = ioHelper;
        this.fileName = fileName;
        this.dataSource = dataSource;
        this.skipCache = skipCache;
    }
    async fetch() {
        const cachedData = await this.load();
        const data = cachedData.notices;
        const expiration = cachedData.expiration ?? 0;
        if (Date.now() > expiration || this.skipCache) {
            let updatedData = cachedData;
            try {
                updatedData = await this.fetchInner();
            }
            catch (e) {
                await this.ioHelper.defaults.debug(`Could not refresh notices: ${e}`);
                updatedData = {
                    expiration: Date.now() + TIME_TO_LIVE_ERROR,
                    notices: [],
                };
                throw toolkit_error_1.ToolkitError.withCause('Failed to load CDK notices. Please try again later.', e);
            }
            finally {
                await this.save(updatedData);
            }
            return updatedData.notices;
        }
        else {
            await this.ioHelper.defaults.debug(`Reading cached notices from ${this.fileName}`);
            return data;
        }
    }
    async fetchInner() {
        return {
            expiration: Date.now() + TIME_TO_LIVE_SUCCESS,
            notices: await this.dataSource.fetch(),
        };
    }
    async load() {
        const defaultValue = {
            expiration: 0,
            notices: [],
        };
        try {
            return fs.existsSync(this.fileName)
                ? await fs.readJSON(this.fileName)
                : defaultValue;
        }
        catch (e) {
            await this.ioHelper.defaults.debug(`Failed to load notices from cache: ${e}`);
            return defaultValue;
        }
    }
    async save(cached) {
        try {
            await fs.ensureFile(this.fileName);
            await fs.writeJSON(this.fileName, cached);
        }
        catch (e) {
            await this.ioHelper.defaults.debug(`Failed to store notices in the cache: ${e}`);
        }
    }
}
exports.CachedDataSource = CachedDataSource;
//# sourceMappingURL=data:application/json;base64,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