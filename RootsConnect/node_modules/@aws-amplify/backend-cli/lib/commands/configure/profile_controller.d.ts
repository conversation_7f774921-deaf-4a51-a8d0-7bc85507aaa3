import _fs from 'fs/promises';
/**
 * Options for the profile configuration.
 */
type ConfigProfileOptions = {
    profile: string;
    region: string;
};
/**
 * Options for the profile credential.
 */
type CredentialProfileOptions = {
    profile: string;
    accessKeyId: string;
    secretAccessKey: string;
};
/**
 * Options for the profile configuration and credential.
 */
export type ProfileOptions = ConfigProfileOptions & CredentialProfileOptions;
/**
 * Manages AWS profiles.
 */
export declare class ProfileController {
    private readonly fs;
    /**
     * constructor
     */
    constructor(fs?: typeof _fs);
    /**
     * Return true if the provided profile exists in the aws config and/or credential file.
     */
    profileExists: (profile: string) => Promise<boolean>;
    /**
     * Appends a profile to AWS config and credential files.
     */
    createOrAppendAWSFiles: (options: ProfileOptions) => Promise<void>;
    private createOrAppendAWSConfigFile;
    private createOrAppendAWSCredentialFile;
    private isFileEndsWithEOL;
}
export {};
//# sourceMappingURL=profile_controller.d.ts.map