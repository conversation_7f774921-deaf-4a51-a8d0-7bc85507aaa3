import { ArgumentsCamelCase, Argv, CommandModule } from 'yargs';
import { BackendIdentifierResolver } from '../../../backend-identifier/backend_identifier_resolver.js';
import { GenerateApiCodeAdapter } from './generate_api_code_adapter.js';
import { GenerateApiCodeFormat, GenerateApiCodeModelTarget, GenerateApiCodeStatementTarget, GenerateApiCodeTypeTarget } from '@aws-amplify/model-generator';
import { ArgumentsKebabCase } from '../../../kebab_case.js';
export type GenerateGraphqlClientCodeCommandOptions = ArgumentsKebabCase<GenerateGraphqlClientCodeCommandOptionsCamelCase>;
type GenerateGraphqlClientCodeCommandOptionsCamelCase = {
    stack: string | undefined;
    appId: string | undefined;
    branch: string | undefined;
    format: GenerateApiCodeFormat | undefined;
    modelTarget: GenerateApiCodeModelTarget | undefined;
    statementTarget: GenerateApiCodeStatementTarget | undefined;
    typeTarget: GenerateApiCodeTypeTarget | undefined;
    out: string | undefined;
    modelGenerateIndexRules: boolean | undefined;
    modelEmitAuthProvider: boolean | undefined;
    modelRespectPrimaryKeyAttributesOnConnectionField: boolean | undefined;
    modelGenerateModelsForLazyLoadAndCustomSelectionSet: boolean | undefined;
    modelAddTimestampFields: boolean | undefined;
    modelHandleListNullabilityTransparently: boolean | undefined;
    statementMaxDepth: number | undefined;
    statementTypenameIntrospection: boolean | undefined;
    typeMultipleSwiftFiles: boolean | undefined;
};
/**
 * Command that generates graphql client code.
 */
export declare class GenerateGraphqlClientCodeCommand implements CommandModule<object, GenerateGraphqlClientCodeCommandOptions> {
    private readonly generateApiCodeAdapter;
    private readonly backendIdentifierResolver;
    /**
     * @inheritDoc
     */
    readonly command: string;
    /**
     * @inheritDoc
     */
    readonly describe: string;
    /**
     * Creates graphql client code generation command.
     */
    constructor(generateApiCodeAdapter: GenerateApiCodeAdapter, backendIdentifierResolver: BackendIdentifierResolver);
    /**
     * @inheritDoc
     */
    handler: (args: ArgumentsCamelCase<GenerateGraphqlClientCodeCommandOptions>) => Promise<void>;
    /**
     * @inheritDoc
     */
    builder: (yargs: Argv) => Argv<GenerateGraphqlClientCodeCommandOptions>;
    /**
     * Produce the required input for graphql-codegen calls from the CLI input, applying sane defaults where applicable.
     * @param args CLI args provided by the customer
     * @returns the codegen options config
     */
    private getGraphqlCodegenFormatParams;
    /**
     * Produce the required input for modelgen calls from the CLI input, applying sane defaults where applicable.
     * @param args CLI args provided by the customer
     * @returns the modelgen options config
     */
    private getModelgenFormatParams;
    /**
     * Produce the introspection schema config shape.
     * @returns the introspection options config
     */
    private getIntrospectionFormatParams;
    private getOutDir;
    private formatParamBuilders;
}
export {};
//# sourceMappingURL=generate_graphql_client_code_command.d.ts.map