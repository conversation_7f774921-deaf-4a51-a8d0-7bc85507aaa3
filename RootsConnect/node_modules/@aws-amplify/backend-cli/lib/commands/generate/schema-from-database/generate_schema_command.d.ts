import { ArgumentsCamelCase, Argv, CommandModule } from 'yargs';
import { BackendIdentifierResolver } from '../../../backend-identifier/backend_identifier_resolver.js';
import { ArgumentsKebabCase } from '../../../kebab_case.js';
import { SecretClient } from '@aws-amplify/backend-secret';
import { SchemaGenerator } from '@aws-amplify/schema-generator';
export type GenerateSchemaCommandOptions = ArgumentsKebabCase<GenerateSchemaCommandOptionsCamelCase>;
type GenerateSchemaCommandOptionsCamelCase = {
    stack: string | undefined;
    appId: string | undefined;
    branch: string | undefined;
    out: string | undefined;
    connectionUriSecret: string | undefined;
    sslCertSecret: string | undefined;
};
/**
 * Command that generates typescript data schema from sql schema.
 */
export declare class GenerateSchemaCommand implements CommandModule<object, GenerateSchemaCommandOptions> {
    private readonly backendIdentifierResolver;
    private readonly secretClient;
    private readonly schemaGenerator;
    /**
     * @inheritDoc
     */
    readonly command: string;
    /**
     * @inheritDoc
     */
    readonly describe: string;
    /**
     * Creates typescript data schema generation command.
     */
    constructor(backendIdentifierResolver: BackendIdentifierResolver, secretClient: SecretClient, schemaGenerator: SchemaGenerator);
    /**
     * @inheritDoc
     */
    handler: (args: ArgumentsCamelCase<GenerateSchemaCommandOptions>) => Promise<void>;
    /**
     * @inheritDoc
     */
    builder: (yargs: Argv) => Argv<GenerateSchemaCommandOptions>;
}
export {};
//# sourceMappingURL=generate_schema_command.d.ts.map