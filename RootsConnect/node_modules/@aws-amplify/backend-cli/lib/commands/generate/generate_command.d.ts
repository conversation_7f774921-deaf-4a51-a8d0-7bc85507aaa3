import { Argv, CommandModule } from 'yargs';
import { GenerateOutputsCommand } from './outputs/generate_outputs_command.js';
import { GenerateFormsCommand } from './forms/generate_forms_command.js';
import { GenerateGraphqlClientCodeCommand } from './graphql-client-code/generate_graphql_client_code_command.js';
import { CommandMiddleware } from '../../command_middleware.js';
import { GenerateSchemaCommand } from './schema-from-database/generate_schema_command.js';
/**
 * An entry point for generate command.
 */
export declare class GenerateCommand implements CommandModule {
    private readonly generateOutputsCommand;
    private readonly generateFormsCommand;
    private readonly generateGraphqlClientCodeCommand;
    private readonly generateSchemaCommand;
    private readonly commandMiddleware;
    /**
     * @inheritDoc
     */
    readonly command: string;
    /**
     * @inheritDoc
     */
    readonly describe: string;
    /**
     * Creates top level entry point for generate command.
     */
    constructor(generateOutputsCommand: GenerateOutputsCommand, generateFormsCommand: GenerateFormsCommand, generateGraphqlClientCodeCommand: GenerateGraphqlClientCodeCommand, generateSchemaCommand: GenerateSchemaCommand, commandMiddleware: CommandMiddleware);
    /**
     * @inheritDoc
     */
    handler: () => void | Promise<void>;
    builder: (yargs: Argv) => Argv;
}
//# sourceMappingURL=generate_command.d.ts.map