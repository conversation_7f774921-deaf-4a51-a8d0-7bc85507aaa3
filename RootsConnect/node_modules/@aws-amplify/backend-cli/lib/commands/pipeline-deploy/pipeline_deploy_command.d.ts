import _isCI from 'is-ci';
import { ArgumentsCamelCase, Argv, CommandModule } from 'yargs';
import { BackendDeployer } from '@aws-amplify/backend-deployer';
import { ClientConfigGeneratorAdapter } from '../../client-config/client_config_generator_adapter.js';
import { ArgumentsKebabCase } from '../../kebab_case.js';
import { ClientConfigFormat } from '@aws-amplify/client-config';
export type PipelineDeployCommandOptions = ArgumentsKebabCase<PipelineDeployCommandOptionsCamelCase>;
type PipelineDeployCommandOptionsCamelCase = {
    branch: string;
    appId: string;
    outputsFormat: ClientConfigFormat | undefined;
    outputsVersion: string;
    outputsOutDir?: string;
};
/**
 * An entry point for deploy command.
 */
export declare class PipelineDeployCommand implements CommandModule<object, PipelineDeployCommandOptions> {
    private readonly clientConfigGenerator;
    private readonly backendDeployer;
    private readonly isCiEnvironment;
    /**
     * @inheritDoc
     */
    readonly command: string;
    /**
     * @inheritDoc
     */
    readonly describe: string;
    /**
     * Creates top level entry point for deploy command.
     */
    constructor(clientConfigGenerator: ClientConfigGeneratorAdapter, backendDeployer: BackendDeployer, isCiEnvironment?: typeof _isCI);
    /**
     * @inheritDoc
     */
    handler: (args: ArgumentsCamelCase<PipelineDeployCommandOptions>) => Promise<void>;
    builder: (yargs: Argv) => Argv<PipelineDeployCommandOptions>;
}
export {};
//# sourceMappingURL=pipeline_deploy_command.d.ts.map