import { CommandModule } from 'yargs';
import { SandboxCommandOptionsKebabCase } from './sandbox_command.js';
import { NoticesRenderer } from '../../notices/notices_renderer.js';
/**
 * Creates wired sandbox command.
 */
export declare const createSandboxCommand: (noticesRenderer: NoticesRenderer) => CommandModule<object, SandboxCommandOptionsKebabCase>;
//# sourceMappingURL=sandbox_command_factory.d.ts.map