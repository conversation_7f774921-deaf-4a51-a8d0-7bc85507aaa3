import { Argv, CommandModule } from 'yargs';
import { NoticesController } from '../../notices/notices_controller.js';
import { NoticesPrinter } from '../../notices/notices_printer.js';
import { ArgumentsKebabCase } from '../../kebab_case.js';
type NoticesListCommandOptionsKebabCase = ArgumentsKebabCase<{
    all?: boolean;
}>;
/**
 * Notices list command.
 */
export declare class NoticesListCommand implements CommandModule<object, NoticesListCommandOptionsKebabCase> {
    private readonly noticesController;
    private readonly noticesPrinter;
    /**
     * @inheritDoc
     */
    readonly command: string;
    /**
     * @inheritDoc
     */
    readonly describe: string;
    /**
     * Creates notices list command
     */
    constructor(noticesController: NoticesController, noticesPrinter: NoticesPrinter);
    /**
     * @inheritDoc
     */
    handler: (args: NoticesListCommandOptionsKebabCase) => Promise<void>;
    /**
     * @inheritDoc
     */
    builder: (yargs: Argv) => Argv;
}
export {};
//# sourceMappingURL=notices_list_command.d.ts.map