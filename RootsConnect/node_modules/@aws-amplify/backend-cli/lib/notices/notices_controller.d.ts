import { Notice } from '@aws-amplify/cli-core';
import { NoticesManifestFetcher } from './notices_manifest_fetcher.js';
import { NamespaceResolver } from '../backend-identifier/local_namespace_resolver.js';
import { NoticePredicatesEvaluator } from './notice_predicates_evaluator.js';
import { PackageManagerController } from '@aws-amplify/plugin-types';
import { NoticesRendererParams } from './notices_renderer.js';
/**
 * A notices controller.
 */
export declare class NoticesController {
    private readonly packageManagerController;
    private readonly noticesAcknowledgementFile;
    private readonly noticesMetadataFile;
    private readonly namespaceResolver;
    private readonly noticesManifestFetcher;
    private readonly noticePredicatesEvaluator;
    /**
     * Creates notices controller.
     */
    constructor(packageManagerController: PackageManagerController, noticesAcknowledgementFile?: import("@aws-amplify/platform-core").TypedConfigurationFile<{
        projectAcknowledgements: {
            projectName: string;
            noticeId: string;
            acknowledgedAt: number;
        }[];
    }>, noticesMetadataFile?: import("@aws-amplify/platform-core").TypedConfigurationFile<{
        printTimes: {
            projectName: string;
            noticeId: string;
            shownAt: number;
        }[];
        manifestCache: {
            noticesManifest: {
                notices: {
                    details: string;
                    id: string;
                    title: string;
                    predicates: ({
                        type: "packageVersion";
                        packageName: string;
                        versionRange: string;
                    } | {
                        type: "nodeVersion";
                        versionRange: string;
                    } | {
                        type: "osFamily";
                        osFamily: "linux" | "windows" | "macos";
                    } | {
                        type: "backendComponent";
                        backendComponent: "function" | "data" | "auth" | "storage" | "ai";
                    } | {
                        command: "sandbox" | "pipeline-deploy" | "generate" | "configure";
                        type: "command";
                    } | {
                        type: "errorMessage";
                        errorMessage: string;
                    })[];
                    link?: string | undefined;
                    frequency?: "once" | "command" | "deployment" | "daily" | undefined;
                    validFrom?: number | undefined;
                    validTo?: number | undefined;
                }[];
            };
            refreshedAt: number;
        };
    }>, namespaceResolver?: NamespaceResolver, noticesManifestFetcher?: NoticesManifestFetcher, noticePredicatesEvaluator?: NoticePredicatesEvaluator);
    getApplicableNotices: (params: {
        includeAcknowledged?: boolean;
    } & NoticesRendererParams) => Promise<Array<Notice>>;
    acknowledge: (noticeId: string) => Promise<void>;
    recordPrintingTimes: (notices: Array<Notice>) => Promise<void>;
    private filterAcknowledgedNotices;
    private applyPredicates;
    private applyFrequency;
    private applyValidityPeriod;
}
//# sourceMappingURL=notices_controller.d.ts.map