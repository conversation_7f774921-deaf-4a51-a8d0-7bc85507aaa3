## API Report File for "@aws-amplify/appsync-modelgen-plugin"

> Do not edit this file. It is a report generated by [API Extractor](https://api-extractor.com/).

```ts

import { CodegenPlugin } from '@graphql-codegen/plugin-helpers';
import { PluginFunction } from '@graphql-codegen/plugin-helpers';
import { RawConfig } from '@graphql-codegen/visitor-plugin-common';
import { RawDocumentsConfig } from '@graphql-codegen/visitor-plugin-common';
import { Types } from '@graphql-codegen/plugin-helpers';

// @public (undocumented)
export const addToSchema: (config: AppSyncModelPluginConfig) => string;

// @public (undocumented)
export type AppSyncModelCodeGenPresetConfig = {
    overrideOutputDir: string | null;
    target: Target;
    isDataStoreEnabled?: boolean;
};

// @public (undocumented)
export interface AppSyncModelPluginConfig extends RawDocumentsConfig {
    // (undocumented)
    directives?: string;
}

// @public (undocumented)
export type Argument = {
    name: string;
    type: InputFieldType;
    isArray: boolean;
    isRequired: boolean;
    isArrayNullable?: boolean;
};

// @public (undocumented)
export type Arguments = Record<string, Argument>;

// @public (undocumented)
export type AssociationBaseType = {
    connectionType: CodeGenConnectionType;
};

// @public (undocumented)
export type AssociationBelongsTo = AssociationBaseType & {
    connectionType: CodeGenConnectionType.BELONGS_TO;
    targetNames: string[];
};

// @public (undocumented)
export type AssociationHasMany = AssociationBaseType & {
    connectionType: CodeGenConnectionType.HAS_MANY;
    associatedWith: string[];
};

// @public (undocumented)
export type AssociationHasOne = AssociationBaseType & {
    connectionType: CodeGenConnectionType.HAS_ONE;
    associatedWith: string[];
    targetNames: string[];
};

// @public (undocumented)
export type AssociationType = AssociationHasMany | AssociationHasOne | AssociationBelongsTo;

// @public (undocumented)
export enum CodeGenConnectionType {
    // (undocumented)
    BELONGS_TO = "BELONGS_TO",
    // (undocumented)
    HAS_MANY = "HAS_MANY",
    // (undocumented)
    HAS_ONE = "HAS_ONE"
}

// @public (undocumented)
export type Field = {
    name: string;
    type: FieldType;
    isArray: boolean;
    isRequired: boolean;
    isReadOnly?: boolean;
    isArrayNullable?: boolean;
    attributes?: FieldAttribute[];
    association?: AssociationType;
    arguments?: Arguments;
};

// @public (undocumented)
export type FieldAttribute = ModelAttribute;

// @public (undocumented)
export type Fields = Record<string, Field>;

// @public (undocumented)
export type FieldType = ScalarType | {
    enum: string;
} | {
    model: string;
} | {
    nonModel: string;
};

// @public (undocumented)
export type Input = {
    name: string;
    attributes: Arguments;
};

// @public (undocumented)
export type InputFieldType = ScalarType | {
    enum: string;
} | {
    input: string;
};

// @public (undocumented)
export type ModelAttribute = {
    type: string;
    properties?: {
        [key: string]: any;
    };
};

// @public (undocumented)
export type ModelIntrospectionSchema = {
    version: 1;
    models: SchemaModels;
    nonModels: SchemaNonModels;
    enums: SchemaEnums;
    queries?: SchemaQueries;
    mutations?: SchemaMutations;
    subscriptions?: SchemaSubscriptions;
    inputs?: SchemaInputs;
    generations?: SchemaGenerations;
    conversations?: SchemaConversationRoutes;
};

// Warning: (ae-forgotten-export) The symbol "RawAppSyncModelConfig" needs to be exported by the entry point index.d.ts
//
// @public (undocumented)
export const plugin: PluginFunction<RawAppSyncModelConfig>;

// @public (undocumented)
export const pluginSync: SyncTypes.PluginFunction<RawAppSyncModelConfig>;

// @public (undocumented)
export const preset: Types.OutputPreset<AppSyncModelCodeGenPresetConfig>;

// @public (undocumented)
export const presetSync: SyncTypes.OutputPreset<AppSyncModelCodeGenPresetConfig>;

// @public (undocumented)
export type PrimaryKeyInfo = {
    isCustomPrimaryKey: boolean;
    primaryKeyFieldName: string;
    sortKeyFieldNames: string[];
};

// @public (undocumented)
export type ScalarType = 'ID' | 'String' | 'Int' | 'Float' | 'AWSDate' | 'AWSTime' | 'AWSDateTime' | 'AWSTimestamp' | 'AWSEmail' | 'AWSURL' | 'AWSIPAddress' | 'Boolean' | 'AWSJSON' | 'AWSPhone';

// @public (undocumented)
export type SchemaConversation = {
    modelName: string;
};

// @public (undocumented)
export type SchemaConversationMessage = {
    modelName: string;
    subscribe: SchemaSubscription;
    send: SchemaMutation;
};

// @public (undocumented)
export type SchemaConversationRoute = {
    name: string;
    models: SchemaModels;
    nonModels: SchemaNonModels;
    enums: SchemaEnums;
    conversation: SchemaConversation;
    message: SchemaConversationMessage;
};

// @public (undocumented)
export type SchemaConversationRoutes = Record<string, SchemaConversationRoute>;

// @public (undocumented)
export type SchemaEnum = {
    name: string;
    values: string[];
};

// @public (undocumented)
export type SchemaEnums = Record<string, SchemaEnum>;

// @public (undocumented)
export type SchemaGenerations = SchemaQueries;

// @public (undocumented)
export type SchemaInputs = Record<string, Input>;

// @public (undocumented)
export type SchemaModel = {
    name: string;
    attributes?: ModelAttribute[];
    fields: Fields;
    pluralName: string;
    syncable?: boolean;
    primaryKeyInfo: PrimaryKeyInfo;
};

// @public (undocumented)
export type SchemaModels = Record<string, SchemaModel>;

// @public (undocumented)
export type SchemaMutation = SchemaQuery;

// @public (undocumented)
export type SchemaMutations = Record<string, SchemaMutation>;

// @public (undocumented)
export type SchemaNonModel = {
    name: string;
    fields: Fields;
};

// @public (undocumented)
export type SchemaNonModels = Record<string, SchemaNonModel>;

// @public (undocumented)
export type SchemaQueries = Record<string, SchemaQuery>;

// @public (undocumented)
export type SchemaQuery = Pick<Field, 'name' | 'type' | 'isArray' | 'isRequired' | 'isArrayNullable' | 'arguments'>;

// @public (undocumented)
export type SchemaSubscription = SchemaQuery;

// @public (undocumented)
export type SchemaSubscriptions = Record<string, SchemaSubscription>;

// @public (undocumented)
export namespace SyncTypes {
    // (undocumented)
    export type CodegenPlugin<T = any> = Omit<CodegenPlugin<T>, 'plugin'> & {
        plugin: PluginFunction<T>;
    };
    // (undocumented)
    export type ConfiguredPlugin = Types.ConfiguredPlugin;
    // (undocumented)
    export type DocumentFile = Types.DocumentFile;
    // Warning: (ae-forgotten-export) The symbol "SyncCache" needs to be exported by the entry point index.d.ts
    // Warning: (ae-forgotten-export) The symbol "SyncPluginMap" needs to be exported by the entry point index.d.ts
    //
    // (undocumented)
    export type GenerateOptions = SyncCache<SyncPluginMap<Types.GenerateOptions>>;
    // (undocumented)
    export type OutputPreset<TPresetConfig = any> = {
        buildGeneratesSection: (options: PresetFnArgs<TPresetConfig>) => GenerateOptions[];
    };
    // (undocumented)
    export type PluginConfig = Types.PluginConfig;
    // (undocumented)
    export type PluginFunction<T> = (...args: Parameters<PluginFunction<T>>) => Awaited<ReturnType<PluginFunction<T>>>;
    // (undocumented)
    export type PluginOutput = Types.PluginOutput;
    // (undocumented)
    export type PresetFnArgs<Config = any, PluginConfig = {
        [key: string]: any;
    }> = SyncCache<SyncPluginMap<Types.PresetFnArgs<Config, PluginConfig>>>;
    // (undocumented)
    export type SkipDocumentsValidationOptions = Types.SkipDocumentsValidationOptions;
}

// @public (undocumented)
export type Target = 'java' | 'swift' | 'javascript' | 'typescript' | 'dart' | 'introspection';

// (No @packageDocumentation comment for this package)

```
