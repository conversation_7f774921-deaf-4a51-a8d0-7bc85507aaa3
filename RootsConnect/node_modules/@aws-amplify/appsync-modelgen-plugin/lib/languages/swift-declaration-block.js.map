{"version": 3, "file": "swift-declaration-block.js", "sourceRoot": "", "sources": ["../../src/languages/swift-declaration-block.ts"], "names": [], "mappings": ";;;AAAA,kFAAiF;AAGjF,SAAS,iBAAiB,CAAC,IAAS;IAClC,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,CAAC;AACzE,CAAC;AACD,MAAM,gBAAgB,GAAG;IACvB,UAAU;IACV,QAAQ;IACR,YAAY;IACZ,QAAQ;IACR,IAAI;IACJ,eAAe;IACf,OAAO;IACP,MAAM;IACN,OAAO;IACP,UAAU;IACV,aAAa;IACb,SAAS;IACT,QAAQ;IACR,QAAQ;IACR,IAAI;IACJ,SAAS;IACT,aAAa;IACb,MAAM;IACN,MAAM;IACN,WAAW;IACX,aAAa;IACb,OAAO;IACP,OAAO;IACP,KAAK;IACL,MAAM;IACN,KAAK;IACL,IAAI;IACJ,QAAQ;IACR,IAAI;IACJ,OAAO;IACP,MAAM;IACN,OAAO;IACP,UAAU;IACV,IAAI;IACJ,MAAM;IACN,MAAM;IACN,KAAK;IACL,UAAU;IACV,KAAK;IACL,MAAM;IACN,aAAa;IACb,UAAU;IACV,UAAU;IACV,UAAU;IACV,SAAS;IACT,YAAY;IACZ,QAAQ;IACR,SAAS;IACT,UAAU;IACV,UAAU;IACV,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,OAAO;IACP,MAAM;IACN,MAAM;IACN,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,WAAW;IACX,OAAO;IACP,QAAQ;IACR,MAAM;IACN,WAAW;IACX,MAAM;IACN,SAAS;IACT,SAAS;IACT,KAAK;IACL,MAAM;IACN,OAAO;IACP,OAAO;IACP,SAAS;CACV,CAAC;AAEF,SAAgB,cAAc,CAAC,OAAe;IAC5C,IAAI,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;QACtC,OAAO,KAAK,OAAO,IAAI,CAAC;KACzB;IACD,OAAO,OAAO,CAAC;AACjB,CAAC;AALD,wCAKC;AAED,SAAS,gBAAgB,CAAC,OAAiC,EAAE,WAAW,GAAG,CAAC;IAC1E,IAAI,iBAAiB,CAAC,OAAO,CAAC,EAAE;QAC9B,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;KACzB;IAED,IAAI,CAAC,OAAO,IAAI,OAAO,KAAK,EAAE,EAAE;QAC9B,OAAO,EAAE,CAAC;KACX;IAED,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAElC,OAAO,KAAK;SACT,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;QACnB,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,KAAK,KAAK,GAAG,CAAC,CAAC;QAC1C,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,CAAC;QAE5B,IAAI,OAAO,IAAI,MAAM,EAAE;YACrB,OAAO,IAAA,8BAAM,EAAC,MAAM,OAAO,KAAK,EAAE,WAAW,CAAC,CAAC;SAChD;QACD,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACrC,OAAO,IAAA,8BAAM,EAAC,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,WAAW,CAAC,CAAC;IAC3F,CAAC,CAAC;SACD,IAAI,CAAC,IAAI,CAAC,CAAC;AAChB,CAAC;AAED,IAAY,QAGX;AAHD,WAAY,QAAQ;IAClB,2BAAe,CAAA;IACf,yBAAa,CAAA;AACf,CAAC,EAHW,QAAQ,GAAR,gBAAQ,KAAR,gBAAQ,QAGnB;AA4CD,MAAa,qBAAqB;IAAlC;QACE,UAAK,GAAW,EAAE,CAAC;QACnB,eAAU,GAAW,EAAE,CAAC;QACxB,UAAK,GAAS,QAAQ,CAAC;QACvB,eAAU,GAAa,EAAE,CAAC;QAC1B,YAAO,GAAW,SAAS,CAAC;QAC5B,gBAAW,GAAqB,EAAE,CAAC;QACnC,aAAQ,GAAmB,EAAE,CAAC;QAC9B,aAAQ,GAAW,EAAE,CAAC;QACtB,WAAM,GAAa,EAAE,CAAC;QACtB,gBAAW,GAA+B,EAAE,CAAC;QAC7C,WAAM,GAAoB,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;IAkQ7C,CAAC;IAhQC,MAAM,CAAC,MAAc;QACnB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QAEtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,UAAmB,IAAI;QAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,OAAO,CAAC;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,WAAW,CAAC,OAAwC;QAClD,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;SAC9C;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,QAAQ,CAAC,IAAuB;QAC9B,IAAI,CAAC,KAAK,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAE,IAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;QAExE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,aAAa,CAAC,SAAiB;QAC7B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,SAAS,CAAC,KAAa;QACrB,IAAI,CAAC,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC;QAEtB,OAAO,IAAI,CAAC;IACd,CAAC;IACD,WAAW,CAAC,KAAa;QACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QACxB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,IAAU;QACf,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,WAAW,CACT,IAAY,EACZ,IAAY,EACZ,KAAc,EACd,SAAiB,QAAQ,EACzB,QAAuB,EAAE,EACzB,OAAgB,EAChB,MAAe,EACf,MAAe;QAEf,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YACpB,IAAI;YACJ,IAAI;YACJ,KAAK;YACL,MAAM;YACN,KAAK,EAAE;gBACL,QAAQ,EAAE,KAAK;gBACf,GAAG,KAAK;aACT;YACD,OAAO;YACP,MAAM;YACN,MAAM;SACP,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;IAED,aAAa,CAAC,SAAmB;QAC/B,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,YAAY,CAAC,IAAY,EAAE,KAAc;QACvC,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,0CAA0C,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;SACzE;QAED,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI,IAAI,CAAC;QACvC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,cAAc,CACZ,IAAY,EACZ,UAAyB,EACzB,cAAsB,EACtB,OAAyB,EAAE,EAC3B,SAAiB,QAAQ,EACzB,QAAqB,EAAE,EACvB,UAAkB,EAAE;QAEpB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACjB,IAAI;YACJ,UAAU;YACV,cAAc,EAAE,cAAc;YAC9B,IAAI;YACJ,MAAM;YACN,KAAK;YACL,OAAO,EAAE,gBAAgB,CAAC,OAAO,CAAC;SACnC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC;IACd,CAAC;IACD,IAAW,MAAM;QACf,IAAI,IAAI,CAAC,KAAK,KAAK,MAAM,EAAE;YACzB,OAAO,IAAI,CAAC,eAAe,EAAE,CAAC;SAC/B;QACD,OAAO,IAAI,CAAC,4BAA4B,EAAE,CAAC;IAC7C,CAAC;IAEO,eAAe;QACrB,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CACxC;YACE,IAAI,CAAC,QAAQ;YACb,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,KAAK;YACV,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE;YACjG,GAAG;SACJ,EACD,KAAK,EACL,GAAG,CACJ,CAAC;QACF,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CACnC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,CACnD,CAAC,MAAM,EAAE,cAAc,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CACrF,EACD,KAAK,CACN,CAAC;QACF,MAAM,eAAe,GAAG,GAAG,CAAC;QAC5B,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,eAAe,EAAE,IAAA,uCAAe,EAAC,UAAU,CAAC,EAAE,eAAe,CAAC,EAAE,KAAK,CAAC,CAAC;IACpG,CAAC;IAEO,4BAA4B;QAClC,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QAElH,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAChC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YACzB,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,kBAAkB,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAA,uCAAe,EAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;YACxG,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CACrC;gBACE,MAAM,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM;gBAChD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;gBACnC,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE;gBACvC,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM;gBACtD,GAAG,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,kBAAkB,EAAE;gBACrD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;gBACnC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,MAAM,MAAM,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE;gBAClD,GAAG;aACJ,EACD,KAAK,EACL,GAAG,CACJ,CAAC;YACF,MAAM,YAAY,GAAG,GAAG,CAAC;YACzB,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,YAAY,EAAE,IAAA,uCAAe,EAAC,MAAM,CAAC,cAAc,CAAC,EAAE,YAAY,CAAC,EAAE,KAAK,CAAC,CAAC;QACzH,CAAC,CAAC,EACF,KAAK,CACN,CAAC;QAEF,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CACxC;YACE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;YAChC,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,KAAK;YACV,GAAG,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAC7F,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,EAClD,EAAE;YACF,GAAG;SACJ,EACD,KAAK,EACL,GAAG,CACJ,CAAC;QACF,MAAM,eAAe,GAAG,IAAA,uCAAe,EAAC,IAAI,CAAC,aAAa,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC;QACnG,MAAM,eAAe,GAAG,GAAG,CAAC;QAC5B,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,QAAQ,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,CAAC,EAAE,KAAK,CAAC,CAAC;IACvG,CAAC;IAEO,eAAe,CAAC,IAAsB;QAC5C,MAAM,GAAG,GAAa,IAAI,CAAC,MAAM,CAAC,CAAC,GAAa,EAAE,GAAG,EAAE,EAAE;YACvD,MAAM,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACjF,IAAI,GAAG,CAAC,KAAK,CAAC,kCAAkC,EAAE;gBAChD,MAAM,aAAa,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC;gBACvF,MAAM,GAAG,GAAkB,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;gBAC1G,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;aAC7G;iBAAM;gBACL,MAAM,GAAG,GAAkB,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC;gBAC/G,GAAG,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;aAClH;YACD,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAAE,CAAC,CAAC;QAEP,OAAO,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,IAAA,uCAAe,EAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3E,CAAC;IAEO,qBAAqB,CAAC,IAAoB;QAChD,IAAI,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;QAC9E,IAAI,YAAY,GAAG,gBAAgB,CAAC,CAAC,CAAC,KAAK,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAEpG,IAAI,IAAI,CAAC,KAAK,CAAC,kCAAkC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACtE,YAAY,GAAG,gBAAgB,CAAC,CAAC,CAAC,KAAK,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;SACvG;QAED,IAAI,SAAS,GAAa;YACxB,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM;YAC5C,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE;YACjC,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK;YAC1E,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,YAAY,EAAE;SAC9C,CAAC;QAEF,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,IAAA,uCAAe,EAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;QAChF,MAAM,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,IAAA,uCAAe,EAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC;QAChF,IAAI,eAAe,GAAG,EAAE,CAAC;QACzB,IAAI,SAAS,EAAE;YACb,eAAe,GAAG,IAAI,CAAC,aAAa,CAClC,CAAC,GAAG,EAAE,IAAA,uCAAe,EAAC,QAAQ,SAAS,EAAE,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,IAAA,uCAAe,EAAC,QAAQ,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,EACvG,KAAK,CACN,CAAC;SACH;aAAM,IAAI,SAAS,EAAE;YACpB,eAAe,GAAG,IAAA,uCAAe,EAAC,SAAS,CAAC,CAAC;SAC9C;QACD,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QAChC,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACpB,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC5B;QACD,MAAM,eAAe,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAC7D,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,eAAe,CAAC,EAAE,KAAK,CAAC,CAAC;IAC/G,CAAC;IAEO,YAAY;QAClB,OAAO,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;IACxD,CAAC;IAED,aAAa,CAAC,QAAkB,EAAE,gBAAyB,IAAI,EAAE,UAAkB,IAAI;QACrF,OAAO,QAAQ;aACZ,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC;aAC5B,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,OAAO,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;aAC1D,IAAI,CAAC,OAAO,CAAC;aACb,IAAI,EAAE,CAAC;IACZ,CAAC;IAEO,WAAW,CAAC,eAA+B;QACjD,IAAI,cAAc,GAAG,GAAG,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;QAC/D,IAAI,eAAe,CAAC,KAAK,CAAC,kCAAkC,EAAE;YAC5D,cAAc,GAAG,eAAe,CAAC,KAAK,CAAC,QAAQ;gBAC7C,CAAC,CAAC,GAAG,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG;gBAC5C,CAAC,CAAC,GAAG,cAAc,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;SAC/C;QACD,IAAI,eAAe,CAAC,KAAK,CAAC,QAAQ,KAAK,QAAQ,CAAC,IAAI,EAAE;YACpD,OAAO,QAAQ,cAAc,GAAG,CAAC;SAClC;QACD,OAAO,IAAI,cAAc,GAAG,CAAC;IAC/B,CAAC;CACF;AA7QD,sDA6QC"}