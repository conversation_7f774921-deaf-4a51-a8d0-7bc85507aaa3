import { GraphQLEnumType, GraphQLInputObjectType } from 'graphql';
import { CompilerOptions } from '../compiler';
import * as t from '@babel/types';
export declare type ObjectProperty = {
    name: string;
    description?: string | null | undefined;
    annotation: t.FlowTypeAnnotation;
};
export interface FlowCompilerOptions extends CompilerOptions {
    useFlowExactObjects: boolean;
}
export declare class FlowGenerator {
    options: FlowCompilerOptions;
    typeAnnotationFromGraphQLType: Function;
    constructor(compilerOptions: FlowCompilerOptions);
    enumerationDeclaration(type: GraphQLEnumType): t.ExportNamedDeclaration;
    inputObjectDeclaration(inputObjectType: GraphQLInputObjectType): t.TypeAlias;
    objectTypeAnnotation(fields: ObjectProperty[], isInputObject?: boolean): t.ObjectTypeAnnotation;
    typeAliasObject(name: string, fields: ObjectProperty[]): t.<PERSON><PERSON>;
    typeAliasObjectUnion(name: string, members: ObjectProperty[][]): t.<PERSON>;
    typeAliasGenericUnion(name: string, members: t.FlowTypeAnnotation[]): t.TypeAlias;
    exportDeclaration(declaration: t.Declaration): t.ExportNamedDeclaration;
    annotationFromScopeStack(scope: string[]): t.GenericTypeAnnotation;
}
