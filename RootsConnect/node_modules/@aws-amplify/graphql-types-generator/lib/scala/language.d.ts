export function comment(generator: any, comment: any): void;
export function packageDeclaration(generator: any, pkg: any): void;
export function objectDeclaration(generator: any, { objectName, superclass, properties }: {
    objectName: any;
    superclass: any;
    properties: any;
}, closure: any): void;
export function caseClassDeclaration(generator: any, { caseClassName, description, superclass, params }: {
    caseClassName: any;
    description: any;
    superclass: any;
    params: any;
}, closure: any): void;
export function propertyDeclaration(generator: any, { propertyName, typeName, description }: {
    propertyName: any;
    typeName: any;
    description: any;
}, closure: any): void;
export function propertyDeclarations(generator: any, declarations: any): void;
export function escapeIdentifierIfNeeded(identifier: any): any;
