export function typeDeclaration(generator: any, { interfaceName, noBrackets }: {
    interfaceName: any;
    noBrackets: any;
}, closure: any): void;
export function propertyDeclaration(generator: any, { fieldName, type, propertyName, typeName, description, isArray, isNullable, isArrayElementNullable, fragmentSpreads, isOptional }: {
    fieldName: any;
    type: any;
    propertyName: any;
    typeName: any;
    description: any;
    isArray: any;
    isNullable: any;
    isArrayElementNullable: any;
    fragmentSpreads: any;
    isOptional: any;
}, closure: any, open?: string, close?: string): void;
export function propertySetsDeclaration(generator: any, property: any, propertySets: any, standalone?: boolean): void;
