export declare const s3WrapperCode = "\n\nextension S3Object: AWSS3ObjectProtocol {\n  public func getBucketName() -> String {\n      return bucket\n  }\n\n  public func getKeyName() -> String {\n      return key\n  }\n\n  public func getRegion() -> String {\n      return region\n  }\n}\n\nextension S3ObjectInput: AWSS3ObjectProtocol, AWSS3InputObjectProtocol {\n  public func getLocalSourceFileURL() -> URL? {\n      return URL(string: self.localUri)\n  }\n\n  public func getMimeType() -> String {\n      return self.mimeType\n  }\n\n  public func getBucketName() -> String {\n      return self.bucket\n  }\n\n  public func getKeyName() -> String {\n      return self.key\n  }\n\n  public func getRegion() -> String {\n      return self.region\n  }\n\n}\n\nimport AWSS3\nextension AWSS3PreSignedURLBuilder: AWSS3ObjectPresignedURLGenerator  {\n  public func getPresignedURL(s3Object: AWSS3ObjectProtocol) -> URL? {\n      let request = AWSS3GetPreSignedURLRequest()\n      request.bucket = s3Object.getBucketName()\n      request.key = s3Object.getKeyName()\n      var url : URL?\n      self.getPreSignedURL(request).continueWith { (task) -> Any? in\n          url = task.result as URL?\n          }.waitUntilFinished()\n      return url\n  }\n}\n\nextension AWSS3TransferUtility: AWSS3ObjectManager {\n\n  public func download(s3Object: AWSS3ObjectProtocol, toURL: URL, completion: @escaping ((Bool, Error?) -> Void)) {\n\n      let completionBlock: AWSS3TransferUtilityDownloadCompletionHandlerBlock = { task, url, data, error  -> Void in\n          if let _ = error {\n              completion(false, error)\n          } else {\n              completion(true, nil)\n          }\n      }\n      let _ = self.download(to: toURL, bucket: s3Object.getBucketName(), key: s3Object.getKeyName(), expression: nil, completionHandler: completionBlock)\n  }\n\n  public func upload(s3Object: AWSS3ObjectProtocol & AWSS3InputObjectProtocol, completion: @escaping ((_ success: Bool, _ error: Error?) -> Void)) {\n      let completionBlock : AWSS3TransferUtilityUploadCompletionHandlerBlock = { task, error  -> Void in\n          if let _ = error {\n              completion(false, error)\n          } else {\n              completion(true, nil)\n          }\n      }\n      let _ = self.uploadFile(s3Object.getLocalSourceFileURL()!, bucket: s3Object.getBucketName(), key: s3Object.getKeyName(), contentType: s3Object.getMimeType(), expression: nil, completionHandler: completionBlock).continueWith { (task) -> Any? in\n          if let err = task.error {\n              completion(false, err)\n          }\n          return nil\n      }\n  }\n}";
