import { GraphQLType, GraphQLScalarType, GraphQLInputField } from 'graphql';
import { Property, Struct } from './language';
import { CompilerOptions, SelectionSet, Field, FragmentSpread, Argument } from '../compiler';
import { Variant } from '../compiler/visitors/typeCase';
export declare class Helpers {
    options: CompilerOptions;
    constructor(options: CompilerOptions);
    typeNameFromGraphQLType(type: GraphQLType, unmodifiedTypeName?: string, isOptional?: boolean): string;
    typeNameForScalarType(type: GraphQLScalarType): string;
    fieldTypeEnum(type: GraphQLType, structName: string): string;
    enumCaseName(name: string): string;
    enumDotCaseName(name: string): string;
    operationClassName(name: string): string;
    structNameForPropertyName(propertyName: string): string;
    structNameForFragmentName(fragmentName: string): string;
    structNameForVariant(variant: SelectionSet): string;
    propertyFromField(field: Field, namespace?: string): Field & Property & Struct;
    propertyFromVariant(variant: Variant): Variant & Property & Struct;
    propertyFromFragmentSpread(fragmentSpread: FragmentSpread, isConditional: boolean): FragmentSpread & Property & Struct;
    propertyFromInputField(field: GraphQLInputField): {
        propertyName: string;
        typeName: string;
        isOptional: boolean;
        description: string | null;
        name: string;
    };
    propertiesForSelectionSet(selectionSet: SelectionSet, namespace?: string): (Field & Property)[] | undefined;
    dictionaryLiteralForFieldArguments(args: Argument[]): string;
    mapExpressionForType(type: GraphQLType, expression: (identifier: string) => string, identifier?: string): string;
}
