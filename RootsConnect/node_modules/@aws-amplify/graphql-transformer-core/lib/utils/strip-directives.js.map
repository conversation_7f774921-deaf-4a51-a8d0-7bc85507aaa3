{"version": 3, "file": "strip-directives.js", "sourceRoot": "", "sources": ["../../src/utils/strip-directives.ts"], "names": [], "mappings": ";;;AAAA,qCAaiB;AAEjB,SAAgB,eAAe,CAAC,GAAiB,EAAE,SAAmB,EAAE;IACtE,MAAM,WAAW,GAAG,EAAE,CAAC;IACvB,KAAK,MAAM,GAAG,IAAI,GAAG,CAAC,WAAW,EAAE;QACjC,QAAQ,GAAG,CAAC,IAAI,EAAE;YAChB,KAAK,cAAI,CAAC,sBAAsB;gBAC9B,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC7C,MAAM;YACR,KAAK,cAAI,CAAC,yBAAyB;gBACjC,WAAW,CAAC,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC,CAAC;gBAChD,MAAM;YACR,KAAK,cAAI,CAAC,qBAAqB;gBAC7B,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC5C,MAAM;YACR,KAAK,cAAI,CAAC,4BAA4B;gBACpC,WAAW,CAAC,IAAI,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAC,CAAC;gBAClD,MAAM;YACR,KAAK,cAAI,CAAC,oBAAoB;gBAC5B,WAAW,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC3C,MAAM;YACR,KAAK,cAAI,CAAC,sBAAsB;gBAC9B,WAAW,CAAC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC7C,MAAM;YACR,QAAQ;SAET;KACF;IAED,SAAS,QAAQ,CAAC,GAAkB;QAClC,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3D,CAAC;IAED,SAAS,qBAAqB,CAAC,IAA8B;;QAC3D,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;QACjF,OAAO;YACL,GAAG,IAAI;YACP,MAAM;YACN,UAAU,EAAE,MAAA,IAAI,CAAC,UAAU,0CAAE,MAAM,CAAC,QAAQ,CAAC;SAC9C,CAAC;IACJ,CAAC;IAED,SAAS,wBAAwB,CAAC,IAAiC;;QACjE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;QACjF,OAAO;YACL,GAAG,IAAI;YACP,MAAM;YACN,UAAU,EAAE,MAAA,IAAI,CAAC,UAAU,0CAAE,MAAM,CAAC,QAAQ,CAAC;SAC9C,CAAC;IACJ,CAAC;IAED,SAAS,oBAAoB,CAAC,IAAyB;;QACrD,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;QAC3F,OAAO;YACL,GAAG,IAAI;YACP,SAAS,EAAE,IAAI;YACf,UAAU,EAAE,MAAA,IAAI,CAAC,UAAU,0CAAE,MAAM,CAAC,QAAQ,CAAC;SAC9C,CAAC;IACJ,CAAC;IAED,SAAS,uBAAuB,CAAC,IAA8B;;QAC7D,OAAO;YACL,GAAG,IAAI;YACP,UAAU,EAAE,MAAA,IAAI,CAAC,UAAU,0CAAE,MAAM,CAAC,QAAQ,CAAC;SAC9C,CAAC;IACJ,CAAC;IAED,SAAS,oBAAoB,CAAC,IAA6B;;QACzD,OAAO;YACL,GAAG,IAAI;YACP,UAAU,EAAE,MAAA,IAAI,CAAC,UAAU,0CAAE,MAAM,CAAC,QAAQ,CAAC;SAC9C,CAAC;IACJ,CAAC;IAED,SAAS,qBAAqB,CAAC,IAA8B;;QAC3D,OAAO;YACL,GAAG,IAAI;YACP,UAAU,EAAE,MAAA,IAAI,CAAC,UAAU,0CAAE,MAAM,CAAC,QAAQ,CAAC;SAC9C,CAAC;IACJ,CAAC;IAED,SAAS,0BAA0B,CAAC,IAAmC;;QACrE,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;QACpF,OAAO;YACL,GAAG,IAAI;YACP,MAAM;YACN,UAAU,EAAE,MAAA,IAAI,CAAC,UAAU,0CAAE,MAAM,CAAC,QAAQ,CAAC;SAC9C,CAAC;IACJ,CAAC;IAED,SAAS,mBAAmB,CAAC,IAA4B;;QACvD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;QACrF,OAAO;YACL,GAAG,IAAI;YACP,MAAM;YACN,UAAU,EAAE,MAAA,IAAI,CAAC,UAAU,0CAAE,MAAM,CAAC,QAAQ,CAAC;SAC9C,CAAC;IACJ,CAAC;IAED,SAAS,wBAAwB,CAAC,IAA6B;;QAC7D,OAAO;YACL,GAAG,IAAI;YACP,UAAU,EAAE,MAAA,IAAI,CAAC,UAAU,0CAAE,MAAM,CAAC,QAAQ,CAAC;SAC9C,CAAC;IACJ,CAAC;IAED,OAAO;QACL,IAAI,EAAE,cAAI,CAAC,QAAQ;QACnB,WAAW;KACZ,CAAC;AACJ,CAAC;AA5GD,0CA4GC"}