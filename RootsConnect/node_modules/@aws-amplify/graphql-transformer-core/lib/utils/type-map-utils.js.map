{"version": 3, "file": "type-map-utils.js", "sourceRoot": "", "sources": ["../../src/utils/type-map-utils.ts"], "names": [], "mappings": ";;;AAAA,qCAciB;AAEjB,SAAgB,iBAAiB,CAAC,GAAW;IAC3C,IAAI,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;QACrB,OAAO,EAAE,CAAC;KACX;IACD,MAAM,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;IACpC,IAAI,UAAU,GAAoB,EAAE,CAAC;IACrC,KAAK,MAAM,GAAG,IAAI,GAAG,CAAC,WAAW,EAAE;QACjC,QAAQ,GAAG,CAAC,IAAI,EAAE;YAChB,KAAK,cAAI,CAAC,sBAAsB;gBAE9B,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC7D,MAAM;YACR,KAAK,cAAI,CAAC,yBAAyB;gBACjC,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAC,CAAC;gBAChE,MAAM;YACR,KAAK,cAAI,CAAC,qBAAqB;gBAC7B,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC5D,MAAM;YACR,KAAK,cAAI,CAAC,4BAA4B;gBACpC,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,4BAA4B,CAAC,GAAG,CAAC,CAAC,CAAC;gBAClE,MAAM;YACR,KAAK,cAAI,CAAC,oBAAoB;gBAC5B,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC3D,MAAM;YACR,KAAK,cAAI,CAAC,sBAAsB;gBAC9B,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC7D,MAAM;YACR,QAAQ;SAET;KACF;IACD,OAAO,UAAU,CAAC;AACpB,CAAC;AAhCD,8CAgCC;AAED,SAAgB,4BAA4B,CAAC,GAAW;IACtD,IAAI,KAAK,GAAG,uBAAuB,CAAC,GAAG,CAAC,CAAC;IACzC,MAAM,kBAAkB,GAA6B,EAAE,CAAC;IACxD,MAAM,UAAU,GAAgB,IAAI,GAAG,EAAE,CAAC;IAC1C,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;QACjC,IAAI,GAAG,GAAgB,IAAI,GAAG,EAAE,CAAC;QACjC,KAAK,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAgB,EAAE,EAAE;YACtC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YACtB,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QACH,kBAAkB,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAC5C,CAAC,CAAC,CAAC;IACH,OAAO,EAAE,KAAK,EAAE,kBAAkB,EAAE,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;AAC3E,CAAC;AAbD,oEAaC;AAED,SAAgB,uBAAuB,CAAC,GAAW;IACjD,IAAI,GAAG,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;QACrB,OAAO,EAAE,CAAC;KACX;IACD,MAAM,GAAG,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC;IAEpC,IAAI,KAAK,GAAoC,EAAE,CAAC;IAChD,KAAK,MAAM,GAAG,IAAI,GAAG,CAAC,WAAW,EAAE;QACjC,QAAQ,GAAG,CAAC,IAAI,EAAE;YAChB,KAAK,cAAI,CAAC,sBAAsB;gBAC9B,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,uBAAuB,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC5F,MAAM;YACR,KAAK,cAAI,CAAC,yBAAyB;gBACjC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,0BAA0B,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC/F,MAAM;YACR,KAAK,cAAI,CAAC,qBAAqB;gBAC7B,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,sBAAsB,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC3F,MAAM;YACR,KAAK,cAAI,CAAC,4BAA4B;gBACpC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,4BAA4B,CAAC,GAAG,CAAC,CAAC,CAAC;gBACjG,MAAM;YACR,KAAK,cAAI,CAAC,oBAAoB;gBAC5B,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,qBAAqB,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC1F,MAAM;YACR,KAAK,cAAI,CAAC,sBAAsB;gBAC9B,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,uBAAuB,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC5F,MAAM;YACR,QAAQ;SAET;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAhCD,0DAgCC;AAED,SAAgB,uBAAuB,CAAC,IAA8B;IACpE,IAAI,IAAI,GAAoB,EAAE,CAAC;IAC/B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE,EAAE;QACrC,MAAM,SAAS,GAAG,sBAAsB,CAAC,KAAK,CAAC,CAAC;QAChD,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;KAC/B;IACD,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;AAC5C,CAAC;AAPD,0DAOC;AAED,SAAgB,0BAA0B,CAAC,IAAiC;IAC1E,IAAI,IAAI,GAAoB,EAAE,CAAC;IAC/B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE,EAAE;QACrC,MAAM,SAAS,GAAG,sBAAsB,CAAC,KAAK,CAAC,CAAC;QAChD,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;KAC/B;IACD,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;AAC5C,CAAC;AAPD,gEAOC;AAED,SAAgB,sBAAsB,CAAC,IAAyB;IAC9D,IAAI,IAAI,GAAoB,EAAE,CAAC;IAC/B,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,SAAS,IAAI,EAAE,EAAE;QACtC,MAAM,OAAO,GAAG,yBAAyB,CAAC,GAAG,CAAC,CAAC;QAC/C,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;KAC7B;IACD,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;AAC5C,CAAC;AAPD,wDAOC;AAED,SAAgB,yBAAyB,CAAC,IAA8B;IACtE,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,CAAC;AACtC,CAAC;AAFD,8DAEC;AAED,SAAgB,sBAAsB,CAAC,IAA6B;IAClE,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,CAAC;AACtC,CAAC;AAFD,wDAEC;AAED,SAAgB,uBAAuB,CAAC,IAA8B;IACpE,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,CAAC;AACtC,CAAC;AAFD,0DAEC;AAED,SAAgB,4BAA4B,CAAC,IAAmC;IAC9E,IAAI,IAAI,GAAoB,EAAE,CAAC;IAC/B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE,EAAE;QACrC,MAAM,SAAS,GAAG,yBAAyB,CAAC,KAAK,CAAC,CAAC;QACnD,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;KAC/B;IACD,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;AAC5C,CAAC;AAPD,oEAOC;AAED,SAAgB,qBAAqB,CAAC,IAA4B;IAChE,IAAI,IAAI,GAAoB,EAAE,CAAC;IAC/B,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,IAAI,EAAE,EAAE;QACnC,MAAM,OAAO,GAAG,0BAA0B,CAAC,GAAG,CAAC,CAAC;QAChD,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;KAC7B;IACD,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC;AAC5C,CAAC;AAPD,sDAOC;AAED,SAAgB,0BAA0B,CAAC,IAA6B;IACtE,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,CAAC,CAAC,CAAC;AACtC,CAAC;AAFD,gEAEC;AAED,SAAS,kBAAkB,CAAC,GAAW;;IACrC,IAAI,GAAG,CAAC;IACR,IAAI;QACF,GAAG,GAAG,IAAA,eAAK,EAAC,GAAG,CAAC,CAAC;KAClB;IAAC,OAAO,CAAC,EAAE;QACV,IAAI,MAAA,CAAC,CAAC,OAAO,0CAAE,QAAQ,CAAC,cAAc,CAAC,EAAE;YACvC,MAAM,IAAI,KAAK,CAAC,yFAAyF,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;SACvH;QACD,MAAM,CAAC,CAAC;KACT;IAED,OAAO,GAAG,CAAC;AACb,CAAC"}