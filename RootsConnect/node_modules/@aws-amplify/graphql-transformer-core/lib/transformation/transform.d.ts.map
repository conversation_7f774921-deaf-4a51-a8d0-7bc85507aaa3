{"version": 3, "file": "transform.d.ts", "sourceRoot": "", "sources": ["../../src/transformation/transform.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,wBAAwB,EAExB,yBAAyB,EACzB,qBAAqB,EACrB,cAAc,EACd,mBAAmB,EACnB,eAAe,EACf,SAAS,EACV,MAAM,6CAA6C,CAAC;AACrD,OAAO,KAAK,EACV,aAAa,EACb,oBAAoB,EACpB,0BAA0B,EAC1B,mBAAmB,EACnB,4BAA4B,EAC5B,uBAAuB,EACvB,0BAA0B,EAC3B,MAAM,6CAA6C,CAAC;AAmBrD,OAAO,EAAE,YAAY,EAA2B,MAAM,kBAAkB,CAAC;AACzE,OAAO,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;AACvC,OAAO,EAAE,cAAc,EAAE,MAAM,8BAA8B,CAAC;AAE9D,OAAO,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;AAE5C,OAAO,EAAE,iBAAiB,EAAE,MAAM,+BAA+B,CAAC;AAOlE,OAAO,EAAE,eAAe,EAAE,MAAM,SAAS,CAAC;AA4B1C,MAAM,WAAW,uBAAuB;IACtC,QAAQ,CAAC,YAAY,EAAE,yBAAyB,EAAE,CAAC;IAInD,QAAQ,CAAC,YAAY,CAAC,EAAE,YAAY,CAAC;IAErC,QAAQ,CAAC,UAAU,CAAC,EAAE,wBAAwB,CAAC;IAC/C,QAAQ,CAAC,mBAAmB,CAAC,EAAE,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAC5D,QAAQ,CAAC,IAAI,CAAC,EAAE,qBAAqB,CAAC;IACtC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,eAAe,EAAE,CAAC,CAAC;IAC9D,QAAQ,CAAC,cAAc,CAAC,EAAE,cAAc,CAAC;CAC1C;AAED,MAAM,WAAW,eAAgB,SAAQ,4BAA4B,EAAE,uBAAuB,EAAE,0BAA0B;IACxH,KAAK,EAAE,SAAS,CAAC;IACjB,mBAAmB,EAAE,mBAAmB,CAAC;IACzC,iBAAiB,CAAC,EAAE,0BAA0B,CAAC;IAC/C,aAAa,EAAE,aAAa,CAAC;IAC7B,eAAe,EAAE,eAAe,CAAC;IACjC,MAAM,EAAE,MAAM,CAAC;IACf,OAAO,CAAC,EAAE,IAAI,GAAG,SAAS,CAAC;CAC5B;AAED,MAAM,MAAM,YAAY,GAAG;IAAE,CAAC,UAAU,EAAE,MAAM,GAAG,MAAM,CAAA;CAAE,CAAC;AAE5D,qBAAa,gBAAgB;IAoBf,OAAO,CAAC,QAAQ,CAAC,OAAO;IAnBpC,OAAO,CAAC,YAAY,CAA8B;IAElD,OAAO,CAAC,qBAAqB,CAAe;IAE5C,OAAO,CAAC,QAAQ,CAAC,UAAU,CAA2B;IAEtD,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAiB;IAEjD,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAoC;IAErE,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAsB;IAK1D,OAAO,CAAC,mBAAmB,CAAgC;IAE3D,OAAO,CAAC,IAAI,CAAmB;gBAEF,OAAO,EAAE,uBAAuB;IAyCtD,gBAAgB,CAAC,MAAM,EAAE,YAAY,GAAG,YAAY;IAwBpD,SAAS,CAAC,EACf,aAAa,EACb,oBAAoB,EACpB,mBAAmB,EACnB,iBAAiB,EACjB,eAAe,EACf,kBAAkB,EAClB,MAAM,EACN,KAAK,EACL,gCAAgC,EAChC,eAAe,EACf,OAAO,GACR,EAAE,eAAe,GAAG,IAAI;IAsJzB,SAAS,CAAC,kBAAkB,CAC1B,YAAY,EAAE,oBAAoB,EAClC,aAAa,EAAE,aAAa,EAC5B,eAAe,EAAE,eAAe,EAChC,MAAM,EAAE,iBAAiB,EACzB,mBAAmB,EAAE,mBAAmB,EACxC,OAAO,CAAC,EAAE,IAAI,GAAG,SAAS,GACzB,UAAU;IAkEb,OAAO,CAAC,gBAAgB;IA0BxB,OAAO,CAAC,eAAe;IAkDvB,OAAO,CAAC,cAAc;IAyDtB,OAAO,CAAC,iBAAiB;IA8BzB,OAAO,CAAC,kBAAkB;IA+B1B,OAAO,CAAC,eAAe;IA4BvB,OAAO,CAAC,cAAc;IA4BtB,OAAO,CAAC,aAAa;IA+BrB,OAAO,CAAC,kBAAkB;IA6B1B,OAAO,CAAC,oBAAoB;IA+B5B,OAAO,CAAC,mBAAmB;IA6BpB,OAAO,IAAI,cAAc,EAAE;IAIlC,OAAO,CAAC,oBAAoB;CAQ7B"}