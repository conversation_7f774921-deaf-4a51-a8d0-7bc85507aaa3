import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  LambdaClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../LambdaClient";
import {
  CreateCodeSigningConfigRequest,
  CreateCodeSigningConfigResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface CreateCodeSigningConfigCommandInput
  extends CreateCodeSigningConfigRequest {}
export interface CreateCodeSigningConfigCommandOutput
  extends CreateCodeSigningConfigResponse,
    __MetadataBearer {}
declare const CreateCodeSigningConfigCommand_base: {
  new (
    input: CreateCodeSigningConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateCodeSigningConfigCommandInput,
    CreateCodeSigningConfigCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    __0_0: CreateCodeSigningConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateCodeSigningConfigCommandInput,
    CreateCodeSigningConfigCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateCodeSigningConfigCommand extends CreateCodeSigningConfigCommand_base {}
