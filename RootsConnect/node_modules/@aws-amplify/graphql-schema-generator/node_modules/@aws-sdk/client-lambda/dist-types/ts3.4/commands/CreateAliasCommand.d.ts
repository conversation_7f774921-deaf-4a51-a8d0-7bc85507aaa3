import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  LambdaClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../LambdaClient";
import { AliasConfiguration, CreateAliasRequest } from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface CreateAliasCommandInput extends CreateAliasRequest {}
export interface CreateAliasCommandOutput
  extends AliasConfiguration,
    __MetadataBearer {}
declare const CreateAliasCommand_base: {
  new (
    input: CreateAliasCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateAliasCommandInput,
    CreateAliasCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    __0_0: CreateAliasCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    CreateAliasCommandInput,
    CreateAliasCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class CreateAliasCommand extends CreateAliasCommand_base {}
