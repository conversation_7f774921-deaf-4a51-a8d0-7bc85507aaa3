import { Command as $Command } from "@smithy/smithy-client";
import { <PERSON>ada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  LambdaClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../LambdaClient";
import { DeleteFunctionCodeSigningConfigRequest } from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface DeleteFunctionCodeSigningConfigCommandInput
  extends DeleteFunctionCodeSigningConfigRequest {}
export interface DeleteFunctionCodeSigningConfigCommandOutput
  extends __MetadataBearer {}
declare const DeleteFunctionCodeSigningConfigCommand_base: {
  new (
    input: DeleteFunctionCodeSigningConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteFunctionCodeSigningConfigCommandInput,
    DeleteFunctionCodeSigningConfigCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    __0_0: DeleteFunctionCodeSigningConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DeleteFunctionCodeSigningConfigCommandInput,
    DeleteFunctionCodeSigningConfigCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DeleteFunctionCodeSigningConfigCommand extends DeleteFunctionCodeSigningConfigCommand_base {}
