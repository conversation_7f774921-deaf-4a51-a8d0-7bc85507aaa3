"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.buildOverrideTypesBindings = exports.buildFormPropNode = exports.validationFunctionType = exports.validationResponseType = exports.generateFieldTypes = exports.generateTypeNodeFromObject = exports.generateObjectFromPaths = exports.getValidationTypeName = exports.getInputValuesTypeName = void 0;
/*
  Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.

  Licensed under the Apache License, Version 2.0 (the "License").
  You may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
 */
const codegen_ui_1 = require("@aws-amplify/codegen-ui");
const typescript_1 = require("typescript");
const helpers_1 = require("../../helpers");
const typescript_type_map_1 = require("./typescript-type-map");
const imports_1 = require("../../imports");
const primitive_1 = require("../../primitive");
const constants_1 = require("../../utils/constants");
const graphql_1 = require("../../utils/graphql");
/**
 * based on the provided dataType (appsync scalar)
 * converts to the correct typescript type
 * default assumption is string type
 */
const getTypeNode = ({ componentType, dataType, isArray, isValidation, importCollection, renderConfig, }) => {
    let typeNode = typescript_1.factory.createKeywordTypeNode(typescript_1.SyntaxKind.StringKeyword);
    if (componentType in typescript_type_map_1.FIELD_TYPE_TO_TYPESCRIPT_MAP) {
        typeNode = typescript_1.factory.createKeywordTypeNode(typescript_type_map_1.FIELD_TYPE_TO_TYPESCRIPT_MAP[componentType]);
    }
    if (dataType && typeof dataType === 'string' && dataType in typescript_type_map_1.DATA_TYPE_TO_TYPESCRIPT_MAP) {
        typeNode = typescript_1.factory.createKeywordTypeNode(typescript_type_map_1.DATA_TYPE_TO_TYPESCRIPT_MAP[dataType]);
    }
    if (dataType && typeof dataType === 'object' && 'model' in dataType) {
        const modelName = dataType.model;
        const aliasedModel = importCollection === null || importCollection === void 0 ? void 0 : importCollection.addModelImport(modelName);
        let identifier = aliasedModel || modelName;
        if ((0, graphql_1.isGraphqlConfig)(renderConfig === null || renderConfig === void 0 ? void 0 : renderConfig.apiConfiguration) && !(renderConfig === null || renderConfig === void 0 ? void 0 : renderConfig.apiConfiguration.typesFilePath)) {
            identifier = 'any';
        }
        typeNode = typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier(identifier));
    }
    if (isValidation) {
        return typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier('ValidationFunction'), [typeNode]);
    }
    if (isArray) {
        return typescript_1.factory.createArrayTypeNode(typeNode);
    }
    return typeNode;
};
const getInputValuesTypeName = (formName) => {
    return `${formName}InputValues`;
};
exports.getInputValuesTypeName = getInputValuesTypeName;
const getValidationTypeName = (formName) => {
    return `${formName}ValidationValues`;
};
exports.getValidationTypeName = getValidationTypeName;
/**
 * given the nested json paths rejoin them into one object
 * where the leafs are the types ex. string | number | boolean
 * src: https://stackoverflow.com/questions/70218560/creating-a-nested-object-from-entries
 *
 * @param object
 * @param [key, value] entry/tuple object shape created from key and value is set at the leaf
 */
const generateObjectFromPaths = (object, [key, value]) => {
    var _a;
    const keys = key.split('.');
    const last = (_a = keys.pop()) !== null && _a !== void 0 ? _a : '';
    // eslint-disable-next-line no-return-assign, no-param-reassign
    keys.reduce((o, k) => { var _a; return ((_a = o[k]) !== null && _a !== void 0 ? _a : (o[k] = {})); }, object)[last] = getTypeNode(value);
    return object;
};
exports.generateObjectFromPaths = generateObjectFromPaths;
const generateTypeNodeFromObject = (obj) => {
    return Object.keys(obj).map((key) => {
        const child = obj[key];
        const value = typeof child === 'object' && Object.getPrototypeOf(child) === Object.prototype
            ? typescript_1.factory.createTypeLiteralNode((0, exports.generateTypeNodeFromObject)(child))
            : child;
        const propertyName = !(0, codegen_ui_1.isValidVariableName)(key) ? typescript_1.factory.createStringLiteral(key) : typescript_1.factory.createIdentifier(key);
        return typescript_1.factory.createPropertySignature(undefined, propertyName, typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), value);
    });
};
exports.generateTypeNodeFromObject = generateTypeNodeFromObject;
/**
 * this generates the input types for onSubmit, onSuccess, onChange, and onValidate
 * onValidate is the one case where it passes true to get the ValidationType
 * instead of the base type
 *
 * validation type is selected
 * export declare type NestedJsonValidationValues = {
    "first-Name"?: ValidationFunction<string>;
    lastName?: ValidationFunction<string>;
    Nicknames1?: ValidationFunction<string>;
    "nick-names2"?: ValidationFunction<string>;
    "first Name"?: ValidationFunction<string>;
    bio?: {
        "favorite Quote"?: ValidationFunction<string>;
        "favorite-Animal"?: ValidationFunction<string>;
    };
 * };
 * if its regular validation then it will be using the main types of the object array types are allowed in this case
 *
 * @param formName
 * @param {('input' | 'validation')}
 * @param fieldConfigs
 * @returns
 */
const generateFieldTypes = (formName, type, fieldConfigs, importCollection, renderConfig) => {
    const nestedPaths = [];
    const typeNodes = [];
    const isValidation = type === 'validation';
    const typeName = isValidation ? (0, exports.getValidationTypeName)(formName) : (0, exports.getInputValuesTypeName)(formName);
    Object.entries(fieldConfigs).forEach(([fieldName, { dataType, componentType, isArray }]) => {
        const getTypeNodeParam = {
            dataType,
            componentType,
            isArray: !!isArray,
            isValidation,
            importCollection,
            renderConfig,
        };
        const hasNestedFieldPath = fieldName.split('.').length > 1;
        if (hasNestedFieldPath) {
            nestedPaths.push([fieldName, getTypeNodeParam]);
        }
        else {
            const propertyName = !(0, codegen_ui_1.isValidVariableName)(fieldName)
                ? typescript_1.factory.createStringLiteral(fieldName)
                : typescript_1.factory.createIdentifier(fieldName);
            typeNodes.push(typescript_1.factory.createPropertySignature(undefined, propertyName, typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), getTypeNode(getTypeNodeParam)));
        }
    });
    if (nestedPaths.length) {
        const nestedObj = nestedPaths.reduce(exports.generateObjectFromPaths, {});
        const nestedTypeNodes = (0, exports.generateTypeNodeFromObject)(nestedObj);
        typeNodes.push(...nestedTypeNodes);
    }
    return typescript_1.factory.createTypeAliasDeclaration(undefined, [typescript_1.factory.createModifier(typescript_1.SyntaxKind.ExportKeyword), typescript_1.factory.createModifier(typescript_1.SyntaxKind.DeclareKeyword)], typescript_1.factory.createIdentifier(typeName), undefined, typescript_1.factory.createTypeLiteralNode(typeNodes));
};
exports.generateFieldTypes = generateFieldTypes;
/**
 * export declare type ValidationResponse = {
 *  hasError: boolean;
 *  errorMessage?: string;
 * };
 */
exports.validationResponseType = typescript_1.factory.createTypeAliasDeclaration(undefined, [typescript_1.factory.createModifier(typescript_1.SyntaxKind.ExportKeyword), typescript_1.factory.createModifier(typescript_1.SyntaxKind.DeclareKeyword)], typescript_1.factory.createIdentifier('ValidationResponse'), undefined, typescript_1.factory.createTypeLiteralNode([
    typescript_1.factory.createPropertySignature(undefined, typescript_1.factory.createIdentifier('hasError'), undefined, typescript_1.factory.createKeywordTypeNode(typescript_1.SyntaxKind.BooleanKeyword)),
    typescript_1.factory.createPropertySignature(undefined, typescript_1.factory.createIdentifier('errorMessage'), typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), typescript_1.factory.createKeywordTypeNode(typescript_1.SyntaxKind.StringKeyword)),
]));
/**
 * export declare type ValidationFunction<T> =
 *  (value: T, validationResponse: ValidationResponse) => ValidationResponse | Promise<ValidationResponse>;
 */
exports.validationFunctionType = typescript_1.factory.createTypeAliasDeclaration(undefined, [typescript_1.factory.createModifier(typescript_1.SyntaxKind.ExportKeyword), typescript_1.factory.createModifier(typescript_1.SyntaxKind.DeclareKeyword)], typescript_1.factory.createIdentifier('ValidationFunction'), [typescript_1.factory.createTypeParameterDeclaration(typescript_1.factory.createIdentifier('T'), undefined, undefined)], typescript_1.factory.createFunctionTypeNode(undefined, [
    typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, typescript_1.factory.createIdentifier('value'), undefined, typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier('T'), undefined), undefined),
    typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, typescript_1.factory.createIdentifier('validationResponse'), undefined, typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier('ValidationResponse'), undefined), undefined),
], typescript_1.factory.createUnionTypeNode([
    typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier('ValidationResponse'), undefined),
    typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier('Promise'), [
        typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier('ValidationResponse'), undefined),
    ]),
])));
/*
    both datastore & custom datasource has onSubmit with the fields
    - onSubmit(fields)
    datastore includes additional hooks
    - onSuccess(fields)
    - onError(fields, errorMessage)
   */
const buildFormPropNode = (form, fieldConfigs, modelName, primaryKeys = []) => {
    const { name: formName, dataType: { dataSourceType, dataTypeName }, formActionType, } = form;
    const propSignatures = [];
    if (dataSourceType === 'DataStore') {
        if (formActionType === 'update') {
            if (primaryKeys.length >= 1) {
                propSignatures.push(createPrimaryKeysTypeProp(primaryKeys, fieldConfigs));
            }
            propSignatures.push(typescript_1.factory.createPropertySignature(undefined, typescript_1.factory.createIdentifier((0, helpers_1.lowerCaseFirst)(dataTypeName)), typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier(modelName !== null && modelName !== void 0 ? modelName : dataTypeName), undefined)));
        }
        if (formActionType === 'create') {
            propSignatures.push(typescript_1.factory.createPropertySignature(undefined, typescript_1.factory.createIdentifier('clearOnSuccess'), typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), typescript_1.factory.createKeywordTypeNode(typescript_1.SyntaxKind.BooleanKeyword)));
        }
        propSignatures.push(typescript_1.factory.createPropertySignature(undefined, 'onSubmit', typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), typescript_1.factory.createFunctionTypeNode(undefined, [
            typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, 'fields', undefined, typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier((0, exports.getInputValuesTypeName)(formName)), undefined), undefined),
        ], typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier((0, exports.getInputValuesTypeName)(formName)), undefined))), typescript_1.factory.createPropertySignature(undefined, 'onSuccess', typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), typescript_1.factory.createFunctionTypeNode(undefined, [
            typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, typescript_1.factory.createIdentifier('fields'), undefined, typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier((0, exports.getInputValuesTypeName)(formName)), undefined), undefined),
        ], typescript_1.factory.createKeywordTypeNode(typescript_1.SyntaxKind.VoidKeyword))), typescript_1.factory.createPropertySignature(undefined, typescript_1.factory.createIdentifier('onError'), typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), typescript_1.factory.createFunctionTypeNode(undefined, [
            typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, typescript_1.factory.createIdentifier('fields'), undefined, typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier((0, exports.getInputValuesTypeName)(formName)), undefined), undefined),
            typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, typescript_1.factory.createIdentifier('errorMessage'), undefined, typescript_1.factory.createKeywordTypeNode(typescript_1.SyntaxKind.StringKeyword), undefined),
        ], typescript_1.factory.createKeywordTypeNode(typescript_1.SyntaxKind.VoidKeyword))));
    }
    if (dataSourceType === 'Custom') {
        if (formActionType === 'update') {
            propSignatures.push(typescript_1.factory.createPropertySignature(undefined, typescript_1.factory.createIdentifier('initialData'), typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), typescript_1.factory.createTypeReferenceNode((0, exports.getInputValuesTypeName)(formName), undefined)));
        }
        propSignatures.push(typescript_1.factory.createPropertySignature(undefined, 'onSubmit', undefined, typescript_1.factory.createFunctionTypeNode(undefined, [
            typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, 'fields', undefined, typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier((0, exports.getInputValuesTypeName)(formName)), undefined), undefined),
        ], typescript_1.factory.createKeywordTypeNode(typescript_1.SyntaxKind.VoidKeyword))));
    }
    if ((0, codegen_ui_1.shouldIncludeCancel)(form)) {
        propSignatures.push(
        // onCancel?: () => void
        typescript_1.factory.createPropertySignature(undefined, 'onCancel', typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), typescript_1.factory.createFunctionTypeNode(undefined, [], typescript_1.factory.createKeywordTypeNode(typescript_1.SyntaxKind.VoidKeyword))));
    }
    propSignatures.push(
    // onChange?: (fields: Record<string, unknown>) => Record<string, unknown>
    typescript_1.factory.createPropertySignature(undefined, 'onChange', typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), typescript_1.factory.createFunctionTypeNode(undefined, [
        typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, typescript_1.factory.createIdentifier('fields'), undefined, typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier((0, exports.getInputValuesTypeName)(formName)), undefined), undefined),
    ], typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier((0, exports.getInputValuesTypeName)(formName)), undefined))), 
    // onValidate?: {formName}ValidationValues
    typescript_1.factory.createPropertySignature(undefined, typescript_1.factory.createIdentifier('onValidate'), typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier((0, exports.getValidationTypeName)(formName)), undefined)));
    return typescript_1.factory.createTypeLiteralNode(propSignatures);
};
exports.buildFormPropNode = buildFormPropNode;
const buildOverrideTypesBindings = (formComponent, formDefinition, importCollection) => {
    importCollection.addImport(imports_1.ImportSource.UI_REACT, 'GridProps');
    const typeNodes = [
        typescript_1.factory.createPropertySignature(undefined, typescript_1.factory.createIdentifier(`${formComponent.name}Grid`), typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier(primitive_1.PRIMITIVE_OVERRIDE_PROPS), [
            typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier('GridProps'), undefined),
        ])),
    ];
    formDefinition.elementMatrix.forEach((row, index) => {
        if (row.length > 1) {
            typeNodes.push(typescript_1.factory.createPropertySignature(undefined, typescript_1.factory.createIdentifier(`RowGrid${index}`), typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier(primitive_1.PRIMITIVE_OVERRIDE_PROPS), [
                typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier('GridProps'), undefined),
            ])));
        }
        row.forEach((field) => {
            let propKey = typescript_1.factory.createIdentifier(field);
            if (field.split('.').length > 1 || !(0, codegen_ui_1.isValidVariableName)(field)) {
                propKey = typescript_1.factory.createStringLiteral(field);
            }
            let componentTypePropName = `${formDefinition.elements[field].componentType}Props`;
            if (formDefinition.elements[field].componentType === 'StorageField') {
                componentTypePropName = 'StorageManagerProps';
                importCollection.addImport(imports_1.ImportSource.REACT_STORAGE, componentTypePropName);
            }
            else {
                importCollection.addImport(imports_1.ImportSource.UI_REACT, componentTypePropName);
            }
            typeNodes.push(typescript_1.factory.createPropertySignature(undefined, propKey, typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier(primitive_1.PRIMITIVE_OVERRIDE_PROPS), [
                typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier(componentTypePropName), undefined),
            ])));
        });
    });
    return typescript_1.factory.createTypeAliasDeclaration(undefined, [typescript_1.factory.createModifier(typescript_1.SyntaxKind.ExportKeyword), typescript_1.factory.createModifier(typescript_1.SyntaxKind.DeclareKeyword)], typescript_1.factory.createIdentifier(`${formComponent.name}OverridesProps`), undefined, typescript_1.factory.createIntersectionTypeNode([
        typescript_1.factory.createTypeLiteralNode(typeNodes),
        typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier('EscapeHatchProps'), undefined),
    ]));
};
exports.buildOverrideTypesBindings = buildOverrideTypesBindings;
const createPrimaryKeysTypeProp = (primaryKeys, fieldConfigs) => {
    // first element is the property name
    if (primaryKeys.length === 1) {
        return typescript_1.factory.createPropertySignature(undefined, typescript_1.factory.createIdentifier(primaryKeys[0]), typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), typescript_1.factory.createKeywordTypeNode(typescript_1.SyntaxKind.StringKeyword));
    }
    if (primaryKeys.length <= 0) {
        throw new codegen_ui_1.InternalError('primaryKeys must not be empty');
    }
    // creates the type literal for a composite key
    const compositeKeyTypeLiteral = primaryKeys.map((primaryKey) => {
        var _a;
        let keywordType = typescript_1.SyntaxKind.StringKeyword;
        const element = fieldConfigs[primaryKey];
        if (element) {
            const { dataType } = element;
            // non-scalar & boolean are not supported for primary keys that leaves number and string
            const stringDataType = typeof dataType === 'string' ? dataType : 'String';
            keywordType = (_a = typescript_type_map_1.DATA_TYPE_TO_TYPESCRIPT_MAP[stringDataType]) !== null && _a !== void 0 ? _a : typescript_1.SyntaxKind.StringKeyword;
        }
        return typescript_1.factory.createPropertySignature(undefined, typescript_1.factory.createIdentifier(primaryKey), undefined, typescript_1.factory.createKeywordTypeNode(keywordType));
    });
    return typescript_1.factory.createPropertySignature(undefined, typescript_1.factory.createIdentifier(constants_1.COMPOSITE_PRIMARY_KEY_PROP_NAME), typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), typescript_1.factory.createTypeLiteralNode(compositeKeyTypeLiteral));
};
//# sourceMappingURL=type-helper.js.map