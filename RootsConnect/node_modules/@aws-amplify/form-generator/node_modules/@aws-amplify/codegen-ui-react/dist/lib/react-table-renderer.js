"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReactTableRenderer = void 0;
const typescript_1 = require("typescript");
const imports_1 = require("./imports");
const primitive_1 = require("./primitive");
const react_table_renderer_helper_1 = require("./react-table-renderer-helper");
class ReactTableRenderer {
    constructor(view, definition, metadata, imports) {
        this.requiredUIReactImports = [
            primitive_1.Primitive.Table,
            primitive_1.Primitive.TableHead,
            primitive_1.Primitive.TableBody,
            primitive_1.Primitive.TableRow,
            primitive_1.Primitive.TableCell,
            primitive_1.Primitive.TableFoot,
        ];
        this.viewComponent = view;
        this.viewDefinition = definition;
        this.viewMetadata = metadata;
        this.viewMetadata.fieldFormatting = {};
        this.viewDefinition.columns.forEach((column) => {
            if (column.valueFormatting) {
                this.viewMetadata.fieldFormatting[column.header] = { ...column.valueFormatting };
            }
        });
        this.requiredUIReactImports.forEach((importName) => {
            imports.addImport(imports_1.ImportSource.UI_REACT, importName);
        });
    }
    createOpeningTableElement() {
        const tableAttributes = [];
        if (this.viewDefinition.tableConfig.table.highlightOnHover) {
            tableAttributes.push(typescript_1.factory.createJsxAttribute(typescript_1.factory.createIdentifier('highlightOnHover'), typescript_1.factory.createJsxExpression(undefined, typescript_1.factory.createIdentifier('highlightOnHover'))));
        }
        return typescript_1.factory.createJsxOpeningElement(typescript_1.factory.createIdentifier(primitive_1.Primitive.Table), undefined, typescript_1.factory.createJsxAttributes(tableAttributes));
    }
    createTableRow(children, attributes) {
        return typescript_1.factory.createJsxElement(typescript_1.factory.createJsxOpeningElement(typescript_1.factory.createIdentifier(primitive_1.Primitive.TableRow), undefined, attributes), children, typescript_1.factory.createJsxClosingElement(typescript_1.factory.createIdentifier(primitive_1.Primitive.TableRow)));
    }
    createTableHeadRow() {
        const cellsInHeader = this.viewDefinition.columns.map((column) => {
            var _a;
            return typescript_1.factory.createJsxElement(typescript_1.factory.createJsxOpeningElement(typescript_1.factory.createIdentifier(primitive_1.Primitive.TableCell), undefined, typescript_1.factory.createJsxAttributes([
                typescript_1.factory.createJsxAttribute(typescript_1.factory.createIdentifier('as'), typescript_1.factory.createStringLiteral('th')),
            ])), [typescript_1.factory.createJsxText((_a = column.label) !== null && _a !== void 0 ? _a : column.header, false)], typescript_1.factory.createJsxClosingElement(typescript_1.factory.createIdentifier(primitive_1.Primitive.TableCell)));
        });
        return this.createTableRow(cellsInHeader, typescript_1.factory.createJsxAttributes([]));
    }
    createTableHeadElement() {
        return typescript_1.factory.createJsxElement(typescript_1.factory.createJsxOpeningElement(typescript_1.factory.createIdentifier(primitive_1.Primitive.TableHead), undefined, typescript_1.factory.createJsxAttributes([])), [this.createTableHeadRow()], typescript_1.factory.createJsxClosingElement(typescript_1.factory.createIdentifier(primitive_1.Primitive.TableHead)));
    }
    createTableHeadElementBlock() {
        return typescript_1.factory.createJsxExpression(undefined, typescript_1.factory.createBinaryExpression(typescript_1.factory.createPrefixUnaryExpression(typescript_1.SyntaxKind.ExclamationToken, typescript_1.factory.createIdentifier('disableHeaders')), typescript_1.SyntaxKind.AmpersandAmpersandToken, this.createTableHeadElement()));
    }
    createRowOnClickCBAttr() {
        return typescript_1.factory.createJsxAttributes([
            typescript_1.factory.createJsxAttribute(typescript_1.factory.createIdentifier('onClick'), typescript_1.factory.createJsxExpression(undefined, typescript_1.factory.createConditionalExpression(typescript_1.factory.createIdentifier('onRowClick'), typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), typescript_1.factory.createArrowFunction(undefined, undefined, [], undefined, typescript_1.factory.createToken(typescript_1.SyntaxKind.EqualsGreaterThanToken), typescript_1.factory.createCallExpression(typescript_1.factory.createIdentifier('onRowClick'), undefined, [
                typescript_1.factory.createIdentifier('item'),
                typescript_1.factory.createIdentifier('index'),
            ])), typescript_1.factory.createToken(typescript_1.SyntaxKind.ColonToken), typescript_1.factory.createNull()))),
        ]);
    }
    /*  Expected arg shape examples:
        For dateTime:
        {
          type: 'NonLocaleDateTimeFormat'
          format: {
            nonLocaleDateTimeFormat: {
              dateFormat: 'locale',
              timeFormat: 'hours24',
            }
          }
        }
        For date:
        {
          type: 'DateFormat'
          format: {
            dateFormat: 'Mmm, DD YYYY',
          }
        }
    */
    generateFormatLiteralExpression(field) {
        const formatting = this.viewMetadata.fieldFormatting;
        if (formatting === null || formatting === void 0 ? void 0 : formatting[field]) {
            return (0, react_table_renderer_helper_1.objectToExpression)(formatting[field].stringFormat);
        }
        return typescript_1.factory.createIdentifier('undefined');
    }
    createFieldAccessExpression(identifier, field) {
        return typescript_1.factory.createPropertyAccessChain(typescript_1.factory.createIdentifier(identifier), typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionDotToken), typescript_1.factory.createIdentifier(field));
    }
    createFormatArg(field) {
        var _a;
        const format = (_a = this.viewMetadata.fieldFormatting) === null || _a === void 0 ? void 0 : _a[field];
        const type = (0, react_table_renderer_helper_1.stringFormatToType)(format);
        if (format && type) {
            return typescript_1.factory.createObjectLiteralExpression([
                typescript_1.factory.createPropertyAssignment(typescript_1.factory.createIdentifier('type'), typescript_1.factory.createStringLiteral(type)),
                typescript_1.factory.createPropertyAssignment(typescript_1.factory.createIdentifier('format'), this.generateFormatLiteralExpression(field)),
            ]);
        }
        return undefined;
    }
    createFormatCallOrPropAccess(field) {
        const formatterArg = this.createFormatArg(field);
        return formatterArg
            ? typescript_1.factory.createCallExpression(typescript_1.factory.createIdentifier('formatter'), undefined, [
                this.createFieldAccessExpression('item', field),
                formatterArg,
            ])
            : this.createFieldAccessExpression('item', field);
    }
    createTableBodyCellFromColumn(column) {
        const columnId = column.header;
        return typescript_1.factory.createJsxElement(typescript_1.factory.createJsxOpeningElement(typescript_1.factory.createIdentifier(primitive_1.Primitive.TableCell), undefined, typescript_1.factory.createJsxAttributes([])), [
            typescript_1.factory.createJsxExpression(undefined, typescript_1.factory.createConditionalExpression(this.createFieldAccessExpression('format', columnId), typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('format'), typescript_1.factory.createIdentifier(columnId)), undefined, [this.createFieldAccessExpression('item', columnId)]), typescript_1.factory.createToken(typescript_1.SyntaxKind.ColonToken), this.createFormatCallOrPropAccess(columnId))),
        ], typescript_1.factory.createJsxClosingElement(typescript_1.factory.createIdentifier(primitive_1.Primitive.TableCell)));
    }
    createTableBodyRow() {
        const tableBodyCells = this.viewDefinition.columns.map((column) => this.createTableBodyCellFromColumn(column));
        return this.createTableRow(tableBodyCells, this.createRowOnClickCBAttr());
    }
    createTableBodyElement() {
        const itemParam = typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, typescript_1.factory.createIdentifier('item'), undefined, undefined, undefined);
        const indexParam = typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, typescript_1.factory.createIdentifier('index'), undefined, undefined, undefined);
        return typescript_1.factory.createJsxElement(typescript_1.factory.createJsxOpeningElement(typescript_1.factory.createIdentifier(primitive_1.Primitive.TableBody), undefined, typescript_1.factory.createJsxAttributes([])), [
            typescript_1.factory.createJsxExpression(undefined, typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('items'), typescript_1.factory.createIdentifier('map')), undefined, [
                typescript_1.factory.createArrowFunction(undefined, undefined, [itemParam, indexParam], undefined, typescript_1.factory.createToken(typescript_1.SyntaxKind.EqualsGreaterThanToken), typescript_1.factory.createParenthesizedExpression(this.createTableBodyRow())),
            ])),
        ], typescript_1.factory.createJsxClosingElement(typescript_1.factory.createIdentifier(primitive_1.Primitive.TableBody)));
    }
    renderElement() {
        return typescript_1.factory.createJsxElement(this.createOpeningTableElement(), [this.createTableHeadElementBlock(), this.createTableBodyElement()], typescript_1.factory.createJsxClosingElement(typescript_1.factory.createIdentifier(primitive_1.Primitive.Table)));
    }
}
exports.ReactTableRenderer = ReactTableRenderer;
//# sourceMappingURL=react-table-renderer.js.map