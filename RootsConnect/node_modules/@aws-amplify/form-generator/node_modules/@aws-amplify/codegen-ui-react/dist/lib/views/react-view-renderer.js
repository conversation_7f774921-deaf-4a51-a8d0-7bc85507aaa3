"use strict";
/*
  Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.

  Licensed under the Apache License, Version 2.0 (the "License").
  You may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
 */
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReactViewTemplateRenderer = void 0;
const codegen_ui_1 = require("@aws-amplify/codegen-ui");
const typescript_1 = require("typescript");
const os_1 = require("os");
const react_studio_template_renderer_helper_1 = require("../react-studio-template-renderer-helper");
const imports_1 = require("../imports");
const primitive_1 = require("../primitive");
const react_component_render_helper_1 = require("../react-component-render-helper");
const react_output_manager_1 = require("../react-output-manager");
const react_render_config_1 = require("../react-render-config");
const react_table_renderer_helper_1 = require("../react-table-renderer-helper");
const utils_file_functions_1 = require("../utils-file-functions");
class ReactViewTemplateRenderer extends codegen_ui_1.StudioTemplateRenderer {
    constructor(component, dataSchema, renderConfig) {
        super(component, new react_output_manager_1.ReactOutputManager(), renderConfig);
        this.importCollection = new imports_1.ImportCollection();
        this.viewDefinition = codegen_ui_1.DEFAULT_TABLE_DEFINITION;
        this.renderConfig = {
            ...react_studio_template_renderer_helper_1.defaultRenderConfig,
            ...renderConfig,
        };
        // the super class creates a component aka form which is what we pass in this extended implmentation
        this.fileName = `${this.component.name}.${(0, react_render_config_1.scriptKindToFileExtension)(this.renderConfig.script)}`;
        switch (component.viewConfiguration.type) {
            case 'Table':
                this.viewDefinition = (0, codegen_ui_1.generateTableDefinition)(component, dataSchema);
                // find if formatter is required
                if ((0, react_table_renderer_helper_1.needsFormatter)(component.viewConfiguration)) {
                    this.importCollection.addMappedImport(imports_1.ImportValue.FORMATTER);
                }
                break;
            case 'Collection':
                // 'Collection' type doesn't need a viewDefinition
                break;
            default:
                throw new Error(`Encountered a viewConfiguration type that is not supported.`);
        }
        this.viewComponent = component;
        this.viewMetadata = {
            id: component.id,
            name: component.name,
            fieldFormatting: {},
        };
    }
    renderComponentOnly() {
        const { printer, file } = (0, react_studio_template_renderer_helper_1.buildPrinter)(this.fileName, this.renderConfig);
        const variableStatements = this.buildVariableStatements();
        const jsx = this.renderJsx(this.viewComponent);
        const requiredDataModels = [];
        const imports = this.importCollection.buildImportStatements();
        let importsText = '';
        imports.forEach((importStatement) => {
            const result = printer.printNode(typescript_1.EmitHint.Unspecified, importStatement, file);
            importsText += result + os_1.EOL;
        });
        const wrappedFunction = this.renderFunctionWrapper(this.component.name, variableStatements, jsx, false);
        const result = printer.printNode(typescript_1.EmitHint.Unspecified, wrappedFunction, file);
        // do not produce declaration becuase it is not used
        const { componentText: compText } = (0, react_studio_template_renderer_helper_1.transpile)(result, { ...this.renderConfig, renderTypeDeclarations: false });
        const { type, model } = this.viewComponent.dataSource;
        if (type === 'DataStore' && model) {
            requiredDataModels.push(model);
            // TODO: require other models if form is handling querying relational models
        }
        return { compText, importsText, requiredDataModels };
    }
    renderComponentInternal() {
        const { printer, file } = (0, react_studio_template_renderer_helper_1.buildPrinter)(this.fileName, this.renderConfig);
        const variableStatements = this.buildVariableStatements();
        const jsx = this.renderJsx(this.viewComponent);
        const wrappedFunction = this.renderFunctionWrapper(this.component.name, variableStatements, jsx, true);
        const propsDeclaration = this.renderBindingPropsType();
        const imports = this.importCollection.buildImportStatements();
        let componentText = `/* eslint-disable */${os_1.EOL}`;
        imports.forEach((importStatement) => {
            const result = printer.printNode(typescript_1.EmitHint.Unspecified, importStatement, file);
            componentText += result + os_1.EOL;
        });
        componentText += os_1.EOL;
        componentText += utils_file_functions_1.overrideTypesString + os_1.EOL;
        propsDeclaration.forEach((typeNode) => {
            const propsPrinted = printer.printNode(typescript_1.EmitHint.Unspecified, typeNode, file);
            componentText += propsPrinted;
        });
        const result = printer.printNode(typescript_1.EmitHint.Unspecified, wrappedFunction, file);
        componentText += result;
        const { componentText: transpiledComponentText, declaration } = (0, react_studio_template_renderer_helper_1.transpile)(componentText, this.renderConfig);
        return {
            componentText: transpiledComponentText,
            declaration,
            renderComponentToFilesystem: async (outputPath) => {
                await this.renderComponentToFilesystem(transpiledComponentText)(this.fileName)(outputPath);
                if (declaration) {
                    await this.renderComponentToFilesystem(declaration)((0, react_studio_template_renderer_helper_1.getDeclarationFilename)(this.fileName))(outputPath);
                }
            },
        };
    }
    renderFunctionWrapper(componentName, variableStatements, jsx, renderExport) {
        var _a;
        const componentPropType = (0, react_component_render_helper_1.getComponentPropName)(componentName);
        const jsxStatement = typescript_1.factory.createReturnStatement(typescript_1.factory.createParenthesizedExpression(this.renderConfig.script !== typescript_1.ScriptKind.TSX
            ? jsx
            : /* add ts-ignore comment above jsx statement. Generated props are incompatible with amplify-ui props */
                (0, typescript_1.addSyntheticLeadingComment)(typescript_1.factory.createParenthesizedExpression(jsx), typescript_1.SyntaxKind.MultiLineCommentTrivia, ' @ts-ignore: TS2322 ', true)));
        const codeBlockContent = variableStatements.concat([jsxStatement]);
        const modifiers = renderExport
            ? [typescript_1.factory.createModifier(typescript_1.SyntaxKind.ExportKeyword), typescript_1.factory.createModifier(typescript_1.SyntaxKind.DefaultKeyword)]
            : [];
        const typeParameter = primitive_1.PrimitiveTypeParameter[primitive_1.Primitive[(_a = this.viewComponent) === null || _a === void 0 ? void 0 : _a.viewConfiguration.type]];
        // only use type parameter reference if one was declared
        const typeParameterReference = typeParameter && typeParameter.declaration() ? typeParameter.reference() : undefined;
        return typescript_1.factory.createFunctionDeclaration(undefined, modifiers, undefined, typescript_1.factory.createIdentifier(componentName), typeParameter ? typeParameter.declaration() : undefined, [
            typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, 'props', undefined, typescript_1.factory.createTypeReferenceNode(componentPropType, typeParameterReference), undefined),
        ], typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createQualifiedName(typescript_1.factory.createIdentifier('React'), typescript_1.factory.createIdentifier('ReactElement')), undefined), typescript_1.factory.createBlock(codeBlockContent, true));
    }
    buildVariableStatements() {
        const statements = [];
        const elements = [];
        const { type, model, predicate, sort } = this.viewComponent.dataSource;
        const itemsProp = 'itemsProp';
        const isDataStoreEnabled = type === 'DataStore' && model;
        if (isDataStoreEnabled) {
            this.importCollection.addModelImport(model);
            this.importCollection.addMappedImport(imports_1.ImportValue.USE_DATA_STORE_BINDING);
            elements.push(typescript_1.factory.createBindingElement(undefined, typescript_1.factory.createIdentifier('items'), typescript_1.factory.createIdentifier(itemsProp), undefined), typescript_1.factory.createBindingElement(undefined, undefined, typescript_1.factory.createIdentifier('predicateOverride'), undefined));
        }
        else {
            elements.push(typescript_1.factory.createBindingElement(undefined, undefined, typescript_1.factory.createIdentifier('items'), undefined));
        }
        // add base Props
        // props
        if (this.viewComponent.viewConfiguration.type === 'Table') {
            const props = [
                typescript_1.factory.createBindingElement(undefined, undefined, typescript_1.factory.createIdentifier('formatOverride'), undefined),
                typescript_1.factory.createBindingElement(undefined, undefined, typescript_1.factory.createIdentifier('highlightOnHover'), undefined),
                typescript_1.factory.createBindingElement(undefined, undefined, typescript_1.factory.createIdentifier('onRowClick'), undefined),
                typescript_1.factory.createBindingElement(undefined, undefined, typescript_1.factory.createIdentifier('disableHeaders'), undefined),
            ];
            elements.push(...props);
        }
        // get rest of props to pass to top level component
        elements.push(typescript_1.factory.createBindingElement(typescript_1.factory.createToken(typescript_1.SyntaxKind.DotDotDotToken), undefined, typescript_1.factory.createIdentifier('rest'), undefined));
        // add binding elments to statements
        statements.push(typescript_1.factory.createVariableStatement(undefined, typescript_1.factory.createVariableDeclarationList([
            typescript_1.factory.createVariableDeclaration(typescript_1.factory.createObjectBindingPattern(elements), undefined, undefined, typescript_1.factory.createIdentifier('props')),
        ], typescript_1.NodeFlags.Const)));
        if (isDataStoreEnabled) {
            /**
             * builds predicate variable
             */
            if (predicate) {
                this.importCollection.addMappedImport(imports_1.ImportValue.CREATE_DATA_STORE_PREDICATE);
                statements.push(typescript_1.factory.createVariableStatement(undefined, typescript_1.factory.createVariableDeclarationList([
                    typescript_1.factory.createVariableDeclaration((0, react_table_renderer_helper_1.getFilterName)(model), undefined, undefined, this.predicateToObjectLiteralExpression(predicate)),
                ], typescript_1.NodeFlags.Const)), typescript_1.factory.createVariableStatement(undefined, typescript_1.factory.createVariableDeclarationList([
                    typescript_1.factory.createVariableDeclaration((0, react_table_renderer_helper_1.getPredicateName)(model), undefined, undefined, typescript_1.factory.createCallExpression(typescript_1.factory.createIdentifier('createDataStorePredicate'), [typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier(model), undefined)], [typescript_1.factory.createIdentifier((0, react_table_renderer_helper_1.getFilterName)(model))])),
                ], typescript_1.NodeFlags.Const)));
            }
            /**
             * builds sort function
             */
            if (sort) {
                this.importCollection.addMappedImport(imports_1.ImportValue.SORT_DIRECTION, imports_1.ImportValue.SORT_PREDICATE);
                statements.push(typescript_1.factory.createVariableStatement(undefined, typescript_1.factory.createVariableDeclarationList([
                    typescript_1.factory.createVariableDeclaration((0, react_table_renderer_helper_1.getPaginationName)(model), undefined, undefined, typescript_1.factory.createObjectLiteralExpression([
                        typescript_1.factory.createPropertyAssignment(typescript_1.factory.createIdentifier('sort'), (0, react_studio_template_renderer_helper_1.buildSortFunction)(model, sort)),
                    ])),
                ], typescript_1.NodeFlags.Const)));
            }
            /*
            if datastore enabled
            const myViewDataStore = useDataStoreBinding({
              model: Model,
              type: 'Collection'
            }).items;
            const items = itemsProp !== undefined ? itemsProp : myViewDataStore;
      
            if custom enabled
            uses regular items array for formatting
            */
            const dsItemsName = typescript_1.factory.createIdentifier((0, react_table_renderer_helper_1.getDataStoreName)(model));
            statements.push((0, react_studio_template_renderer_helper_1.buildBaseCollectionVariableStatement)(dsItemsName, (0, react_table_renderer_helper_1.buildDataStoreCollectionCall)(model, predicate ? (0, react_table_renderer_helper_1.getPredicateName)(model) : undefined, sort ? (0, react_table_renderer_helper_1.getPaginationName)(model) : undefined)), 
            // checks to see if an override was passed
            typescript_1.factory.createVariableStatement(undefined, typescript_1.factory.createVariableDeclarationList([
                typescript_1.factory.createVariableDeclaration(typescript_1.factory.createIdentifier('items'), undefined, undefined, typescript_1.factory.createConditionalExpression(typescript_1.factory.createBinaryExpression(typescript_1.factory.createIdentifier(itemsProp), typescript_1.factory.createToken(typescript_1.SyntaxKind.ExclamationEqualsEqualsToken), typescript_1.factory.createIdentifier('undefined')), typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), typescript_1.factory.createIdentifier(itemsProp), typescript_1.factory.createToken(typescript_1.SyntaxKind.ColonToken), dsItemsName)),
            ], typescript_1.NodeFlags.Const)));
        }
        return statements;
    }
    predicateToObjectLiteralExpression(predicate) {
        return typescript_1.factory.createObjectLiteralExpression(Object.entries(predicate).map(([key, value]) => {
            return typescript_1.factory.createPropertyAssignment(typescript_1.factory.createIdentifier(key), key === 'and' || key === 'or'
                ? typescript_1.factory.createArrayLiteralExpression(value.map((pred) => this.predicateToObjectLiteralExpression(pred), false))
                : typescript_1.factory.createStringLiteral(value));
        }, false));
    }
    renderBindingPropsType() {
        const escapeHatchTypeNode = typescript_1.factory.createTypeLiteralNode([
            typescript_1.factory.createPropertySignature(undefined, typescript_1.factory.createIdentifier('overrides'), typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), typescript_1.factory.createUnionTypeNode([
                typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier('EscapeHatchProps'), undefined),
                typescript_1.factory.createKeywordTypeNode(typescript_1.SyntaxKind.UndefinedKeyword),
                typescript_1.factory.createLiteralTypeNode(typescript_1.factory.createNull()),
            ])),
            typescript_1.factory.createPropertySignature(undefined, typescript_1.factory.createIdentifier('predicateOverride'), typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), typescript_1.factory.createUnionTypeNode([
                typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier('ReturnType'), [
                    typescript_1.factory.createTypeQueryNode(typescript_1.factory.createIdentifier('createDataStorePredicate')),
                ]),
                typescript_1.factory.createKeywordTypeNode(typescript_1.SyntaxKind.UndefinedKeyword),
                typescript_1.factory.createLiteralTypeNode(typescript_1.factory.createNull()),
            ])),
        ]);
        const formPropType = (0, react_component_render_helper_1.getComponentPropName)(this.component.name);
        this.importCollection.addMappedImport(imports_1.ImportValue.CREATE_DATA_STORE_PREDICATE);
        return [
            typescript_1.factory.createTypeAliasDeclaration(undefined, [typescript_1.factory.createModifier(typescript_1.SyntaxKind.ExportKeyword)], typescript_1.factory.createIdentifier(formPropType), undefined, typescript_1.factory.createTypeReferenceNode(typescript_1.factory.createIdentifier('React.PropsWithChildren'), [
                typescript_1.factory.createIntersectionTypeNode([escapeHatchTypeNode]),
            ])),
        ];
    }
    validateSchema(component) {
        (0, codegen_ui_1.validateViewSchema)(component);
    }
}
__decorate([
    codegen_ui_1.handleCodegenErrors
], ReactViewTemplateRenderer.prototype, "renderComponentOnly", null);
__decorate([
    codegen_ui_1.handleCodegenErrors
], ReactViewTemplateRenderer.prototype, "renderComponentInternal", null);
exports.ReactViewTemplateRenderer = ReactViewTemplateRenderer;
//# sourceMappingURL=react-view-renderer.js.map