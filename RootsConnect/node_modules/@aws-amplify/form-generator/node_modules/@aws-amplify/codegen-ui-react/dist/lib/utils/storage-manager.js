"use strict";
/*
  Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.

  Licensed under the Apache License, Version 2.0 (the "License").
  You may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.processFile = void 0;
const constants_1 = require("./constants");
const processFile = async ({ file }) => {
    const fileExtension = file.name.split('.').pop();
    return file
        .arrayBuffer()
        .then((filebuffer) => window.crypto.subtle.digest(constants_1.STORAGE_FILE_ALGO_TYPE, filebuffer))
        .then((hashBuffer) => {
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        const hashHex = hashArray.map((a) => a.toString(16).padStart(2, '0')).join('');
        return { file, key: `${hashHex}.${fileExtension}` };
    });
};
exports.processFile = processFile;
//# sourceMappingURL=storage-manager.js.map