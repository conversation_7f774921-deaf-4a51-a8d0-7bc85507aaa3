"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReactUtilsStudioTemplateRenderer = void 0;
/*
  Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.

  Licensed under the Apache License, Version 2.0 (the "License").
  You may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
 */
const os_1 = require("os");
const typescript_1 = require("typescript");
const codegen_ui_1 = require("@aws-amplify/codegen-ui");
const react_render_config_1 = require("./react-render-config");
const imports_1 = require("./imports");
const react_output_manager_1 = require("./react-output-manager");
const react_studio_template_renderer_helper_1 = require("./react-studio-template-renderer-helper");
const utils_file_functions_1 = require("./utils-file-functions");
const amplify_js_versioning_1 = require("./helpers/amplify-js-versioning");
const constants_1 = require("./utils/constants");
class ReactUtilsStudioTemplateRenderer extends codegen_ui_1.StudioTemplateRenderer {
    constructor(utils, renderConfig) {
        super(utils, new react_output_manager_1.ReactOutputManager(), renderConfig);
        this.importCollection = new imports_1.ImportCollection();
        this.utils = utils;
        this.renderConfig = {
            ...react_studio_template_renderer_helper_1.defaultRenderConfig,
            ...renderConfig,
            renderTypeDeclarations: false, // Never render type declarations for index.js|ts file.
        };
        this.fileName = `utils.${(0, react_render_config_1.scriptKindToFileExtensionNonReact)(this.renderConfig.script)}`;
    }
    renderComponentInternal() {
        const { printer, file } = (0, react_studio_template_renderer_helper_1.buildPrinter)(this.fileName, this.renderConfig);
        const parsedUtils = [
            utils_file_functions_1.constantsString,
            utils_file_functions_1.amplifySymbolString,
            utils_file_functions_1.useStateMutationActionString,
            utils_file_functions_1.useNavigateActionString,
            utils_file_functions_1.findChildOverridesString,
            utils_file_functions_1.getOverridePropsString,
            utils_file_functions_1.getOverridesFromVariantsString,
            utils_file_functions_1.mergeVariantsAndOverridesString,
            utils_file_functions_1.getErrorMessageString,
            utils_file_functions_1.useTypeCastFieldsString,
            utils_file_functions_1.useDataStoreCreateActionString,
            utils_file_functions_1.useDataStoreUpdateActionString,
            utils_file_functions_1.useDataStoreDeleteActionString,
            utils_file_functions_1.createDataStorePredicateString,
            utils_file_functions_1.useDataStoreBindingString,
        ];
        if ((0, amplify_js_versioning_1.getAmplifyJSVersionToRender)(this.renderConfig.dependencies) === constants_1.AMPLIFY_JS_V6) {
            this.importCollection.addImport(imports_1.ImportSource.AMPLIFY_AUTH, imports_1.ImportValue.SIGN_OUT);
            this.importCollection.addImport(imports_1.ImportSource.AMPLIFY_AUTH, imports_1.ImportValue.FETCH_USER_ATTRIBUTES);
            this.importCollection.addImport(imports_1.ImportSource.AMPLIFY_DATASTORE_V6, imports_1.ImportValue.DATASTORE);
            this.importCollection.addImport(imports_1.ImportSource.AMPLIFY_UTILS, imports_1.ImportValue.HUB);
            parsedUtils.push(utils_file_functions_1.useAuthSignOutActionStringV6);
            parsedUtils.push(utils_file_functions_1.useAuthString);
        }
        else {
            this.importCollection.addMappedImport(imports_1.ImportValue.HUB, imports_1.ImportValue.DATASTORE, imports_1.ImportValue.AUTH);
            parsedUtils.push(utils_file_functions_1.useAuthSignOutActionString);
        }
        const utilsSet = new Set(this.utils);
        if (utilsSet.has('validation')) {
            parsedUtils.push(utils_file_functions_1.validationString);
        }
        if (utilsSet.has('formatter')) {
            parsedUtils.push(utils_file_functions_1.formatterString);
        }
        if (utilsSet.has('fetchByPath')) {
            parsedUtils.push(utils_file_functions_1.fetchByPathString);
        }
        if (utilsSet.has('processFile')) {
            parsedUtils.push(utils_file_functions_1.processFileString);
        }
        let componentText = `/* eslint-disable */${os_1.EOL}`;
        const imports = this.importCollection.buildImportStatements(false);
        imports.forEach((importStatement) => {
            const result = printer.printNode(typescript_1.EmitHint.Unspecified, importStatement, file);
            componentText += result + os_1.EOL;
        });
        componentText += os_1.EOL;
        componentText += parsedUtils.join(os_1.EOL) + os_1.EOL;
        const { componentText: transpliedText } = (0, react_studio_template_renderer_helper_1.transpile)(componentText, this.renderConfig, true);
        return {
            componentText: transpliedText,
            renderComponentToFilesystem: async (outputPath) => {
                await this.renderComponentToFilesystem(transpliedText)(this.fileName)(outputPath);
            },
        };
    }
    // no-op
    validateSchema() { }
}
exports.ReactUtilsStudioTemplateRenderer = ReactUtilsStudioTemplateRenderer;
//# sourceMappingURL=react-utils-studio-template-renderer.js.map