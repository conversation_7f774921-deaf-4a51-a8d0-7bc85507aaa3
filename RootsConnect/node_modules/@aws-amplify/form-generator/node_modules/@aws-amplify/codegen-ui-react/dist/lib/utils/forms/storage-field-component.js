"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.renderFieldWrapper = exports.renderStorageFieldComponent = exports.buildStorageManagerProcessFileVariableStatement = void 0;
/*
  Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.

  Licensed under the Apache License, Version 2.0 (the "License").
  You may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
 */
const codegen_ui_1 = require("@aws-amplify/codegen-ui");
const typescript_1 = require("typescript");
const form_renderer_helper_1 = require("../../forms/form-renderer-helper");
const event_handler_props_1 = require("../../forms/form-renderer-helper/event-handler-props");
const react_component_render_helper_1 = require("../../react-component-render-helper");
const constants_1 = require("../constants");
const helpers_1 = require("../../helpers");
const imports_1 = require("../../imports");
const fieldKeys = new Set([
    'label',
    'isRequired',
    'isReadOnly',
    'descriptiveText',
]);
const storageManagerKeys = new Set([
    'accessLevel',
    'acceptedFileTypes',
    'showThumbnails',
    'isResumable',
    'maxFileCount',
    'maxSize',
]);
function isFieldKey(key) {
    return fieldKeys.has(key);
}
function isStorageManagerKey(key) {
    return storageManagerKeys.has(key);
}
const buildStorageManagerProcessFileVariableStatement = () => {
    return typescript_1.factory.createVariableStatement([typescript_1.factory.createModifier(typescript_1.SyntaxKind.ExportKeyword)], typescript_1.factory.createVariableDeclarationList([
        typescript_1.factory.createVariableDeclaration(typescript_1.factory.createIdentifier('processFile'), undefined, undefined, typescript_1.factory.createArrowFunction([typescript_1.factory.createToken(typescript_1.SyntaxKind.AsyncKeyword)], undefined, [
            typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, typescript_1.factory.createObjectBindingPattern([
                typescript_1.factory.createBindingElement(undefined, undefined, typescript_1.factory.createIdentifier('file'), undefined),
            ]), undefined, undefined, undefined),
        ], undefined, typescript_1.factory.createToken(typescript_1.SyntaxKind.EqualsGreaterThanToken), typescript_1.factory.createBlock([
            typescript_1.factory.createVariableStatement(undefined, typescript_1.factory.createVariableDeclarationList([
                typescript_1.factory.createVariableDeclaration(typescript_1.factory.createIdentifier('fileExtension'), undefined, undefined, typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('file'), typescript_1.factory.createIdentifier('name')), typescript_1.factory.createIdentifier('split')), undefined, [typescript_1.factory.createStringLiteral('.')]), typescript_1.factory.createIdentifier('pop')), undefined, [])),
            ], typescript_1.NodeFlags.Const)),
            typescript_1.factory.createReturnStatement(typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('file'), typescript_1.factory.createIdentifier('arrayBuffer')), undefined, []), typescript_1.factory.createIdentifier('then')), undefined, [
                typescript_1.factory.createArrowFunction(undefined, undefined, [
                    typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, typescript_1.factory.createIdentifier('filebuffer'), undefined, undefined, undefined),
                ], undefined, typescript_1.factory.createToken(typescript_1.SyntaxKind.EqualsGreaterThanToken), typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('window'), typescript_1.factory.createIdentifier('crypto')), typescript_1.factory.createIdentifier('subtle')), typescript_1.factory.createIdentifier('digest')), undefined, [
                    typescript_1.factory.createStringLiteral(constants_1.STORAGE_FILE_ALGO_TYPE),
                    typescript_1.factory.createIdentifier('filebuffer'),
                ])),
            ]), typescript_1.factory.createIdentifier('then')), undefined, [
                typescript_1.factory.createArrowFunction(undefined, undefined, [
                    typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, typescript_1.factory.createIdentifier('hashBuffer'), undefined, undefined, undefined),
                ], undefined, typescript_1.factory.createToken(typescript_1.SyntaxKind.EqualsGreaterThanToken), typescript_1.factory.createBlock([
                    typescript_1.factory.createVariableStatement(undefined, typescript_1.factory.createVariableDeclarationList([
                        typescript_1.factory.createVariableDeclaration(typescript_1.factory.createIdentifier('hashArray'), undefined, undefined, typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('Array'), typescript_1.factory.createIdentifier('from')), undefined, [
                            typescript_1.factory.createNewExpression(typescript_1.factory.createIdentifier('Uint8Array'), undefined, [
                                typescript_1.factory.createIdentifier('hashBuffer'),
                            ]),
                        ])),
                    ], typescript_1.NodeFlags.Const)),
                    typescript_1.factory.createVariableStatement(undefined, typescript_1.factory.createVariableDeclarationList([
                        typescript_1.factory.createVariableDeclaration(typescript_1.factory.createIdentifier('hashHex'), undefined, undefined, typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('hashArray'), typescript_1.factory.createIdentifier('map')), undefined, [
                            typescript_1.factory.createArrowFunction(undefined, undefined, [
                                typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, typescript_1.factory.createIdentifier('a'), undefined, undefined, undefined),
                            ], undefined, typescript_1.factory.createToken(typescript_1.SyntaxKind.EqualsGreaterThanToken), typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('a'), typescript_1.factory.createIdentifier('toString')), undefined, [typescript_1.factory.createNumericLiteral('16')]), typescript_1.factory.createIdentifier('padStart')), undefined, [typescript_1.factory.createNumericLiteral('2'), typescript_1.factory.createStringLiteral('0')])),
                        ]), typescript_1.factory.createIdentifier('join')), undefined, [typescript_1.factory.createStringLiteral('')])),
                    ], typescript_1.NodeFlags.Const)),
                    typescript_1.factory.createReturnStatement(typescript_1.factory.createObjectLiteralExpression([
                        typescript_1.factory.createShorthandPropertyAssignment(typescript_1.factory.createIdentifier('file'), undefined),
                        typescript_1.factory.createPropertyAssignment(typescript_1.factory.createIdentifier('key'), typescript_1.factory.createTemplateExpression(typescript_1.factory.createTemplateHead('', ''), [
                            typescript_1.factory.createTemplateSpan(typescript_1.factory.createIdentifier('hashHex'), typescript_1.factory.createTemplateMiddle('.', '.')),
                            typescript_1.factory.createTemplateSpan(typescript_1.factory.createIdentifier('fileExtension'), typescript_1.factory.createTemplateTail('', '')),
                        ])),
                    ], false)),
                ], true)),
            ])),
        ], true))),
    ], typescript_1.NodeFlags.Const));
};
exports.buildStorageManagerProcessFileVariableStatement = buildStorageManagerProcessFileVariableStatement;
const renderStorageFieldComponent = (component, componentMetadata, fieldLabel, fieldConfigs, labelDecorator, isRequired) => {
    var _a, _b;
    const { name: componentName } = component;
    const dataTypeName = ((_a = componentMetadata.formMetadata) === null || _a === void 0 ? void 0 : _a.dataType.dataTypeName) || '';
    const lowerCaseDataTypeName = (0, helpers_1.lowerCaseFirst)(dataTypeName);
    const lowerCaseDataTypeNameRecord = `${lowerCaseDataTypeName}Record`;
    const storageManagerComponentName = typescript_1.factory.createIdentifier('StorageManager');
    const storageManagerAttributes = [];
    const fieldAttributes = [];
    if (componentMetadata.formMetadata) {
        const errorKey = componentName.split('.').length > 1 || !(0, codegen_ui_1.isValidVariableName)(componentName)
            ? typescript_1.factory.createElementAccessExpression(typescript_1.factory.createIdentifier('errors'), typescript_1.factory.createStringLiteral(componentName))
            : typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('errors'), typescript_1.factory.createIdentifier(componentName));
        if (componentMetadata.formMetadata.formActionType === 'update') {
            const defaultFileExpression = fieldConfigs[component.name].isArray
                ? typescript_1.factory.createJsxExpression(undefined, typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier(lowerCaseDataTypeNameRecord), typescript_1.factory.createIdentifier(componentName)), typescript_1.factory.createIdentifier('map')), undefined, [
                    typescript_1.factory.createParenthesizedExpression(typescript_1.factory.createArrowFunction(undefined, undefined, [
                        typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, typescript_1.factory.createIdentifier(constants_1.STORAGE_FILE_KEY), undefined, undefined, undefined),
                    ], undefined, typescript_1.factory.createToken(typescript_1.SyntaxKind.EqualsGreaterThanToken), typescript_1.factory.createParenthesizedExpression(typescript_1.factory.createObjectLiteralExpression([
                        typescript_1.factory.createShorthandPropertyAssignment(typescript_1.factory.createIdentifier(constants_1.STORAGE_FILE_KEY), undefined),
                    ], false)))),
                ]))
                : typescript_1.factory.createJsxExpression(undefined, typescript_1.factory.createArrayLiteralExpression([
                    typescript_1.factory.createObjectLiteralExpression([
                        typescript_1.factory.createPropertyAssignment(typescript_1.factory.createIdentifier('key'), typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier(lowerCaseDataTypeNameRecord), typescript_1.factory.createIdentifier(componentName))),
                    ], false),
                ], false));
            storageManagerAttributes.push(typescript_1.factory.createJsxAttribute(typescript_1.factory.createIdentifier('defaultFiles'), defaultFileExpression));
        }
        storageManagerAttributes.push((0, event_handler_props_1.buildStorageManagerOnChangeStatement)(component, fieldConfigs, 'onUploadSuccess'));
        storageManagerAttributes.push((0, event_handler_props_1.buildStorageManagerOnChangeStatement)(component, fieldConfigs, 'onFileRemove'));
        storageManagerAttributes.push(typescript_1.factory.createJsxAttribute(typescript_1.factory.createIdentifier('processFile'), typescript_1.factory.createJsxExpression(undefined, typescript_1.factory.createIdentifier(imports_1.ImportValue.PROCESS_FILE))));
        fieldAttributes.push(typescript_1.factory.createJsxAttribute(typescript_1.factory.createIdentifier('errorMessage'), typescript_1.factory.createJsxExpression(undefined, typescript_1.factory.createPropertyAccessChain(errorKey, typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionDotToken), typescript_1.factory.createIdentifier('errorMessage')))), typescript_1.factory.createJsxAttribute(typescript_1.factory.createIdentifier('hasError'), typescript_1.factory.createJsxExpression(undefined, typescript_1.factory.createPropertyAccessChain(errorKey, typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionDotToken), typescript_1.factory.createIdentifier('hasError')))));
    }
    Object.entries(component.properties).forEach(([key, value]) => {
        if (isFieldKey(key)) {
            if ((key === 'label' && labelDecorator && labelDecorator === 'required' && isRequired) ||
                (labelDecorator === 'optional' && !isRequired && 'value' in value)) {
                fieldAttributes.push((0, form_renderer_helper_1.getDecoratedLabel)('label', fieldLabel, labelDecorator));
            }
            else {
                fieldAttributes.push(typescript_1.factory.createJsxAttribute(typescript_1.factory.createIdentifier(key), typescript_1.factory.createJsxExpression(undefined, (0, react_component_render_helper_1.propertyToExpression)(componentMetadata, value))));
            }
        }
        if (isStorageManagerKey(key)) {
            let storageManagerValue = value;
            if (key === 'maxFileCount' && !fieldConfigs[componentName].isArray) {
                storageManagerValue = { ...value, value: 1 };
            }
            storageManagerAttributes.push(typescript_1.factory.createJsxAttribute(typescript_1.factory.createIdentifier(key), typescript_1.factory.createJsxExpression(undefined, (0, react_component_render_helper_1.propertyToExpression)(componentMetadata, storageManagerValue))));
        }
    });
    storageManagerAttributes.push(typescript_1.factory.createJsxSpreadAttribute(typescript_1.factory.createCallExpression(typescript_1.factory.createIdentifier('getOverrideProps'), undefined, [
        typescript_1.factory.createIdentifier('overrides'),
        typescript_1.factory.createStringLiteral(componentName),
    ])));
    const storageManager = typescript_1.factory.createJsxElement(typescript_1.factory.createJsxOpeningElement(storageManagerComponentName, undefined, typescript_1.factory.createJsxAttributes(storageManagerAttributes)), [], typescript_1.factory.createJsxClosingElement(storageManagerComponentName));
    const wrappedStorageManagerBlock = typescript_1.factory.createJsxExpression(undefined, typescript_1.factory.createBinaryExpression(typescript_1.factory.createIdentifier(lowerCaseDataTypeNameRecord), typescript_1.factory.createToken(typescript_1.SyntaxKind.AmpersandAmpersandToken), typescript_1.factory.createParenthesizedExpression(storageManager)));
    return (0, exports.renderFieldWrapper)(fieldAttributes, ((_b = componentMetadata.formMetadata) === null || _b === void 0 ? void 0 : _b.formActionType) === 'update' ? wrappedStorageManagerBlock : storageManager);
};
exports.renderStorageFieldComponent = renderStorageFieldComponent;
const renderFieldWrapper = (attributes, storageManagerComponent) => {
    const storageManagerComponentName = typescript_1.factory.createIdentifier('Field');
    return typescript_1.factory.createJsxElement(typescript_1.factory.createJsxOpeningElement(storageManagerComponentName, undefined, typescript_1.factory.createJsxAttributes(attributes)), [storageManagerComponent], typescript_1.factory.createJsxClosingElement(storageManagerComponentName));
};
exports.renderFieldWrapper = renderFieldWrapper;
//# sourceMappingURL=storage-field-component.js.map