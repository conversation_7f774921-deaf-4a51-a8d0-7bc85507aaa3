{"version": 3, "file": "addSchemaToClient.mjs", "sources": ["../../../src/runtime/addSchemaToClient.ts"], "sourcesContent": ["// Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.\n// SPDX-License-Identifier: Apache-2.0\nimport { generateConversationsProperty, generateCustomQueriesProperty, generateCustomMutationsProperty, generateCustomSubscriptionsProperty, generateEnumsProperty, generateGenerationsProperty, generateModelsProperty, upgradeClientCancellation, } from './internals';\nexport function addSchemaToClient(client, apiGraphqlConfig, getInternals) {\n    upgradeClientCancellation(client);\n    client.models = generateModelsProperty(client, apiGraphqlConfig, getInternals);\n    client.enums = generateEnumsProperty(apiGraphqlConfig);\n    client.queries = generateCustomQueriesProperty(client, apiGraphqlConfig, getInternals);\n    client.mutations = generateCustomMutationsProperty(client, apiGraphqlConfig, getInternals);\n    client.subscriptions = generateCustomSubscriptionsProperty(client, apiGraphqlConfig, getInternals);\n    client.conversations = generateConversationsProperty(client, apiGraphqlConfig, getInternals);\n    client.generations = generateGenerationsProperty(client, apiGraphqlConfig, getInternals);\n    return client;\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AAEO,SAAS,iBAAiB,CAAC,MAAM,EAAE,gBAAgB,EAAE,YAAY,EAAE;AAC1E,IAAI,yBAAyB,CAAC,MAAM,CAAC;AACrC,IAAI,MAAM,CAAC,MAAM,GAAG,sBAAsB,CAAC,MAAM,EAAE,gBAAgB,EAAE,YAAY,CAAC;AAClF,IAAI,MAAM,CAAC,KAAK,GAAG,qBAAqB,CAAC,gBAAgB,CAAC;AAC1D,IAAI,MAAM,CAAC,OAAO,GAAG,6BAA6B,CAAC,MAAM,EAAE,gBAAgB,EAAE,YAAY,CAAC;AAC1F,IAAI,MAAM,CAAC,SAAS,GAAG,+BAA+B,CAAC,MAAM,EAAE,gBAAgB,EAAE,YAAY,CAAC;AAC9F,IAAI,MAAM,CAAC,aAAa,GAAG,mCAAmC,CAAC,MAAM,EAAE,gBAAgB,EAAE,YAAY,CAAC;AACtG,IAAI,MAAM,CAAC,aAAa,GAAG,6BAA6B,CAAC,MAAM,EAAE,gBAAgB,EAAE,YAAY,CAAC;AAChG,IAAI,MAAM,CAAC,WAAW,GAAG,2BAA2B,CAAC,MAAM,EAAE,gBAAgB,EAAE,YAAY,CAAC;AAC5F,IAAI,OAAO,MAAM;AACjB;;;;"}