{"name": "@aws-amplify/data-schema", "version": "1.21.0", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/aws-amplify/amplify-data.git"}, "main": "./dist/cjs/index.js", "module": "./dist/esm/index.mjs", "types": "./dist/esm/index.d.ts", "scripts": {"build:esm-cjs": "rollup -c rollup.config.mjs", "build": "npm run clean && npm run build:esm-cjs", "build:watch": "npm run build:esm-cjs -- --watch", "clean": "<PERSON><PERSON><PERSON> dist", "docs": "api-extractor run && api-documenter markdown --input-folder temp --output-folder docs", "docs:temp": "api-extractor run && api-documenter markdown --input-folder temp --output-folder temp/docs", "check:api": "rimraf temp && npm run docs:temp && tsx ../../scripts/shallow-dir-diff.ts docs temp/docs", "lint": "eslint .", "test": "jest --coverage"}, "exports": {".": {"types": "./dist/esm/index.d.ts", "import": "./dist/esm/index.mjs", "require": "./dist/cjs/index.js", "node": "./dist/esm/index.mjs"}, "./internals": {"types": "./dist/esm/internals/index.d.ts", "import": "./dist/esm/internals/index.mjs", "require": "./dist/cjs/internals/index.js", "node": "./dist/esm/index.mjs"}, "./runtime": {"types": "./dist/esm/runtime/index.d.ts", "types@<5": "./dist/esm/runtime/index.v3.d.ts", "import": "./dist/esm/runtime/index.mjs", "require": "./dist/cjs/runtime/index.js", "node": "./dist/esm/index.mjs"}, "./package.json": "./package.json"}, "typesVersions": {">=5.0": {"internals": ["./dist/esm/internals/index.d.ts"]}}, "dependencies": {"@aws-amplify/data-schema-types": "*", "@smithy/util-base64": "^3.0.0", "@types/aws-lambda": "^8.10.134", "@types/json-schema": "^7.0.15", "rxjs": "^7.8.1"}, "devDependencies": {"@changesets/cli": "^2.27.10", "@rollup/plugin-typescript": "11.1.5", "@tsd/typescript": "^5.1.6", "@types/jest": "29.5.4", "jest": "^29.7.0", "jest-tsd": "^0.2.2", "rimraf": "^5.0.5", "rollup": "^4.22.4", "ts-jest": "^29.1.1", "turbo": "^1.10.14", "typescript": "^5.1.6"}, "files": ["src", "dist/**", "internals", "runtime", "package.json", "README.md", "CHANGELOG", "LICENSE", "NOTICE"]}