{"version": 3, "file": "generic-from-datastore.js", "sourceRoot": "", "sources": ["../../lib/generic-from-datastore.ts"], "names": [], "mappings": ";;;AA4BA,qCAA6C;AAG7C,MAAM,gBAAgB,GAAG,CACvB,KAAoD,EACoC,EAAE,CAC1F,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,IAAI,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC;AAE1D,MAAM,uBAAuB,GAAG,CAAC,KAAoD,EAAY,EAAE;IACjG,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,CAAC,gBAAgB,IAAI,KAAK,CAAC,WAAW,CAAC,EAAE;QAClE,OAAO,EAAE,CAAC;KACX;IAED,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,WAAW,CAAC,cAAc,CAAC;QACpD,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,cAAc;QAClC,CAAC,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;AACzC,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,CAAC,KAAoD,EAAY,EAAE;IACxF,MAAM,EAAE,WAAW,EAAE,GAAG,KAAK,CAAC;IAC9B,IAAI,WAAW,EAAE;QACf,MAAM,UAAU,GAAG,YAAY,IAAI,WAAW,IAAI,WAAW,CAAC,UAAU,CAAC;QACzE,MAAM,WAAW,GAAG,aAAa,IAAI,WAAW,IAAI,WAAW,CAAC,WAAW,CAAC;QAC5E,IAAI,OAAO,UAAU,KAAK,QAAQ,EAAE;YAClC,OAAO,CAAC,UAAU,CAAC,CAAC;SACrB;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE;YAC9B,OAAO,WAAW,CAAC;SACpB;KACF;IACD,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AAEF;;;;;GAKG;AACH,SAAS,sBAAsB,CAAC,SAAiB,EAAE,MAAkD;;IACnG,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IACvC,IAAI,CAAC,KAAK,EAAE;QACV,OAAO,KAAK,CAAC;KACd;IAED,IAAI,yBAAyB,GAAG,CAAC,CAAC;IAElC,MAAM,qBAAqB,GAAa,CAAC,IAAI,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;IAEzE,MAAA,KAAK,CAAC,UAAU,0CAAE,OAAO,CAAC,CAAC,SAAS,EAAE,EAAE;QACtC,IAAI,SAAS,CAAC,IAAI,KAAK,KAAK,EAAE;YAC5B,yBAAyB,IAAI,CAAC,CAAC;YAC/B,IAAI,SAAS,CAAC,UAAU,IAAI,QAAQ,IAAI,SAAS,CAAC,UAAU,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;gBAC1G,qBAAqB,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;aAC5D;SACF;IACH,CAAC,CAAC,CAAC;IAEH,qBAAqB;IACrB,IAAI,yBAAyB,KAAK,CAAC,EAAE;QACnC,OAAO,KAAK,CAAC;KACd;IAED,MAAM,gBAAgB,GAA8D,EAAE,CAAC;IACvF,IAAI,gBAAgB,GAAG,IAAI,CAAC;IAE5B,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,KAA8D,EAAE,EAAE;QACtG,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,GAAG,KAAK,CAAC;QAC5B,IAAI,gBAAgB,CAAC,KAAK,CAAC,EAAE;YAC3B,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;SAC9B;aAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YAChD,gBAAgB,GAAG,KAAK,CAAC;SAC1B;IACH,CAAC,CAAC,CAAC;IAEH,qCAAqC;IACrC,IAAI,CAAC,gBAAgB,EAAE;QACrB,OAAO,KAAK,CAAC;KACd;IAED,6BAA6B;IAC7B,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;QACjC,OAAO,KAAK,CAAC;KACd;IAED,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,EAAE,UAAU,CAAC,EAAE,EAAE;;QACxD,qBAAqB;QACrB,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE;YAC1B,OAAO,KAAK,CAAC;SACd;QAED,uBAAuB;QACvB,IAAI,CAAA,MAAA,UAAU,CAAC,WAAW,0CAAE,cAAc,MAAK,YAAY,EAAE;YAC3D,OAAO,KAAK,CAAC;SACd;QACD,MAAM,YAAY,GAAG,gBAAgB,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE1F,IAAI,CAAC,YAAY,EAAE;YACjB,OAAO,KAAK,CAAC;SACd;QAED,wCAAwC;QACxC,kCAAkC;QAClC,OAAO,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,KAAoD,EAAE,EAAE;;YACtG,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAA,MAAA,KAAK,CAAC,WAAW,0CAAE,cAAc,MAAK,UAAU,EAAE;gBAChF,OAAO,KAAK,CAAC;aACd;YAED,MAAM,oBAAoB,GAAG,uBAAuB,CAAC,KAAK,CAAC,CAAC;YAC5D,OAAO,oBAAoB,CAAC,MAAM,KAAK,CAAC,IAAI,oBAAoB,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,mBAAmB,CAAC,KAAoD;IAC/E,OAAO;QACL,QAAQ,EAAE,KAAK,CAAC,IAAI;QACpB,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,UAAU;QAC5B,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,UAAU;QAC5B,OAAO,EAAE,KAAK,CAAC,OAAO;KACvB,CAAC;AACJ,CAAC;AAED,sCAAsC;AACtC,SAAS,eAAe,CACtB,MAEC,EACD,SAAiB,EACjB,SAAiB,EACjB,YAA8C;IAE9C,2CAA2C;IAC3C,IAAI,SAAS,KAAK,WAAW,EAAE;QAC7B,MAAM,IAAI,0BAAiB,CAAC,gCAAgC,CAAC,CAAC;KAC/D;IACD,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;QACtB,MAAM,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;KACxB;IAED,MAAM,oBAAoB,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,CAAC;IAE1D,MAAM,cAAc,GAClB,oBAAoB,IAAI,gBAAgB,IAAI,oBAAoB,IAAI,oBAAoB,CAAC,cAAc,CAAC;IAC1G,IAAI,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,IAAI,MAAK,SAAS,IAAI,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,IAAI,MAAK,YAAY,EAAE;QAC3E,iEAAiE;QACjE,IAAI,cAAc,EAAE;YAClB,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC;SACpC;QACD,IAAI,CAAA,oBAAoB,aAApB,oBAAoB,uBAApB,oBAAoB,CAAE,IAAI,MAAK,YAAY,EAAE;YAC/C,YAAY,CAAC,IAAI,GAAG,YAAY,CAAC;SAClC;KACF;IACD,MAAM,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,GAAG,YAAY,CAAC;AAC9C,CAAC;AACD,qCAAqC;AAErC,gCAAgC;AAChC,mFAAmF;AACnF,SAAS,cAAc,CAAC,EAAE,KAAK,EAAkD;;IAC/E,MAAM,iBAAiB,GAAG,MAAA,MAAA,MAAA,KAAK,CAAC,UAAU,0CAAE,IAAI,CAC9C,CAAC,IAAI,EAAE,EAAE,CACP,IAAI,CAAC,IAAI,KAAK,KAAK;QACnB,CAAC,IAAI,CAAC,UAAU,KAAK,SAAS;YAC5B,gFAAgF;YAChF,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CACpD,0CAAE,UAAU,0CAAE,MAAM,CAAC;IAEtB,OAAO,iBAAiB,IAAI,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACxH,CAAC;AAED,SAAgB,uBAAuB,CACrC,eAA2D;IAE3D,MAAM,aAAa,GAAsB;QACvC,cAAc,EAAE,WAAW;QAC3B,MAAM,EAAE,EAAE;QACV,KAAK,EAAE,EAAE;QACT,SAAS,EAAE,EAAE;KACd,CAAC;IAEF,MAAM,+BAA+B,GAEjC,EAAE,CAAC;IAEP,MAAM,cAAc,GAAa,EAAE,CAAC;IAEpC,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,KAA0C,EAAE,EAAE;QAC3F,MAAM,aAAa,GAA8C,EAAE,CAAC;QAEpE,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,KAAoD,EAAE,EAAE;;YAC3F,MAAM,YAAY,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAEhD,uBAAuB;YACvB,IAAI,gBAAgB,CAAC,KAAK,CAAC,EAAE;gBAC3B,IAAI,KAAK,CAAC,WAAW,EAAE;oBACrB,MAAM,gBAAgB,GAAG,KAAK,CAAC,WAAW,CAAC,cAAc,CAAC;oBAE1D,IAAI,gBAAgB,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;oBACxC,IAAI,oBAAoB,CAAC;oBACzB,IAAI,oBAAoB,CAAC;oBACzB,IAAI,iBAA0D,CAAC;oBAE/D,IAAI,gBAAgB,KAAK,UAAU,IAAI,gBAAgB,IAAI,KAAK,CAAC,WAAW,EAAE;wBAC5E,kDAAkD;wBAClD,iDAAiD;wBACjD,MAAM,6BAA6B,GAAgB,IAAI,GAAG,EAAE,CAAC;wBAE7D,MAAM,eAAe,GAAG,eAAe,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;wBACjE,MAAM,oBAAoB,GAAG,uBAAuB,CAAC,KAAK,CAAC,CAAC;wBAC5D,IAAI,wBAAwB,GAAG,IAAI,CAAC;wBAEpC,oBAAoB,CAAC,OAAO,CAAC,CAAC,mBAAmB,EAAE,EAAE;4BACnD,MAAM,eAAe,GAAG,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,MAAM,CAAC,mBAAmB,CAAC,CAAC;4BACrE,iFAAiF;4BACjF,IAAI,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,UAAU,EAAE;gCAC/B,wBAAwB,GAAG,KAAK,CAAC;6BAClC;4BAED,+FAA+F;4BAC/F,IAAI,eAAe,IAAI,sBAAsB,CAAC,eAAe,CAAC,IAAI,EAAE,eAAe,CAAC,EAAE;gCACpF,cAAc,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gCAE1C,MAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,IAAI,CACjE,CAAC,SAAwD,EAAE,EAAE,CAC3D,SAAS,CAAC,IAAI,KAAK,mBAAmB,IAAI,gBAAgB,CAAC,SAAS,CAAC,CACxE,CAAC;gCACF,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,gBAAgB,CAAC,EAAE;oCAC1D,oBAAoB,GAAG,gBAAgB,CAAC;oCACxC,gBAAgB,GAAG,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC;oCAC/C,oBAAoB,GAAG,gBAAgB,CAAC,IAAI,CAAC;iCAC9C;gCACD,+FAA+F;6BAChG;iCAAM,IAAI,eAAe,EAAE;gCAC1B,IAAI,gBAAgB,CAAC,eAAe,CAAC,EAAE;oCACrC,6BAA6B,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;iCACxD;qCAAM;oCACL,eAAe,CAAC,+BAA+B,EAAE,gBAAgB,EAAE,mBAAmB,EAAE;wCACtF,IAAI,EAAE,SAAS;wCACf,gBAAgB,EAAE,KAAK,CAAC,IAAI;wCAC5B,sCAAsC;wCACtC,cAAc,EAAE,IAAI;qCACrB,CAAC,CAAC;iCACJ;6BACF;wBACH,CAAC,CAAC,CAAC;wBAEH,MAAM,iCAAiC,GAAG,MAAM,CAAC,OAAO,CAAC,MAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,MAAM,mCAAI,EAAE,CAAC,CAAC,IAAI,CAC1F,CAAC,CAAC,EAAE,CAAC,CAA0D,EAAE,EAAE,WACjE,OAAA,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,IAAI,CAAA,MAAA,CAAC,CAAC,WAAW,0CAAE,cAAc,MAAK,YAAY,CAAA,EAAA,CACvG,CAAC;wBAEF,IAAI,iCAAiC,IAAI,iCAAiC,CAAC,CAAC,CAAC,CAAC,UAAU,EAAE;4BACxF,wBAAwB,GAAG,KAAK,CAAC;yBAClC;wBAED,iBAAiB,GAAG;4BAClB,IAAI,EAAE,gBAAgB;4BACtB,wBAAwB;4BACxB,gBAAgB;4BAChB,kBAAkB,EAAE,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,6BAA6B,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;4BAC7F,oBAAoB;4BACpB,oBAAoB;yBACrB,CAAC;wBAEF,IAAI,iCAAiC,EAAE;4BACrC,MAAM,CAAC,cAAc,CAAC,GAAG,iCAAiC,CAAC;4BAC3D,iBAAiB,CAAC,4BAA4B,GAAG,cAAc,CAAC;yBACjE;qBACF;oBAED,IAAI,gBAAgB,KAAK,SAAS,IAAI,gBAAgB,KAAK,YAAY,EAAE;wBACvE,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;wBAC1C,MAAM,gBAAgB,GAAa,EAAE,CAAC;wBACtC,oEAAoE;wBACpE,IAAI,WAAW,EAAE;4BACf,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gCACjC,eAAe,CAAC,+BAA+B,EAAE,KAAK,CAAC,IAAI,EAAE,UAAU,EAAE;oCACvE,IAAI,EAAE,gBAAgB;oCACtB,gBAAgB;iCACjB,CAAC,CAAC;gCACH,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;4BACpC,CAAC,CAAC,CAAC;yBACJ;wBACD,iBAAiB,GAAG;4BAClB,IAAI,EAAE,gBAAgB;4BACtB,gBAAgB;4BAChB,gBAAgB,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,SAAS;yBACzE,CAAC;qBACH;oBAED,YAAY,CAAC,YAAY,GAAG,iBAAiB,CAAC;iBAC/C;aACF;YAED,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC;QAC3C,CAAC,CAAC,CAAC;QAEH,aAAa,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,cAAc,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;IACvG,CAAC,CAAC,CAAC;IAEH,MAAM,CAAC,OAAO,CAAC,+BAA+B,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,EAAE,MAAM,CAAC,EAAE,EAAE;QAC9E,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,EAAE,YAAY,CAAC,EAAE,EAAE;;YAC3D,MAAM,KAAK,GAAG,MAAA,aAAa,CAAC,MAAM,CAAC,SAAS,CAAC,0CAAE,MAAM,CAAC,SAAS,CAAC,CAAC;YACjE,IAAI,KAAK,EAAE;gBACT,KAAK,CAAC,YAAY,GAAG,YAAY,CAAC;aACnC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,cAAc,CAAC,OAAO,CAAC,CAAC,aAAa,EAAE,EAAE;QACvC,MAAM,KAAK,GAAG,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QAClD,IAAI,KAAK,EAAE;YACT,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;SAC1B;IACH,CAAC,CAAC,CAAC;IAEH,aAAa,CAAC,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC;IAE5C,IAAI,eAAe,CAAC,SAAS,EAAE;QAC7B,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,QAAmD,EAAE,EAAE;YACvG,MAAM,aAAa,GAA8C,EAAE,CAAC;YACpE,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,CAAC,KAAoD,EAAE,EAAE;gBAC9F,MAAM,YAAY,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;gBAChD,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC;YAC3C,CAAC,CAAC,CAAC;YACH,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,EAAE,aAAa,EAAE,CAAC;QACrE,CAAC,CAAC,CAAC;KACJ;IAED,OAAO,aAAa,CAAC;AACvB,CAAC;AAhKD,0DAgKC"}