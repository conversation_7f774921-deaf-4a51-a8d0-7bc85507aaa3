{"version": 3, "file": "form-component-metadata.js", "sourceRoot": "", "sources": ["../../../lib/utils/form-component-metadata.ts"], "names": [], "mappings": ";;;AAAA;;;;;;;;;;;;;;GAcG;AACH,6CAAwC;AAWxC,qDAAiD;AAE1C,MAAM,qBAAqB,GAAG,CAAC,QAAgB,EAAE,EAAE;IACxD,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACnF,CAAC,CAAC;AAFW,QAAA,qBAAqB,yBAEhC;AAEF;;;GAGG;AACI,MAAM,mBAAmB,GAAG,CAAC,KAAa,EAAE,aAA2B,EAAW,EAAE;IACzF,MAAM,QAAQ,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAClE,OAAO,6BAA6B,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC;AAC/D,CAAC,CAAC;AAHW,QAAA,mBAAmB,uBAG9B;AAEK,MAAM,eAAe,GAAG,CAAC,KAAa,EAAU,EAAE,CAAC,IAAA,uBAAS,EAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,EAAE,GAAG,CAAC,CAAC,CAAC;AAAzG,QAAA,eAAe,mBAA0F;AAEtH,SAAS,cAAc,CAAC,OAA8B;IACpD,OAAO,OAAO,CAAC,aAAa,KAAK,MAAM,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS,IAAI,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;AACxH,CAAC;AAED,2CAA2C;AAC3C,SAAgB,uBAAuB,CAAC,IAAY,EAAE,mBAAgC;IACpF,IAAI,kBAAkB,GAAG,IAAA,2BAAmB,EAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAA,uBAAe,EAAC,IAAI,CAAC,CAAC;IACvG,IAAI,KAAK,GAAG,CAAC,CAAC;IACd,IAAI,mBAAmB,CAAC,GAAG,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;QACpF,IAAI,kBAAkB,GAAG,kBAAkB,GAAG,KAAK,CAAC;QACpD,OAAO,mBAAmB,CAAC,GAAG,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC,EAAE;YAChE,KAAK,IAAI,CAAC,CAAC;YACX,kBAAkB,GAAG,kBAAkB,GAAG,KAAK,CAAC;SACjD;QACD,kBAAkB,GAAG,kBAAkB,CAAC;KACzC;IACD,mBAAmB,CAAC,GAAG,CAAC,kBAAkB,CAAC,WAAW,EAAE,CAAC,CAAC;IAC1D,OAAO,kBAAkB,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,kBAAkB,CAAC;AACzE,CAAC;AAbD,0DAaC;AAEM,MAAM,eAAe,GAAG,CAAC,IAAgB,EAAE,cAA8B,EAAgB,EAAE;IAChG,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAS,8BAAa,CAAC,CAAC;IAC3D,OAAO;QACL,EAAE,EAAE,IAAI,CAAC,EAAE;QACX,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,cAAc,EAAE,IAAI,CAAC,cAAc;QACnC,QAAQ,EAAE,IAAI,CAAC,QAAQ;QACvB,aAAa,EAAE,cAAc,CAAC,IAAI,CAAC,WAAW;QAC9C,cAAc,EAAE,cAAc,CAAC,IAAI,CAAC,cAAc;QAClD,YAAY,EAAE,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,MAAM,CAC1D,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,EAAE;YAC3B,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;gBAC5B,OAAO,OAAO,CAAC;aAChB;YAED,MAAM,cAAc,GAAG,OAAO,CAAC;YAC/B,MAAM,QAAQ,GAAwB;gBACpC,eAAe,EAAE,EAAE;gBACnB,aAAa,EAAE,OAAO,CAAC,aAAa;gBACpC,uBAAuB,EAAE,yBAAyB,IAAI,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC,SAAS;gBAC3G,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,YAAY,EAAE,OAAO,CAAC,YAAY;aACnC,CAAC;YACF,IAAI,aAAa,IAAI,OAAO,IAAI,OAAO,CAAC,WAAW,EAAE;gBACnD,QAAQ,CAAC,eAAe,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAA+B,CAAC,UAAU,EAAE,EAAE;oBAC9F,MAAM,iBAAiB,GAAG,UAAU,CAAC;oBACrC,OAAO,iBAAiB,CAAC,SAAS,CAAC;oBACnC,OAAO,iBAAiB,CAAC;gBAC3B,CAAC,CAAC,CAAC;aACJ;YAED,IAAI,eAAe,IAAI,OAAO,EAAE;gBAC9B,QAAQ,CAAC,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;aAChD;YAED,kDAAkD;YAClD,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,KAAK,WAAW,EAAE;gBAChD,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;aACrD;YACD,MAAM,kBAAkB,GAAG,uBAAuB,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;YAC9E,IAAI,kBAAkB,EAAE;gBACtB,QAAQ,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;aAClD;YAED,cAAc,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;YAChC,OAAO,cAAc,CAAC;QACxB,CAAC,EACD,EAAE,CACH;KACF,CAAC;AACJ,CAAC,CAAC;AAnDW,QAAA,eAAe,mBAmD1B"}