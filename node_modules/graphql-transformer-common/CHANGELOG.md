# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

# [4.34.0](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.34.0) (2025-04-03)

### Features

- upgrade lambda runtimes to node 22 ([#3232](https://github.com/aws-amplify/amplify-category-api/issues/3232)) ([ea4a75a](https://github.com/aws-amplify/amplify-category-api/commit/ea4a75a995b73037b9b89a95b2be8849f4845cfd))

# [4.33.0](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.33.0) (2025-03-06)

### Features

- gen 1 migrations ([#3199](https://github.com/aws-amplify/amplify-category-api/issues/3199)) ([22bc2d8](https://github.com/aws-amplify/amplify-category-api/commit/22bc2d8e2e795fc892ac88da6ee259b4459ded4f)), closes [#3183](https://github.com/aws-amplify/amplify-category-api/issues/3183)

## [4.32.1](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.32.1) (2025-02-22)

### Reverts

- Revert "Gen1 migration" (#3183) ([6352c5a](https://github.com/aws-amplify/amplify-category-api/commit/6352c5ad3f3e6ddaba6b699f501eb7ad5989db4a)), closes [#3183](https://github.com/aws-amplify/amplify-category-api/issues/3183) [#3084](https://github.com/aws-amplify/amplify-category-api/issues/3084)

# [4.32.0](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.32.0) (2025-02-20)

### Features

- add enableGen2Migration feature flag ([#3084](https://github.com/aws-amplify/amplify-category-api/issues/3084)) ([7263b2c](https://github.com/aws-amplify/amplify-category-api/commit/7263b2c561586e5f7107800a915e4d83d7a81374))

## [4.31.1](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.31.1) (2024-07-02)

### Bug Fixes

- **amplify-category-api:** ssl cert api name change ([#2680](https://github.com/aws-amplify/amplify-category-api/issues/2680)) ([74e7d06](https://github.com/aws-amplify/amplify-category-api/commit/74e7d06f63cd9d865d01aa46be4ae7a420fc3c2f))

# [4.31.0](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.31.0) (2024-06-25)

### Features

- **generate-schema:** custom ssl cert support to generate schema command ([#2615](https://github.com/aws-amplify/amplify-category-api/issues/2615)) ([390887f](https://github.com/aws-amplify/amplify-category-api/commit/390887ff4467baca9dad8f70071442b95bb04cf9))

## [4.30.1](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.30.1) (2024-04-26)

**Note:** Version bump only for package graphql-transformer-common

# [4.30.0](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.30.0) (2024-04-11)

### Bug Fixes

- **api:** add identifier to generated ts data schema ([#2440](https://github.com/aws-amplify/amplify-category-api/issues/2440)) ([aa7de30](https://github.com/aws-amplify/amplify-category-api/commit/aa7de30e4abb49e2684477607b3cadbe2fbc4e20))

### Features

- Fetch SNS topic ARN from SQL manifest ([#2345](https://github.com/aws-amplify/amplify-category-api/issues/2345)) ([fca256e](https://github.com/aws-amplify/amplify-category-api/commit/fca256e7cabf5af838b28b26c4ae0c3c8b1583eb))

# [4.29.0](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.29.0) (2024-03-28)

### Features

- **api:** auto detect vpc configuration on generate typescript database schema ([#2376](https://github.com/aws-amplify/amplify-category-api/issues/2376)) ([ef6f53c](https://github.com/aws-amplify/amplify-category-api/commit/ef6f53c04bbcc50921bf20a937d1332d4597a3fa))

## [4.28.1](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.28.1) (2024-02-28)

**Note:** Version bump only for package graphql-transformer-common

# [4.28.0](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.28.0) (2024-02-05)

### Features

- **api:** generate typescript data schema from sql schema internal representation ([#2225](https://github.com/aws-amplify/amplify-category-api/issues/2225)) ([460b077](https://github.com/aws-amplify/amplify-category-api/commit/460b0776af82932d2b42a2b7720b49b91fdbd45c))

## [4.27.1](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.27.1) (2023-12-18)

**Note:** Version bump only for package graphql-transformer-common

# [4.27.0](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.27.0) (2023-12-06)

### Features

- combine heterogeneous data sources ([#2109](https://github.com/aws-amplify/amplify-category-api/issues/2109)) ([fd58bb5](https://github.com/aws-amplify/amplify-category-api/commit/fd58bb5af4249220d17c9751acf677955aed74ea))

## [4.26.2](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.26.2) (2023-11-18)

### Bug Fixes

- regionalize lambda layer patching SNS topics ([#2079](https://github.com/aws-amplify/amplify-category-api/issues/2079)) ([6006c86](https://github.com/aws-amplify/amplify-category-api/commit/6006c86cd4ee624b24c184fab523fcdcdb38be63))

## [4.26.1](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.26.1) (2023-11-16)

### Bug Fixes

- Rename VPC Endpoint CDK prefix ([#2072](https://github.com/aws-amplify/amplify-category-api/issues/2072)) ([00824c1](https://github.com/aws-amplify/amplify-category-api/commit/00824c137a07fd04d325e02465ca6be3805f78c2))

# [4.26.0](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.26.0) (2023-11-15)

### Bug Fixes

- Change 'rds' to 'sql' in public-facing symbols ([#2069](https://github.com/aws-amplify/amplify-category-api/issues/2069)) ([ff374dd](https://github.com/aws-amplify/amplify-category-api/commit/ff374dd8398d3f1138a31669b1a5962122039437))
- handle field names starting with number ([2e9de1b](https://github.com/aws-amplify/amplify-category-api/commit/2e9de1b061ac212fad23b133ada44bfaadb1a2ff))

### Features

- add managed table support in API construct ([#2024](https://github.com/aws-amplify/amplify-category-api/issues/2024)) ([96a0d94](https://github.com/aws-amplify/amplify-category-api/commit/96a0d94fa872a5329da120f53be139833449b815)), closes [#1849](https://github.com/aws-amplify/amplify-category-api/issues/1849) [#1903](https://github.com/aws-amplify/amplify-category-api/issues/1903) [#1940](https://github.com/aws-amplify/amplify-category-api/issues/1940) [#1971](https://github.com/aws-amplify/amplify-category-api/issues/1971) [#1973](https://github.com/aws-amplify/amplify-category-api/issues/1973)
- **api:** add arrays and objects support for rds datasource ([cbfb017](https://github.com/aws-amplify/amplify-category-api/commit/cbfb017029e45c6e7cb8fea4250794d02afff4ca))
- infer and preserve field mappings in schema ([4f8e7f4](https://github.com/aws-amplify/amplify-category-api/commit/4f8e7f43e6ba3e64702baffe60b8b4870d37ffb6))
- sql lambda provisioned concurrency ([#2055](https://github.com/aws-amplify/amplify-category-api/issues/2055)) ([d8c5bf0](https://github.com/aws-amplify/amplify-category-api/commit/d8c5bf0b7df3cdd1ad499380d24fe49a61acbc7e))

## [4.25.1](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.25.1) (2023-08-30)

**Note:** Version bump only for package graphql-transformer-common

# [4.25.0](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.25.0) (2023-07-21)

### Features

- **graphql:** patching rds lambda layer ([a751fcb](https://github.com/aws-amplify/amplify-category-api/commit/a751fcbe75daf1fd8a1ce37b97379ad6ca3d6cec))

## [4.24.8](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.24.8) (2023-07-17)

**Note:** Version bump only for package graphql-transformer-common

## [4.24.7](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.24.7) (2023-06-29)

**Note:** Version bump only for package graphql-transformer-common

## [4.24.6](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.24.6) (2023-05-17)

**Note:** Version bump only for package graphql-transformer-common

## [4.24.5](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.24.5) (2023-03-01)

**Note:** Version bump only for package graphql-transformer-common

## [4.24.5-beta.2](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.24.5-beta.2) (2023-02-21)

**Note:** Version bump only for package graphql-transformer-common

## [4.24.5-beta.1](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.24.5-beta.1) (2023-02-15)

**Note:** Version bump only for package graphql-transformer-common

## [4.24.5-beta.0](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.24.5-beta.0) (2023-02-03)

**Note:** Version bump only for package graphql-transformer-common

## [4.24.4](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.24.4) (2023-01-26)

**Note:** Version bump only for package graphql-transformer-common

## [4.24.3](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.24.3) (2023-01-12)

**Note:** Version bump only for package graphql-transformer-common

## [4.24.2](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.24.2) (2023-01-12)

**Note:** Version bump only for package graphql-transformer-common

## [4.24.1](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.24.1) (2022-12-03)

**Note:** Version bump only for package graphql-transformer-common

# [4.24.0](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.24.0) (2022-07-01)

### Features

- **graphql:** add runtime filtering support for subscriptions ([#551](https://github.com/aws-amplify/amplify-category-api/issues/551)) ([0a24bb0](https://github.com/aws-amplify/amplify-category-api/commit/0a24bb0444ecc0947218db41094ab4ef4f0e2948))

## [4.23.3](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.23.3) (2022-06-23)

**Note:** Version bump only for package graphql-transformer-common

## [4.23.2](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.23.2) (2022-06-07)

**Note:** Version bump only for package graphql-transformer-common

## [4.23.1](https://github.com/aws-amplify/amplify-category-api/compare/<EMAIL>-transformer-common@4.23.1) (2022-05-31)

**Note:** Version bump only for package graphql-transformer-common

# [4.23.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.23.0) (2022-01-31)

### Features

- `[@maps](https://github.com/maps)To` directive to enable renaming models while retaining data ([#9340](https://github.com/aws-amplify/amplify-cli/issues/9340)) ([aedf45d](https://github.com/aws-amplify/amplify-cli/commit/aedf45d9237812d71bb8b56164efe0222ad3d534))

## [4.22.5](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.22.5) (2022-01-13)

### Bug Fixes

- clean up missing and unused GraphQL v1 dependencies ([#9496](https://github.com/aws-amplify/amplify-cli/issues/9496)) ([fe8201b](https://github.com/aws-amplify/amplify-cli/commit/fe8201be17f42db233fce0bb366ff4d0c8358ec0))

## [4.22.4](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.22.4) (2022-01-10)

## 7.6.7 (2022-01-10)

**Note:** Version bump only for package graphql-transformer-common

## [4.22.2](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.22.2) (2021-11-17)

### Bug Fixes

- append apiKey if global auth is enabled and its not default auth ([#8843](https://github.com/aws-amplify/amplify-cli/issues/8843)) ([3aadcde](https://github.com/aws-amplify/amplify-cli/commit/3aadcde2225f0ede5c5d94c2a4cd9d1afece5288))

## [4.22.1](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.22.1) (2021-11-15)

**Note:** Version bump only for package graphql-transformer-common

# [4.20.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.20.0) (2021-11-11)

### Bug Fixes

- [@function](https://github.com/function) vNext payload, remove unused code, and update common mapping tempalte function ([#8462](https://github.com/aws-amplify/amplify-cli/issues/8462)) ([24d0de9](https://github.com/aws-amplify/amplify-cli/commit/24d0de97a1bfacc3983e5b11a7582c9500759adc))
- auth on getting related model name and searchablevNext e2e ([#8455](https://github.com/aws-amplify/amplify-cli/issues/8455)) ([8536dd3](https://github.com/aws-amplify/amplify-cli/commit/8536dd3eb4cffc14602d80eea82b8b62b8227485))

### Features

- generate list types as non-null ([#8166](https://github.com/aws-amplify/amplify-cli/issues/8166)) ([93786c1](https://github.com/aws-amplify/amplify-cli/commit/93786c13ef04c72748ca32a1ef7878c0e6b5b129))

## [4.19.10](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.19.10) (2021-09-27)

### Bug Fixes

- **graphql-model-transformer:** iam role name does not exceed 64 characters ([#8244](https://github.com/aws-amplify/amplify-cli/issues/8244)) ([812a671](https://github.com/aws-amplify/amplify-cli/commit/812a67163d6dd33160bf7ace9afd538c83a7af1a))

## [4.19.9](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.19.9) (2021-09-02)

**Note:** Version bump only for package graphql-transformer-common

## [4.19.8](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.19.8) (2021-08-24)

**Note:** Version bump only for package graphql-transformer-common

## [4.19.7](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.19.7) (2021-08-06)

### Bug Fixes

- **graphql-model-transformer:** model input fields transform ([#7857](https://github.com/aws-amplify/amplify-cli/issues/7857)) ([12ff663](https://github.com/aws-amplify/amplify-cli/commit/12ff663a94a4896bd9eacef3847be15b7631d8df))

## [4.19.6](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.19.6) (2021-07-27)

**Note:** Version bump only for package graphql-transformer-common

## [4.19.5](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.19.5) (2021-07-16)

**Note:** Version bump only for package graphql-transformer-common

## [4.19.4](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.19.4) (2021-06-30)

### Bug Fixes

- **graphql-transformer-common:** improve generated graphql pluralization ([#7258](https://github.com/aws-amplify/amplify-cli/issues/7258)) ([fc3ad0d](https://github.com/aws-amplify/amplify-cli/commit/fc3ad0dd5a12a7912c59ae12024f593b4cdf7f2d)), closes [#4224](https://github.com/aws-amplify/amplify-cli/issues/4224)

## [4.19.3](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.19.3) (2021-05-03)

## 4.50.1 (2021-05-03)

**Note:** Version bump only for package graphql-transformer-common

## [4.19.2](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.19.2) (2021-05-03)

**Note:** Version bump only for package graphql-transformer-common

## [4.19.1](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.19.1) (2021-03-05)

**Note:** Version bump only for package graphql-transformer-common

# [4.19.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.19.0) (2021-02-26)

### Features

- **graphql-key-transformer:** only modify GSI sort key when present ([#6742](https://github.com/aws-amplify/amplify-cli/issues/6742)) ([7cbd396](https://github.com/aws-amplify/amplify-cli/commit/7cbd39632181a5bc323ac3ad3a835a358c74adf6))

## [4.18.2](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.18.2) (2021-02-11)

### Bug Fixes

- **graphql-key-transformer:** prevent non-scalar key fields ([#5319](https://github.com/aws-amplify/amplify-cli/issues/5319)) ([4a5b305](https://github.com/aws-amplify/amplify-cli/commit/4a5b305dd695e61fcbc4ce0ca659b6f5a1c7e467)), closes [#5300](https://github.com/aws-amplify/amplify-cli/issues/5300)
- [#6108](https://github.com/aws-amplify/amplify-cli/issues/6108) - add proper AWSJSON mapping in generated filter input types ([#6112](https://github.com/aws-amplify/amplify-cli/issues/6112)) ([743e84a](https://github.com/aws-amplify/amplify-cli/commit/743e84a9d968aab4648a12d3a19aa5ea14c4d755))

### Reverts

- Revert "Revert "Revert "fix: #6108 - add proper AWSJSON mapping in generated filter input types (#6112)" (#6158)" (#6160)" (#6183) ([a0ca94e](https://github.com/aws-amplify/amplify-cli/commit/a0ca94e5a1a848404ef3977743f19d26300a636a)), closes [#6108](https://github.com/aws-amplify/amplify-cli/issues/6108) [#6112](https://github.com/aws-amplify/amplify-cli/issues/6112) [#6158](https://github.com/aws-amplify/amplify-cli/issues/6158) [#6160](https://github.com/aws-amplify/amplify-cli/issues/6160) [#6183](https://github.com/aws-amplify/amplify-cli/issues/6183)
- Revert "fix(graphql-key-transformer): prevent non-scalar key fields (#5319)" (#6181) ([c61268d](https://github.com/aws-amplify/amplify-cli/commit/c61268d093571c906c13e7033552503b9fd83a98)), closes [#5319](https://github.com/aws-amplify/amplify-cli/issues/5319) [#6181](https://github.com/aws-amplify/amplify-cli/issues/6181)
- Revert "Revert "fix: #6108 - add proper AWSJSON mapping in generated filter input types (#6112)" (#6158)" (#6160) ([f425924](https://github.com/aws-amplify/amplify-cli/commit/f42592420dcb49640c680c5001b3026ae0129090)), closes [#6108](https://github.com/aws-amplify/amplify-cli/issues/6108) [#6112](https://github.com/aws-amplify/amplify-cli/issues/6112) [#6158](https://github.com/aws-amplify/amplify-cli/issues/6158) [#6160](https://github.com/aws-amplify/amplify-cli/issues/6160)
- Revert "fix: #6108 - add proper AWSJSON mapping in generated filter input types (#6112)" (#6158) ([9e57e4d](https://github.com/aws-amplify/amplify-cli/commit/9e57e4d8c887be8ee4119c87383c7379cec40c37)), closes [#6108](https://github.com/aws-amplify/amplify-cli/issues/6108) [#6112](https://github.com/aws-amplify/amplify-cli/issues/6112) [#6158](https://github.com/aws-amplify/amplify-cli/issues/6158)

## [4.18.1](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.18.1) (2020-11-22)

**Note:** Version bump only for package graphql-transformer-common

# [4.18.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.18.0) (2020-11-22)

### Bug Fixes

- **graphql-connection-transformer:** error if field not in relatedType ([#4481](https://github.com/aws-amplify/amplify-cli/issues/4481)) ([48e4a5e](https://github.com/aws-amplify/amplify-cli/commit/48e4a5ed8656f963d7cde49d465e4436b313e23e)), closes [#4236](https://github.com/aws-amplify/amplify-cli/issues/4236)
- **graphql-function-transformer:** add hash to function iam role name ([#3030](https://github.com/aws-amplify/amplify-cli/issues/3030)) ([e3c4a32](https://github.com/aws-amplify/amplify-cli/commit/e3c4a32135f3df6ffb06308d5250433aaf2c1ce9)), closes [#2468](https://github.com/aws-amplify/amplify-cli/issues/2468)
- **graphql-transformer-common:** support underscore names ([#5263](https://github.com/aws-amplify/amplify-cli/issues/5263)) ([cc7cae6](https://github.com/aws-amplify/amplify-cli/commit/cc7cae65188b57bb913a307186ea20458c875002)), closes [#5212](https://github.com/aws-amplify/amplify-cli/issues/5212)
- [#2296](https://github.com/aws-amplify/amplify-cli/issues/2296) [#2304](https://github.com/aws-amplify/amplify-cli/issues/2304) [#2100](https://github.com/aws-amplify/amplify-cli/issues/2100) ([#2439](https://github.com/aws-amplify/amplify-cli/issues/2439)) ([82762d6](https://github.com/aws-amplify/amplify-cli/commit/82762d6187eb2102ebd134b181622188c5632d1d))
- [#2347](https://github.com/aws-amplify/amplify-cli/issues/2347) - enum validation for key directive ([#2363](https://github.com/aws-amplify/amplify-cli/issues/2363)) ([1facade](https://github.com/aws-amplify/amplify-cli/commit/1facaded3095eaff5a015e76ca4d718b7bc3c938))
- build break, chore: typescript, lerna update ([#2640](https://github.com/aws-amplify/amplify-cli/issues/2640)) ([29fae36](https://github.com/aws-amplify/amplify-cli/commit/29fae366f4cab054feefa58c7dc733002d19570c))
- upgrade to node10 as min version for CLI ([#3128](https://github.com/aws-amplify/amplify-cli/issues/3128)) ([a0b18e0](https://github.com/aws-amplify/amplify-cli/commit/a0b18e0187a26b4ab0e6e986b0277f347e829444))

### Features

- **amplify-category-api:** change default graphql query limit to 100 ([#4124](https://github.com/aws-amplify/amplify-cli/issues/4124)) ([1a68c4d](https://github.com/aws-amplify/amplify-cli/commit/1a68c4d589e2101357dec4e980719fc547964e23))
- **amplify-category-function:** refactor to support runtime and template plugins ([#3517](https://github.com/aws-amplify/amplify-cli/issues/3517)) ([607ae21](https://github.com/aws-amplify/amplify-cli/commit/607ae21287941805f44ea8a9b78dd12d16d71f85))
- **cli:** cLI updates and new features for Amplify Console ([#2742](https://github.com/aws-amplify/amplify-cli/issues/2742)) ([0fd0dd5](https://github.com/aws-amplify/amplify-cli/commit/0fd0dd5102177766c454c8715fa5acac32385048))
- **graphql-key-transformer:** add query automatically for named keys ([#4458](https://github.com/aws-amplify/amplify-cli/issues/4458)) ([375282d](https://github.com/aws-amplify/amplify-cli/commit/375282d648cf9d096d13c7b958a0dfb7bd6d60b0))
- **graphql-key-transformer:** auto population of id and timestamp ([#4382](https://github.com/aws-amplify/amplify-cli/issues/4382)) ([6586611](https://github.com/aws-amplify/amplify-cli/commit/6586611293a07db9959247ff82f95542a239ff1f))
- adding amplify cli predictions category ([#1936](https://github.com/aws-amplify/amplify-cli/issues/1936)) ([b7b7c2c](https://github.com/aws-amplify/amplify-cli/commit/b7b7c2c1927da10f8c54f38a523021187361131c))
- conditions update ([#2789](https://github.com/aws-amplify/amplify-cli/issues/2789)) ([3fae391](https://github.com/aws-amplify/amplify-cli/commit/3fae391340d5fd151e1c43286c90142b5ab0eab0))
- implement multi-auth functionality ([#1916](https://github.com/aws-amplify/amplify-cli/issues/1916)) ([b99f58e](https://github.com/aws-amplify/amplify-cli/commit/b99f58e4a2b85cbe9f430838554ae3c277440132))
- resolver changes ([#2760](https://github.com/aws-amplify/amplify-cli/issues/2760)) ([8ce0d12](https://github.com/aws-amplify/amplify-cli/commit/8ce0d12eb1d3bd6d0132baca39b6e9daff04c39a))
- sanity check ([#1815](https://github.com/aws-amplify/amplify-cli/issues/1815)) ([54a8dbe](https://github.com/aws-amplify/amplify-cli/commit/54a8dbe8925a4e73358b03ba927267a2df328b78))

### Reverts

- add query automatically for named keys ([#4513](https://github.com/aws-amplify/amplify-cli/issues/4513)) ([50c1120](https://github.com/aws-amplify/amplify-cli/commit/50c112050645b8fd5011a1e6863d30f58e0c55cb))

## [4.17.13](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.17.13) (2020-11-08)

**Note:** Version bump only for package graphql-transformer-common

## [4.17.12](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.17.12) (2020-10-30)

**Note:** Version bump only for package graphql-transformer-common

## [4.17.11](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.17.11) (2020-10-22)

**Note:** Version bump only for package graphql-transformer-common

## [4.17.10](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.17.10) (2020-09-16)

### Bug Fixes

- **graphql-transformer-common:** support underscore names ([#5263](https://github.com/aws-amplify/amplify-cli/issues/5263)) ([cc7cae6](https://github.com/aws-amplify/amplify-cli/commit/cc7cae65188b57bb913a307186ea20458c875002)), closes [#5212](https://github.com/aws-amplify/amplify-cli/issues/5212)

## [4.17.9](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.17.9) (2020-08-31)

**Note:** Version bump only for package graphql-transformer-common

## [4.17.8](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.17.8) (2020-08-14)

**Note:** Version bump only for package graphql-transformer-common

## [4.17.7](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.17.7) (2020-07-29)

**Note:** Version bump only for package graphql-transformer-common

## [4.17.6](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.17.6) (2020-07-23)

**Note:** Version bump only for package graphql-transformer-common

## [4.17.5](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.17.5) (2020-07-18)

**Note:** Version bump only for package graphql-transformer-common

## [4.17.4](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.17.4) (2020-07-15)

### Bug Fixes

- **graphql-connection-transformer:** error if field not in relatedType ([#4481](https://github.com/aws-amplify/amplify-cli/issues/4481)) ([0a6a7ea](https://github.com/aws-amplify/amplify-cli/commit/0a6a7eabbe726d7add52b8a8811c54f7257d176f)), closes [#4236](https://github.com/aws-amplify/amplify-cli/issues/4236)

## [4.17.3](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.17.3) (2020-06-25)

### Reverts

- Revert "fix: change scope of hashed files for AppSync (#4602)" ([73aaab1](https://github.com/aws-amplify/amplify-cli/commit/73aaab1a7b1f8b2de5fa22fa1ef9aeea7de35cb4)), closes [#4602](https://github.com/aws-amplify/amplify-cli/issues/4602)

## [4.17.2](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.17.2) (2020-06-18)

### Bug Fixes

- change scope of hashed files for AppSync ([#4602](https://github.com/aws-amplify/amplify-cli/issues/4602)) ([10fa9da](https://github.com/aws-amplify/amplify-cli/commit/10fa9da646f4de755e2dc92cd4bb2a6319425d72)), closes [#4458](https://github.com/aws-amplify/amplify-cli/issues/4458)

## [4.17.1](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.17.1) (2020-06-11)

### Reverts

- add query automatically for named keys ([#4513](https://github.com/aws-amplify/amplify-cli/issues/4513)) ([6d3123b](https://github.com/aws-amplify/amplify-cli/commit/6d3123bfe3ba412d3b1af076e550e6733c988c8f))

# [4.17.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.17.0) (2020-06-10)

### Features

- **graphql-key-transformer:** add query automatically for named keys ([#4458](https://github.com/aws-amplify/amplify-cli/issues/4458)) ([3d194f8](https://github.com/aws-amplify/amplify-cli/commit/3d194f805dcbd6325ddf78155c4327dbca3e7f4a))

# [4.16.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.16.0) (2020-06-02)

### Features

- **graphql-key-transformer:** auto population of id and timestamp ([#4382](https://github.com/aws-amplify/amplify-cli/issues/4382)) ([c0a4f88](https://github.com/aws-amplify/amplify-cli/commit/c0a4f8889fc363bb9c9d08ff822c591874777f7b))

# [4.15.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.15.0) (2020-05-08)

### Features

- **amplify-category-api:** change default graphql query limit to 100 ([#4124](https://github.com/aws-amplify/amplify-cli/issues/4124)) ([1a68c4d](https://github.com/aws-amplify/amplify-cli/commit/1a68c4d589e2101357dec4e980719fc547964e23))

# [4.14.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.14.0) (2020-03-22)

### Features

- **amplify-category-function:** refactor to support runtime and template plugins ([#3517](https://github.com/aws-amplify/amplify-cli/issues/3517)) ([607ae21](https://github.com/aws-amplify/amplify-cli/commit/607ae21287941805f44ea8a9b78dd12d16d71f85))

## [4.13.3](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.13.3) (2020-02-13)

**Note:** Version bump only for package graphql-transformer-common

## [4.13.2](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.13.2) (2020-02-07)

**Note:** Version bump only for package graphql-transformer-common

## [4.13.1](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.13.1) (2020-01-24)

**Note:** Version bump only for package graphql-transformer-common

# [4.13.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.13.0) (2020-01-23)

### Bug Fixes

- **graphql-function-transformer:** add hash to function iam role name ([#3030](https://github.com/aws-amplify/amplify-cli/issues/3030)) ([e3c4a32](https://github.com/aws-amplify/amplify-cli/commit/e3c4a32135f3df6ffb06308d5250433aaf2c1ce9)), closes [#2468](https://github.com/aws-amplify/amplify-cli/issues/2468)
- upgrade to node10 as min version for CLI ([#3128](https://github.com/aws-amplify/amplify-cli/issues/3128)) ([a0b18e0](https://github.com/aws-amplify/amplify-cli/commit/a0b18e0187a26b4ab0e6e986b0277f347e829444))

### Features

- conditions update ([#2789](https://github.com/aws-amplify/amplify-cli/issues/2789)) ([3fae391](https://github.com/aws-amplify/amplify-cli/commit/3fae391340d5fd151e1c43286c90142b5ab0eab0))
- resolver changes ([#2760](https://github.com/aws-amplify/amplify-cli/issues/2760)) ([8ce0d12](https://github.com/aws-amplify/amplify-cli/commit/8ce0d12eb1d3bd6d0132baca39b6e9daff04c39a))
- **cli:** cLI updates and new features for Amplify Console ([#2742](https://github.com/aws-amplify/amplify-cli/issues/2742)) ([0fd0dd5](https://github.com/aws-amplify/amplify-cli/commit/0fd0dd5102177766c454c8715fa5acac32385048))

# [4.12.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.12.0) (2020-01-09)

### Bug Fixes

- **graphql-function-transformer:** add hash to function iam role name ([#3030](https://github.com/aws-amplify/amplify-cli/issues/3030)) ([e3c4a32](https://github.com/aws-amplify/amplify-cli/commit/e3c4a32135f3df6ffb06308d5250433aaf2c1ce9)), closes [#2468](https://github.com/aws-amplify/amplify-cli/issues/2468)
- upgrade to node10 as min version for CLI ([#3128](https://github.com/aws-amplify/amplify-cli/issues/3128)) ([a0b18e0](https://github.com/aws-amplify/amplify-cli/commit/a0b18e0187a26b4ab0e6e986b0277f347e829444))

### Features

- conditions update ([#2789](https://github.com/aws-amplify/amplify-cli/issues/2789)) ([3fae391](https://github.com/aws-amplify/amplify-cli/commit/3fae391340d5fd151e1c43286c90142b5ab0eab0))
- resolver changes ([#2760](https://github.com/aws-amplify/amplify-cli/issues/2760)) ([8ce0d12](https://github.com/aws-amplify/amplify-cli/commit/8ce0d12eb1d3bd6d0132baca39b6e9daff04c39a))
- **cli:** cLI updates and new features for Amplify Console ([#2742](https://github.com/aws-amplify/amplify-cli/issues/2742)) ([0fd0dd5](https://github.com/aws-amplify/amplify-cli/commit/0fd0dd5102177766c454c8715fa5acac32385048))

# [4.11.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.11.0) (2019-12-31)

### Bug Fixes

- **graphql-function-transformer:** add hash to function iam role name ([#3030](https://github.com/aws-amplify/amplify-cli/issues/3030)) ([e3c4a32](https://github.com/aws-amplify/amplify-cli/commit/e3c4a32135f3df6ffb06308d5250433aaf2c1ce9)), closes [#2468](https://github.com/aws-amplify/amplify-cli/issues/2468)

### Features

- conditions update ([#2789](https://github.com/aws-amplify/amplify-cli/issues/2789)) ([3fae391](https://github.com/aws-amplify/amplify-cli/commit/3fae391340d5fd151e1c43286c90142b5ab0eab0))
- resolver changes ([#2760](https://github.com/aws-amplify/amplify-cli/issues/2760)) ([8ce0d12](https://github.com/aws-amplify/amplify-cli/commit/8ce0d12eb1d3bd6d0132baca39b6e9daff04c39a))
- **cli:** cLI updates and new features for Amplify Console ([#2742](https://github.com/aws-amplify/amplify-cli/issues/2742)) ([0fd0dd5](https://github.com/aws-amplify/amplify-cli/commit/0fd0dd5102177766c454c8715fa5acac32385048))

# [4.10.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.10.0) (2019-12-28)

### Bug Fixes

- **graphql-function-transformer:** add hash to function iam role name ([#3030](https://github.com/aws-amplify/amplify-cli/issues/3030)) ([e3c4a32](https://github.com/aws-amplify/amplify-cli/commit/e3c4a32135f3df6ffb06308d5250433aaf2c1ce9)), closes [#2468](https://github.com/aws-amplify/amplify-cli/issues/2468)

### Features

- conditions update ([#2789](https://github.com/aws-amplify/amplify-cli/issues/2789)) ([3fae391](https://github.com/aws-amplify/amplify-cli/commit/3fae391340d5fd151e1c43286c90142b5ab0eab0))
- resolver changes ([#2760](https://github.com/aws-amplify/amplify-cli/issues/2760)) ([8ce0d12](https://github.com/aws-amplify/amplify-cli/commit/8ce0d12eb1d3bd6d0132baca39b6e9daff04c39a))
- **cli:** cLI updates and new features for Amplify Console ([#2742](https://github.com/aws-amplify/amplify-cli/issues/2742)) ([0fd0dd5](https://github.com/aws-amplify/amplify-cli/commit/0fd0dd5102177766c454c8715fa5acac32385048))

# [4.9.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.9.0) (2019-12-26)

### Bug Fixes

- **graphql-function-transformer:** add hash to function iam role name ([#3030](https://github.com/aws-amplify/amplify-cli/issues/3030)) ([e3c4a32](https://github.com/aws-amplify/amplify-cli/commit/e3c4a32135f3df6ffb06308d5250433aaf2c1ce9)), closes [#2468](https://github.com/aws-amplify/amplify-cli/issues/2468)

### Features

- conditions update ([#2789](https://github.com/aws-amplify/amplify-cli/issues/2789)) ([3fae391](https://github.com/aws-amplify/amplify-cli/commit/3fae391340d5fd151e1c43286c90142b5ab0eab0))
- resolver changes ([#2760](https://github.com/aws-amplify/amplify-cli/issues/2760)) ([8ce0d12](https://github.com/aws-amplify/amplify-cli/commit/8ce0d12eb1d3bd6d0132baca39b6e9daff04c39a))
- **cli:** cLI updates and new features for Amplify Console ([#2742](https://github.com/aws-amplify/amplify-cli/issues/2742)) ([0fd0dd5](https://github.com/aws-amplify/amplify-cli/commit/0fd0dd5102177766c454c8715fa5acac32385048))

# [4.8.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.8.0) (2019-12-25)

### Bug Fixes

- **graphql-function-transformer:** add hash to function iam role name ([#3030](https://github.com/aws-amplify/amplify-cli/issues/3030)) ([e3c4a32](https://github.com/aws-amplify/amplify-cli/commit/e3c4a32135f3df6ffb06308d5250433aaf2c1ce9)), closes [#2468](https://github.com/aws-amplify/amplify-cli/issues/2468)

### Features

- conditions update ([#2789](https://github.com/aws-amplify/amplify-cli/issues/2789)) ([3fae391](https://github.com/aws-amplify/amplify-cli/commit/3fae391340d5fd151e1c43286c90142b5ab0eab0))
- resolver changes ([#2760](https://github.com/aws-amplify/amplify-cli/issues/2760)) ([8ce0d12](https://github.com/aws-amplify/amplify-cli/commit/8ce0d12eb1d3bd6d0132baca39b6e9daff04c39a))
- **cli:** cLI updates and new features for Amplify Console ([#2742](https://github.com/aws-amplify/amplify-cli/issues/2742)) ([0fd0dd5](https://github.com/aws-amplify/amplify-cli/commit/0fd0dd5102177766c454c8715fa5acac32385048))

# [4.7.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.7.0) (2019-12-20)

### Bug Fixes

- **graphql-function-transformer:** add hash to function iam role name ([#3030](https://github.com/aws-amplify/amplify-cli/issues/3030)) ([e3c4a32](https://github.com/aws-amplify/amplify-cli/commit/e3c4a32135f3df6ffb06308d5250433aaf2c1ce9)), closes [#2468](https://github.com/aws-amplify/amplify-cli/issues/2468)

### Features

- conditions update ([#2789](https://github.com/aws-amplify/amplify-cli/issues/2789)) ([3fae391](https://github.com/aws-amplify/amplify-cli/commit/3fae391340d5fd151e1c43286c90142b5ab0eab0))
- resolver changes ([#2760](https://github.com/aws-amplify/amplify-cli/issues/2760)) ([8ce0d12](https://github.com/aws-amplify/amplify-cli/commit/8ce0d12eb1d3bd6d0132baca39b6e9daff04c39a))
- **cli:** cLI updates and new features for Amplify Console ([#2742](https://github.com/aws-amplify/amplify-cli/issues/2742)) ([0fd0dd5](https://github.com/aws-amplify/amplify-cli/commit/0fd0dd5102177766c454c8715fa5acac32385048))

# [4.6.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.6.0) (2019-12-10)

### Features

- conditions update ([#2789](https://github.com/aws-amplify/amplify-cli/issues/2789)) ([3fae391](https://github.com/aws-amplify/amplify-cli/commit/3fae391340d5fd151e1c43286c90142b5ab0eab0))
- resolver changes ([#2760](https://github.com/aws-amplify/amplify-cli/issues/2760)) ([8ce0d12](https://github.com/aws-amplify/amplify-cli/commit/8ce0d12eb1d3bd6d0132baca39b6e9daff04c39a))
- **cli:** cLI updates and new features for Amplify Console ([#2742](https://github.com/aws-amplify/amplify-cli/issues/2742)) ([0fd0dd5](https://github.com/aws-amplify/amplify-cli/commit/0fd0dd5102177766c454c8715fa5acac32385048))

# [4.4.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.4.0) (2019-12-03)

### Features

- conditions update ([#2789](https://github.com/aws-amplify/amplify-cli/issues/2789)) ([3fae391](https://github.com/aws-amplify/amplify-cli/commit/3fae391340d5fd151e1c43286c90142b5ab0eab0))
- resolver changes ([#2760](https://github.com/aws-amplify/amplify-cli/issues/2760)) ([8ce0d12](https://github.com/aws-amplify/amplify-cli/commit/8ce0d12eb1d3bd6d0132baca39b6e9daff04c39a))
- **cli:** cLI updates and new features for Amplify Console ([#2742](https://github.com/aws-amplify/amplify-cli/issues/2742)) ([0fd0dd5](https://github.com/aws-amplify/amplify-cli/commit/0fd0dd5102177766c454c8715fa5acac32385048))

# [4.3.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.3.0) (2019-12-01)

### Features

- conditions update ([#2789](https://github.com/aws-amplify/amplify-cli/issues/2789)) ([3fae391](https://github.com/aws-amplify/amplify-cli/commit/3fae391340d5fd151e1c43286c90142b5ab0eab0))
- resolver changes ([#2760](https://github.com/aws-amplify/amplify-cli/issues/2760)) ([8ce0d12](https://github.com/aws-amplify/amplify-cli/commit/8ce0d12eb1d3bd6d0132baca39b6e9daff04c39a))
- **cli:** cLI updates and new features for Amplify Console ([#2742](https://github.com/aws-amplify/amplify-cli/issues/2742)) ([0fd0dd5](https://github.com/aws-amplify/amplify-cli/commit/0fd0dd5102177766c454c8715fa5acac32385048))

# [4.2.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.2.0) (2019-11-27)

### Features

- conditions update ([#2789](https://github.com/aws-amplify/amplify-cli/issues/2789)) ([3fae391](https://github.com/aws-amplify/amplify-cli/commit/3fae391340d5fd151e1c43286c90142b5ab0eab0))
- resolver changes ([#2760](https://github.com/aws-amplify/amplify-cli/issues/2760)) ([8ce0d12](https://github.com/aws-amplify/amplify-cli/commit/8ce0d12eb1d3bd6d0132baca39b6e9daff04c39a))
- **cli:** cLI updates and new features for Amplify Console ([#2742](https://github.com/aws-amplify/amplify-cli/issues/2742)) ([0fd0dd5](https://github.com/aws-amplify/amplify-cli/commit/0fd0dd5102177766c454c8715fa5acac32385048))

# [4.1.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@4.1.0) (2019-11-27)

### Features

- conditions update ([#2789](https://github.com/aws-amplify/amplify-cli/issues/2789)) ([3fae391](https://github.com/aws-amplify/amplify-cli/commit/3fae391340d5fd151e1c43286c90142b5ab0eab0))
- resolver changes ([#2760](https://github.com/aws-amplify/amplify-cli/issues/2760)) ([8ce0d12](https://github.com/aws-amplify/amplify-cli/commit/8ce0d12eb1d3bd6d0132baca39b6e9daff04c39a))
- **cli:** cLI updates and new features for Amplify Console ([#2742](https://github.com/aws-amplify/amplify-cli/issues/2742)) ([0fd0dd5](https://github.com/aws-amplify/amplify-cli/commit/0fd0dd5102177766c454c8715fa5acac32385048))

# [3.14.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@3.14.0) (2019-08-30)

### Features

- adding amplify cli predictions category ([#1936](https://github.com/aws-amplify/amplify-cli/issues/1936)) ([b7b7c2c](https://github.com/aws-amplify/amplify-cli/commit/b7b7c2c))
- sanity check ([#1815](https://github.com/aws-amplify/amplify-cli/issues/1815)) ([54a8dbe](https://github.com/aws-amplify/amplify-cli/commit/54a8dbe))

# [3.13.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@3.13.0) (2019-08-28)

### Features

- adding amplify cli predictions category ([#1936](https://github.com/aws-amplify/amplify-cli/issues/1936)) ([b7b7c2c](https://github.com/aws-amplify/amplify-cli/commit/b7b7c2c))
- sanity check ([#1815](https://github.com/aws-amplify/amplify-cli/issues/1815)) ([54a8dbe](https://github.com/aws-amplify/amplify-cli/commit/54a8dbe))

# [3.12.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@3.12.0) (2019-08-13)

### Features

- adding amplify cli predictions category ([#1936](https://github.com/aws-amplify/amplify-cli/issues/1936)) ([b7b7c2c](https://github.com/aws-amplify/amplify-cli/commit/b7b7c2c))
- sanity check ([#1815](https://github.com/aws-amplify/amplify-cli/issues/1815)) ([54a8dbe](https://github.com/aws-amplify/amplify-cli/commit/54a8dbe))

# [3.11.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@3.11.0) (2019-08-07)

### Features

- adding amplify cli predictions category ([#1936](https://github.com/aws-amplify/amplify-cli/issues/1936)) ([b7b7c2c](https://github.com/aws-amplify/amplify-cli/commit/b7b7c2c))
- sanity check ([#1815](https://github.com/aws-amplify/amplify-cli/issues/1815)) ([54a8dbe](https://github.com/aws-amplify/amplify-cli/commit/54a8dbe))

# [3.10.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@3.10.0) (2019-08-02)

### Features

- adding amplify cli predictions category ([#1936](https://github.com/aws-amplify/amplify-cli/issues/1936)) ([b7b7c2c](https://github.com/aws-amplify/amplify-cli/commit/b7b7c2c))
- sanity check ([#1815](https://github.com/aws-amplify/amplify-cli/issues/1815)) ([54a8dbe](https://github.com/aws-amplify/amplify-cli/commit/54a8dbe))

# [3.9.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@3.9.0) (2019-07-31)

### Features

- adding amplify cli predictions category ([#1936](https://github.com/aws-amplify/amplify-cli/issues/1936)) ([b7b7c2c](https://github.com/aws-amplify/amplify-cli/commit/b7b7c2c))
- sanity check ([#1815](https://github.com/aws-amplify/amplify-cli/issues/1815)) ([54a8dbe](https://github.com/aws-amplify/amplify-cli/commit/54a8dbe))

## [3.8.3](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@3.8.3) (2019-07-24)

**Note:** Version bump only for package graphql-transformer-common

## [3.8.2](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@3.8.2) (2019-06-26)

### Bug Fixes

- **graphql-key-transformer:** Fix type resolve for 2 field [@key](https://github.com/key) when second field is an Enum ([#1619](https://github.com/aws-amplify/amplify-cli/issues/1619)) ([bbd82b0](https://github.com/aws-amplify/amplify-cli/commit/bbd82b0)), closes [#1572](https://github.com/aws-amplify/amplify-cli/issues/1572)

## [3.8.1](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@3.8.1) (2019-06-12)

**Note:** Version bump only for package graphql-transformer-common

# [3.8.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@3.8.0) (2019-05-29)

### Features

- feature/[@key](https://github.com/key) ([#1463](https://github.com/aws-amplify/amplify-cli/issues/1463)) ([00ed819](https://github.com/aws-amplify/amplify-cli/commit/00ed819))

# [3.7.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@3.7.0) (2019-05-21)

### Features

- **graphql-dynamodb-transformer:** always output stream arn ([df1712b](https://github.com/aws-amplify/amplify-cli/commit/df1712b)), closes [#980](https://github.com/aws-amplify/amplify-cli/issues/980)

## [3.6.2](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@3.6.2) (2019-05-17)

**Note:** Version bump only for package graphql-transformer-common

## [3.6.1](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@3.6.1) (2019-05-07)

**Note:** Version bump only for package graphql-transformer-common

# [3.6.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@3.6.0) (2019-04-16)

### Features

- **field-level-auth:** Add field level auth support via the [@auth](https://github.com/auth) directive ([#1262](https://github.com/aws-amplify/amplify-cli/issues/1262)) ([3b1c600](https://github.com/aws-amplify/amplify-cli/commit/3b1c600)), closes [#1043](https://github.com/aws-amplify/amplify-cli/issues/1043)

## [3.5.1](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@3.5.1) (2019-04-09)

**Note:** Version bump only for package graphql-transformer-common

# [3.4.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@3.4.0) (2019-04-03)

### Features

- **graphql-elasticsearch-transformer:** export domain arn and endpoint ([97b8cad](https://github.com/aws-amplify/amplify-cli/commit/97b8cad)), closes [#1047](https://github.com/aws-amplify/amplify-cli/issues/1047)

## [3.0.6](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@3.0.6) (2019-03-22)

**Note:** Version bump only for package graphql-transformer-common

## [3.0.5](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@3.0.5) (2019-02-11)

**Note:** Version bump only for package graphql-transformer-common

## [3.0.3](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@3.0.3) (2019-02-11)

**Note:** Version bump only for package graphql-transformer-common

## [3.0.3-beta.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@3.0.3-beta.0) (2019-02-11)

**Note:** Version bump only for package graphql-transformer-common

<a name="2.0.0-multienv.1"></a>

# [2.0.0-multienv.1](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@2.0.0-multienv.1) (2018-12-31)

### Bug Fixes

- update grahql transformer package versions for multienv ([8b4b2bd](https://github.com/aws-amplify/amplify-cli/commit/8b4b2bd))

<a name="1.0.34-multienv.0"></a>

## [1.0.34-multienv.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@1.0.34-multienv.0) (2018-11-16)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.33"></a>

## [1.0.33](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@1.0.33) (2018-11-09)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.33-beta.0"></a>

## [1.0.33-beta.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@1.0.33-beta.0) (2018-11-09)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.32"></a>

## [1.0.32](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@1.0.32) (2018-11-05)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.32-beta.0"></a>

## [1.0.32-beta.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@1.0.32-beta.0) (2018-11-05)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.31"></a>

## [1.0.31](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@1.0.31) (2018-11-02)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.30"></a>

## [1.0.30](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@1.0.30) (2018-11-02)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.30-beta.0"></a>

## [1.0.30-beta.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@1.0.30-beta.0) (2018-11-02)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.29"></a>

## [1.0.29](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@1.0.29) (2018-10-23)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.29-beta.0"></a>

## [1.0.29-beta.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@1.0.29-beta.0) (2018-10-23)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.28"></a>

## [1.0.28](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@1.0.28) (2018-10-18)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.28-beta.0"></a>

## [1.0.28-beta.0](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@1.0.28-beta.0) (2018-10-12)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.12"></a>

## [1.0.12](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@1.0.12) (2018-08-23)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.11"></a>

## [1.0.11](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@1.0.11) (2018-08-23)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.10"></a>

## [1.0.10](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@1.0.10) (2018-08-23)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.9"></a>

## [1.0.9](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@1.0.9) (2018-08-23)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.8"></a>

## [1.0.8](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@1.0.8) (2018-08-23)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.7"></a>

## [1.0.7](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@1.0.7) (2018-08-23)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.6"></a>

## [1.0.6](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@1.0.6) (2018-08-23)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.5"></a>

## [1.0.5](https://github.com/aws-amplify/amplify-cli/compare/<EMAIL>-transformer-common@1.0.5) (2018-08-23)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.4"></a>

## 1.0.4 (2018-08-23)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.3"></a>

## 1.0.3 (2018-08-23)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.2"></a>

## 1.0.2 (2018-08-23)

**Note:** Version bump only for package graphql-transformer-common

<a name="1.0.1"></a>

## 1.0.1 (2018-08-23)

**Note:** Version bump only for package graphql-transformer-common
