## API Report File for "graphql-transformer-common"

> Do not edit this file. It is a report generated by [API Extractor](https://api-extractor.com/).

```ts

import { ArgumentNode } from 'graphql';
import { CompoundExpressionNode } from 'graphql-mapping-template';
import { DefinitionNode } from 'graphql';
import { DirectiveNode } from 'graphql';
import { DocumentNode } from 'graphql';
import { EnumTypeDefinitionNode } from 'graphql';
import { FieldDefinitionNode } from 'graphql';
import { IfNode } from 'graphql-mapping-template';
import { InputObjectTypeDefinitionNode } from 'graphql';
import { InputValueDefinitionNode } from 'graphql';
import { ListTypeNode } from 'graphql';
import { NamedTypeNode } from 'graphql';
import { NonNullTypeNode } from 'graphql';
import { ObjectTypeDefinitionNode } from 'graphql';
import { ObjectTypeExtensionNode } from 'graphql';
import { OperationTypeDefinitionNode } from 'graphql';
import { OperationTypeNode } from 'graphql';
import { ReferenceNode } from 'graphql-mapping-template';
import { SchemaDefinitionNode } from 'graphql';
import { SetNode } from 'graphql-mapping-template';
import { TypeNode } from 'graphql';
import { UnionTypeDefinitionNode } from 'graphql';
import { ValueNode } from 'graphql';

// @public (undocumented)
export function applyCompositeKeyConditionExpression(keyNames: string[], queryExprReference: string, sortKeyArgumentName: string, sortKeyAttributeName: string): CompoundExpressionNode;

// @public (undocumented)
export function applyKeyConditionExpression(argName: string, attributeType?: 'S' | 'N' | 'B', queryExprReference?: string, sortKeyName?: string, prefixVariableName?: string): CompoundExpressionNode;

// @public (undocumented)
export function applyKeyExpressionForCompositeKey(keys: string[], attributeTypes?: ('S' | 'N' | 'B')[], queryExprReference?: string): IfNode | CompoundExpressionNode;

// Warning: (ae-forgotten-export) The symbol "ScalarMap" needs to be exported by the entry point index.d.ts
//
// @public (undocumented)
export const APPSYNC_DEFINED_SCALARS: ScalarMap;

// @public (undocumented)
export function attributeTypeFromScalar(scalar: TypeNode): 'S' | 'N';

// @public (undocumented)
export function blankObject(name: string): ObjectTypeDefinitionNode;

// @public (undocumented)
export function blankObjectExtension(name: string): ObjectTypeExtensionNode;

// @public (undocumented)
export const DEFAULT_SCALARS: ScalarMap;

// @public (undocumented)
export function defineUnionType(name: string, types?: NamedTypeNode[]): UnionTypeDefinitionNode;

// @public (undocumented)
export const directiveExists: (definition: ObjectTypeDefinitionNode, name: string) => DirectiveNode;

// @public (undocumented)
export function extendFieldWithDirectives(field: FieldDefinitionNode, directives: DirectiveNode[]): FieldDefinitionNode;

// @public (undocumented)
export function extensionWithDirectives(object: ObjectTypeExtensionNode, directives: DirectiveNode[]): ObjectTypeExtensionNode;

// @public (undocumented)
export function extensionWithFields(object: ObjectTypeExtensionNode, fields: FieldDefinitionNode[]): ObjectTypeExtensionNode;

// @public (undocumented)
export const findObjectDefinition: (document: DocumentNode, name: string) => ObjectTypeDefinitionNode | undefined;

// @public (undocumented)
export class FunctionResourceIDs {
    // (undocumented)
    static FunctionAppSyncFunctionConfigurationID(name: string, region?: string, accountId?: string): string;
    // (undocumented)
    static FunctionDataSourceID(name: string, region?: string, accountId?: string): string;
    // (undocumented)
    static FunctionIAMRoleID(name: string, region?: string, accountId?: string): string;
    // (undocumented)
    static FunctionIAMRoleName(name: string, withEnv?: boolean): string;
}

// @public (undocumented)
export function getBaseType(type: TypeNode): string;

// @public (undocumented)
export function getDirectiveArgument(directive: DirectiveNode, arg: string, dflt?: any): any;

// @public (undocumented)
export const getNonModelTypes: (document: DocumentNode) => DefinitionNode[];

// @public (undocumented)
export function graphqlName(val: string): string;

// @public (undocumented)
export class HttpResourceIDs {
    // (undocumented)
    static HttpDataSourceID(baseURL: string): string;
}

// @public (undocumented)
export const isArrayOrObject: (type: TypeNode, enums: EnumTypeDefinitionNode[]) => boolean;

// @public (undocumented)
export function isEnum(type: TypeNode, document: DocumentNode): DefinitionNode;

// @public (undocumented)
export function isListType(type: TypeNode): boolean;

// @public (undocumented)
export const isNamedType: (type: TypeNode) => boolean;

// @public (undocumented)
export const isNonModelType: (definition: DefinitionNode) => boolean;

// @public (undocumented)
export function isNonNullType(type: TypeNode): boolean;

// @public (undocumented)
export const isOfType: (type: TypeNode, name: string) => boolean;

// @public (undocumented)
export function isScalar(type: TypeNode): any;

// @public (undocumented)
export function isScalarOrEnum(type: TypeNode, enums: EnumTypeDefinitionNode[]): any;

// @public (undocumented)
export function makeArgument(name: string, value: ValueNode): ArgumentNode;

// @public (undocumented)
export function makeCompositeKeyConditionInputForKey(modelName: string, keyName: string, fields: FieldDefinitionNode[]): InputObjectTypeDefinitionNode;

// @public (undocumented)
export function makeCompositeKeyInputForKey(modelName: string, keyName: string, fields: FieldDefinitionNode[]): InputObjectTypeDefinitionNode;

// @public (undocumented)
export function makeConnectionField(fieldName: string, returnTypeName: string, args?: InputValueDefinitionNode[], directives?: DirectiveNode[]): FieldDefinitionNode;

// @public (undocumented)
export function makeDirective(name: string, args: ArgumentNode[]): DirectiveNode;

// @public (undocumented)
export function makeField(name: string, args: InputValueDefinitionNode[], type: TypeNode, directives?: DirectiveNode[]): FieldDefinitionNode;

// @public (undocumented)
export function makeInputObjectDefinition(name: string, inputs: InputValueDefinitionNode[]): InputObjectTypeDefinitionNode;

// @public (undocumented)
export function makeInputValueDefinition(name: string, type: TypeNode): InputValueDefinitionNode;

// @public (undocumented)
export function makeListType(type: TypeNode): ListTypeNode;

// @public (undocumented)
export function makeModelScalarKeyConditionInputObject(type: string): InputObjectTypeDefinitionNode;

// @public (undocumented)
export function makeNamedType(name: string): NamedTypeNode;

// @public (undocumented)
export function makeNonNullType(type: NamedTypeNode | ListTypeNode): NonNullTypeNode;

// @public (undocumented)
export function makeObjectDefinition(name: string, inputs: FieldDefinitionNode[]): ObjectTypeDefinitionNode;

// @public (undocumented)
export function makeOperationType(operation: OperationTypeNode, type: string): OperationTypeDefinitionNode;

// @public (undocumented)
export function makeScalarKeyConditionForType(type: TypeNode, nonScalarTypeResolver?: (baseType: string) => string): InputObjectTypeDefinitionNode;

// @public (undocumented)
export function makeScalarKeyConditionInputs(): InputObjectTypeDefinitionNode[];

// @public (undocumented)
export function makeSchema(operationTypes: OperationTypeDefinitionNode[]): SchemaDefinitionNode;

// @public (undocumented)
export function makeValueNode(value: any): ValueNode;

// @public (undocumented)
export const MAP_SCALARS: {
    [k: string]: boolean;
};

// @public (undocumented)
export class ModelResourceIDs {
    // (undocumented)
    static GetModelFromConnectionType(typeName: string): string;
    // (undocumented)
    static HttpBodyInputObjectName(typeName: string, fieldName: string): string;
    // (undocumented)
    static HttpQueryInputObjectName(typeName: string, fieldName: string): string;
    // (undocumented)
    static IsModelConnectionType(typeName: string): boolean;
    // (undocumented)
    static ModelAttributeTypesName(): string;
    // (undocumented)
    static ModelCompositeAttributeName(keyFieldNames: string[]): string;
    // (undocumented)
    static ModelCompositeKeyArgumentName(keyFieldNames: string[]): string;
    // (undocumented)
    static ModelCompositeKeyConditionInputTypeName(modelName: string, keyName: string): string;
    // (undocumented)
    static ModelCompositeKeyInputTypeName(modelName: string, keyName: string): string;
    // (undocumented)
    static ModelCompositeKeySeparator(): string;
    // (undocumented)
    static ModelConditionInputTypeName(name: string): string;
    // (undocumented)
    static ModelConnectionTypeName(typeName: string): string;
    // (undocumented)
    static ModelCreateInputObjectName(typeName: string): string;
    // (undocumented)
    static ModelDeleteInputObjectName(typeName: string): string;
    // (undocumented)
    static ModelFilterInputTypeName(name: string): string;
    // (undocumented)
    static ModelFilterListInputTypeName(name: string, includeFilter: Boolean, isSubscriptionFilter?: boolean): string;
    // (undocumented)
    static ModelFilterScalarInputTypeName(name: string, includeFilter: Boolean, isSubscriptionFilter?: boolean): string;
    // (undocumented)
    static ModelKeyConditionInputTypeName(name: string): string;
    // (undocumented)
    static ModelOnCreateSubscriptionName(typeName: string): string;
    // (undocumented)
    static ModelOnDeleteSubscriptionName(typeName: string): string;
    // (undocumented)
    static ModelOnUpdateSubscriptionName(typeName: string): string;
    // (undocumented)
    static ModelScalarFilterInputTypeName(name: string, includeFilter: Boolean): string;
    // (undocumented)
    static ModelSizeInputTypeName(): string;
    // (undocumented)
    static ModelTableDataSourceID(typeName: string): string;
    // (undocumented)
    static ModelTableIAMRoleID(typeName: string): string;
    // (undocumented)
    static ModelTableResourceID(typeName: string): string;
    // (undocumented)
    static ModelTableStreamArn(typeName: string): string;
    // (undocumented)
    static ModelUpdateInputObjectName(typeName: string): string;
    // (undocumented)
    static NonModelInputObjectName(typeName: string): string;
    // (undocumented)
    static setModelNameMap: (modelToTableNameMap: Map<string, string>) => void;
    // (undocumented)
    static UrlParamsInputObjectName(typeName: string, fieldName: string): string;
}

// @public (undocumented)
export const NONE_INT_VALUE = -2147483648;

// @public (undocumented)
export const NONE_VALUE = "___xamznone____";

// @public (undocumented)
export const NUMERIC_SCALARS: {
    [k: string]: boolean;
};

// @public (undocumented)
export function plurality(val: string, improvePluralization: boolean): string;

// @public (undocumented)
export class PredictionsResourceIDs {
    // (undocumented)
    static actionMapID: string;
    // (undocumented)
    static getPredictionFunctionName(action: string): string;
    // (undocumented)
    static iamRole: string;
    // (undocumented)
    static lambdaHandlerName: string;
    // (undocumented)
    static lambdaIAMRole: string;
    // (undocumented)
    static lambdaID: string;
    // (undocumented)
    static lambdaName: string;
    // (undocumented)
    static lambdaRuntime: string;
    // (undocumented)
    static lambdaTimeout: number;
}

// @public (undocumented)
export class ResolverResourceIDs {
    // (undocumented)
    static DynamoDBCreateResolverResourceID(typeName: string): string;
    // (undocumented)
    static DynamoDBDeleteResolverResourceID(typeName: string): string;
    // (undocumented)
    static DynamoDBGetResolverResourceID(typeName: string): string;
    // (undocumented)
    static DynamoDBListResolverResourceID(typeName: string): string;
    // (undocumented)
    static DynamoDBUpdateResolverResourceID(typeName: string): string;
    // (undocumented)
    static ElasticsearchSearchResolverResourceID(typeName: string): string;
    // (undocumented)
    static ResolverResourceID(typeName: string, fieldName: string): string;
    // (undocumented)
    static SyncResolverResourceID(typeName: string): string;
}

// @public (undocumented)
export class ResourceConstants {
    // (undocumented)
    static CONDITIONS: {
        HasEnvironmentParameter: string;
        ShouldUsePayPerRequestBilling: string;
        ShouldUsePointInTimeRecovery: string;
        ShouldUseServerSideEncryption: string;
        ShouldCreateAPIKey: string;
        APIKeyExpirationEpochIsPositive: string;
    };
    // (undocumented)
    static DEFAULT_PAGE_LIMIT: number;
    // (undocumented)
    static DEFAULT_SEARCHABLE_PAGE_LIMIT: number;
    // (undocumented)
    static DEFAULT_SYNC_QUERY_PAGE_LIMIT: number;
    // (undocumented)
    static MAPPINGS: {};
    // (undocumented)
    static METADATA: {};
    // (undocumented)
    static NONE: string;
    // (undocumented)
    static OUTPUTS: {
        GraphQLAPIEndpointOutput: string;
        GraphQLAPIApiKeyOutput: string;
        GraphQLAPIIdOutput: string;
        ElasticsearchStreamingLambdaIAMRoleArn: string;
        ElasticsearchAccessIAMRoleArn: string;
        ElasticsearchDomainArn: string;
        ElasticsearchDomainEndpoint: string;
        OpenSearchStreamingLambdaIAMRoleArn: string;
        OpenSearchAccessIAMRoleArn: string;
        OpenSearchDomainArn: string;
        OpenSearchDomainEndpoint: string;
        AuthCognitoUserPoolIdOutput: string;
        AuthCognitoUserPoolNativeClientOutput: string;
        AuthCognitoUserPoolJSClientOutput: string;
        DataSourceMappingOutput: string;
    };
    // (undocumented)
    static PARAMETERS: {
        Env: string;
        S3DeploymentBucket: string;
        S3DeploymentRootKey: string;
        AppSyncApiName: string;
        AppSyncApiId: string;
        CreateAPIKey: string;
        AuthRoleName: string;
        UnauthRoleName: string;
        APIKeyExpirationEpoch: string;
        DynamoDBBillingMode: string;
        DynamoDBModelTableReadIOPS: string;
        DynamoDBModelTableWriteIOPS: string;
        DynamoDBEnablePointInTimeRecovery: string;
        DynamoDBEnableServerSideEncryption: string;
        ElasticsearchAccessIAMRoleName: string;
        ElasticsearchDebugStreamingLambda: string;
        ElasticsearchStreamingIAMRoleName: string;
        ElasticsearchStreamingFunctionName: string;
        ElasticsearchStreamBatchSize: string;
        ElasticsearchStreamMaximumBatchingWindowInSeconds: string;
        ElasticsearchInstanceCount: string;
        ElasticsearchInstanceType: string;
        ElasticsearchEBSVolumeGB: string;
        ElasticsearchStreamingLambdaHandlerName: string;
        ElasticsearchStreamingLambdaRuntime: string;
        OpenSearchAccessIAMRoleName: string;
        OpenSearchDebugStreamingLambda: string;
        OpenSearchStreamingIAMRoleName: string;
        OpenSearchStreamingFunctionName: string;
        OpenSearchStreamBatchSize: string;
        OpenSearchStreamMaximumBatchingWindowInSeconds: string;
        OpenSearchInstanceCount: string;
        OpenSearchInstanceType: string;
        OpenSearchEBSVolumeGB: string;
        OpenSearchStreamingLambdaHandlerName: string;
        OpenSearchStreamingLambdaRuntime: string;
        AuthCognitoUserPoolId: string;
    };
    // (undocumented)
    static readonly RESOURCES: {
        GraphQLAPILogicalID: string;
        GraphQLSchemaLogicalID: string;
        APIKeyLogicalID: string;
        AuthRolePolicy: string;
        UnauthRolePolicy: string;
        ElasticsearchAccessIAMRoleLogicalID: string;
        ElasticsearchDomainLogicalID: string;
        ElasticsearchStreamingLambdaIAMRoleLogicalID: string;
        ElasticsearchStreamingLambdaFunctionLogicalID: string;
        ElasticsearchDataSourceLogicalID: string;
        OpenSearchAccessIAMRoleLogicalID: string;
        OpenSearchDomainLogicalID: string;
        OpenSearchStreamingLambdaIAMRoleLogicalID: string;
        OpenSearchStreamingLambdaFunctionLogicalID: string;
        OpenSearchDataSourceLogicalID: string;
        SQLLayerManifestBucket: string;
        SQLLayerManifestBucketRegion: string;
        SQLLayerVersionManifestKeyPrefix: string;
        SQLSNSTopicARNManifestKeyPrefix: string;
        NoneDataSource: string;
        AuthCognitoUserPoolLogicalID: string;
        AuthCognitoUserPoolNativeClientLogicalID: string;
        AuthCognitoUserPoolJSClientLogicalID: string;
        TableManagerOnEventHandlerLogicalID: string;
        TableManagerIsCompleteHandlerLogicalID: string;
        TableManagerCustomProviderLogicalID: string;
    };
    // (undocumented)
    static readonly SNIPPETS: {
        AuthCondition: string;
        AuthMode: string;
        VersionedCondition: string;
        ModelObjectKey: string;
        DynamoDBNameOverrideMap: string;
        ModelQueryExpression: string;
        ModelQueryIndex: string;
        IsDynamicGroupAuthorizedVariable: string;
        IsLocalDynamicGroupAuthorizedVariable: string;
        IsStaticGroupAuthorizedVariable: string;
        IsOwnerAuthorizedVariable: string;
        IsLocalOwnerAuthorizedVariable: string;
        SyncResolverKey: string;
        HasSeenSomeKeyArg: string;
    };
}

// @public (undocumented)
export function resourceName(val: string): string;

// @public (undocumented)
export class SearchableResourceIDs {
    // (undocumented)
    static SearchableEventSourceMappingID(typeName: string): string;
    // (undocumented)
    static SearchableFilterInputTypeName(name: string): string;
}

// @public (undocumented)
export const setArgs: SetNode;

// @public (undocumented)
export const setTransformedArgs: (value: ReferenceNode) => SetNode;

// @public (undocumented)
export function setupHashKeyExpression(hashKeyName: string, hashKeyAttributeType: string, queryExprReference: string): IfNode;

// @public (undocumented)
export function simplifyName(val: string): string;

// @public (undocumented)
export const STANDARD_SCALARS: ScalarMap;

// @public (undocumented)
export class SyncResourceIDs {
    // (undocumented)
    static syncDataSourceID: string;
    // (undocumented)
    static syncFunctionID(name: string, region?: string): string;
    // (undocumented)
    static syncFunctionRoleName: string;
    // (undocumented)
    static syncIAMRoleID: string;
    // (undocumented)
    static syncIAMRoleName: string;
    // (undocumented)
    static syncPrimaryKey: string;
    // (undocumented)
    static syncRangeKey: string;
    // (undocumented)
    static syncTableName: string;
}

// @public (undocumented)
export function toCamelCase(words: string[]): string;

// @public (undocumented)
export function toLower(word: string): string;

// @public (undocumented)
export function toPascalCase(words: string[]): string;

// @public (undocumented)
export function toUpper(word: string): string;

// @public (undocumented)
export const transformedArgsRef: ReferenceNode;

// @public (undocumented)
export const TYPESCRIPT_DATA_SCHEMA_CONSTANTS: {
    SCHEMA_PACKAGE: string;
    SCHEMA_PACKAGE_INTERNALS: string;
    BACKEND_PACKAGE: string;
    MODEL_METHOD: string;
    SCHEMA_METHOD: string;
    IDENTIFIER_METHOD: string;
    ARRAY_METHOD: string;
    REQUIRED_METHOD: string;
    STRING_METHOD: string;
    ENUM_METHOD: string;
    REFERENCE_A: string;
    EXPORT_VARIABLE_NAME: string;
    INTERNALS_CONFIGURE_METHOD: string;
    BACKEND_SECRET_METHOD: string;
    PROPERTY_VPC: string;
    PROPERTY_VPC_ID: string;
    PROPERTY_SECURITY_GROUP_IDS: string;
    PROPERTY_AZ_CONFIG: string;
    PROPERTY_SUBNET_ID: string;
    PROPERTY_AZ: string;
    PROPERTY_DATABASE: string;
    PROPERTY_CONNECTION_URI: string;
    PROPERTY_SSL_CERTIFICATE: string;
    PROPERTY_ENGINE: string;
    PROPERTY_IDENTIFIER: string;
};

// @public (undocumented)
export function unwrapNonNull(type: TypeNode): any;

// @public (undocumented)
export function withNamedNodeNamed(t: TypeNode, n: string): TypeNode;

// @public (undocumented)
export function wrapNonNull(type: TypeNode): NonNullTypeNode;

// (No @packageDocumentation comment for this package)

```
