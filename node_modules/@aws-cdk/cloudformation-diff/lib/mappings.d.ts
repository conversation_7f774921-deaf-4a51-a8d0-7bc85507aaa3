export interface TypedMapping {
    readonly type: string;
    readonly sourcePath: string;
    readonly destinationPath: string;
}
export declare function formatMappingsHeader(stream: NodeJS.WritableStream): void;
export declare function formatTypedMappings(stream: NodeJS.WritableStream, mappings: TypedMapping[], env: string): void;
export declare function formatAmbiguitySectionHeader(stream: NodeJS.WritableStream): void;
export declare function formatAmbiguousMappings(stream: NodeJS.WritableStream, pairs: [string[], string[]][], env: string): void;
