export declare class Statement {
    /**
     * Statement ID
     */
    readonly sid: string | undefined;
    /**
     * Statement effect
     */
    readonly effect: Effect;
    /**
     * Resources
     */
    readonly resources: Targets;
    /**
     * Principals
     */
    readonly principals: Targets;
    /**
     * Actions
     */
    readonly actions: Targets;
    /**
     * Object with conditions
     */
    readonly condition?: any;
    private readonly serializedIntrinsic;
    constructor(statement: UnknownMap | string);
    /**
     * Whether this statement is equal to the other statement
     */
    equal(other: Statement): boolean;
    render(): RenderedStatement;
    /**
     * Whether this is a negative statement
     *
     * A statement is negative if any of its targets are negative, inverted
     * if the Effect is Deny.
     */
    get isNegativeStatement(): boolean;
}
export interface RenderedStatement {
    readonly resource: string;
    readonly effect: string;
    readonly action: string;
    readonly principal: string;
    readonly condition: string;
}
export interface StatementJson {
    sid?: string;
    effect: string;
    resources: TargetsJson;
    actions: TargetsJson;
    principals: TargetsJson;
    condition?: any;
}
export interface TargetsJson {
    not: boolean;
    values: string[];
}
/**
 * Parse a list of statements from undefined, a Statement, or a list of statements
 */
export declare function parseStatements(x: any): Statement[];
/**
 * Parse a Statement from a Lambda::Permission object
 *
 * This is actually what Lambda adds to the policy document if you call AddPermission.
 */
export declare function parseLambdaPermission(x: any): Statement;
/**
 * Targets for a field
 */
export declare class Targets {
    /**
     * The values of the targets
     */
    readonly values: string[];
    /**
     * Whether positive or negative matchers
     */
    readonly not: boolean;
    constructor(statement: UnknownMap, positiveKey: string, negativeKey: string);
    get empty(): boolean;
    /**
     * Whether this set of targets is equal to the other set of targets
     */
    equal(other: Targets): any;
    /**
     * If the current value set is empty, put this in it
     */
    replaceEmpty(replacement: string): void;
    /**
     * If the actions contains a '*', replace with this string.
     */
    replaceStar(replacement: string): void;
    /**
     * Render into a summary table cell
     */
    render(): string;
}
type UnknownMap = {
    [key: string]: unknown;
};
export declare enum Effect {
    Unknown = "Unknown",
    Allow = "Allow",
    Deny = "Deny"
}
/**
 * Render the Condition column
 */
export declare function renderCondition(condition: any): string;
export {};
