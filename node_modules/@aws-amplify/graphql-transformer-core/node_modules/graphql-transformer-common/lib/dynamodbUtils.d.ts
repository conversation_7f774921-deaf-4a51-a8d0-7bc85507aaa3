import { InputObjectTypeDefinitionNode, TypeNode, FieldDefinitionNode } from 'graphql';
export declare function makeModelScalarKeyConditionInputObject(type: string): InputObjectTypeDefinitionNode;
export declare function makeScalarKeyConditionInputs(): InputObjectTypeDefinitionNode[];
export declare function makeScalarKeyConditionForType(type: TypeNode, nonScalarTypeResolver?: (baseType: string) => string): InputObjectTypeDefinitionNode;
export declare function makeCompositeKeyConditionInputForKey(modelName: string, keyName: string, fields: FieldDefinitionNode[]): InputObjectTypeDefinitionNode;
export declare function makeCompositeKeyInputForKey(modelName: string, keyName: string, fields: FieldDefinitionNode[]): InputObjectTypeDefinitionNode;
export declare function applyKeyConditionExpression(argName: string, attributeType?: 'S' | 'N' | 'B', queryExprReference?: string, sortKeyName?: string, prefixVariableName?: string): import("graphql-mapping-template").CompoundExpressionNode;
export declare function applyCompositeKeyConditionExpression(keyNames: string[], queryExprReference: string, sortKeyArgumentName: string, sortKeyAttributeName: string): import("graphql-mapping-template").CompoundExpressionNode;
export declare function applyKeyExpressionForCompositeKey(keys: string[], attributeTypes?: ('S' | 'N' | 'B')[], queryExprReference?: string): import("graphql-mapping-template").IfNode | import("graphql-mapping-template").CompoundExpressionNode;
export declare function setupHashKeyExpression(hashKeyName: string, hashKeyAttributeType: string, queryExprReference: string): import("graphql-mapping-template").IfNode;
//# sourceMappingURL=dynamodbUtils.d.ts.map