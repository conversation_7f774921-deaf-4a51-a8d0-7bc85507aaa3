"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createSyncLambdaIAMPolicy = exports.isLambdaSyncConfig = exports.getSyncConfig = exports.validateResolverConfigForType = exports.syncDataSourceConfig = exports.createSyncTable = void 0;
const aws_dynamodb_1 = require("aws-cdk-lib/aws-dynamodb");
const cdk = __importStar(require("aws-cdk-lib"));
const iam = __importStar(require("aws-cdk-lib/aws-iam"));
const graphql_transformer_common_1 = require("graphql-transformer-common");
const utils_1 = require("../utils");
function createSyncTable(context) {
    const scope = context.stackManager.getScopeFor(graphql_transformer_common_1.SyncResourceIDs.syncTableName);
    const tableName = context.resourceHelper.generateTableName(graphql_transformer_common_1.SyncResourceIDs.syncTableName);
    const syncTable = new aws_dynamodb_1.Table(scope, graphql_transformer_common_1.SyncResourceIDs.syncDataSourceID, {
        tableName,
        partitionKey: {
            name: graphql_transformer_common_1.SyncResourceIDs.syncPrimaryKey,
            type: aws_dynamodb_1.AttributeType.STRING,
        },
        sortKey: {
            name: graphql_transformer_common_1.SyncResourceIDs.syncRangeKey,
            type: aws_dynamodb_1.AttributeType.STRING,
        },
        stream: aws_dynamodb_1.StreamViewType.NEW_AND_OLD_IMAGES,
        removalPolicy: cdk.RemovalPolicy.DESTROY,
        billingMode: aws_dynamodb_1.BillingMode.PAY_PER_REQUEST,
        timeToLiveAttribute: '_ttl',
    });
    (0, utils_1.setResourceName)(syncTable, { name: graphql_transformer_common_1.SyncResourceIDs.syncTableName, setOnDefaultChild: true });
    createSyncIAMRole(context, scope, tableName);
}
exports.createSyncTable = createSyncTable;
function createSyncIAMRole(context, scope, tableName) {
    const role = new iam.Role(scope, graphql_transformer_common_1.SyncResourceIDs.syncIAMRoleName, {
        roleName: context.resourceHelper.generateIAMRoleName(graphql_transformer_common_1.SyncResourceIDs.syncIAMRoleName),
        assumedBy: new iam.ServicePrincipal('appsync.amazonaws.com'),
    });
    (0, utils_1.setResourceName)(role, { name: graphql_transformer_common_1.SyncResourceIDs.syncIAMRoleName, setOnDefaultChild: true });
    role.attachInlinePolicy(new iam.Policy(scope, 'DynamoDBAccess', {
        statements: [
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: [
                    'dynamodb:BatchGetItem',
                    'dynamodb:BatchWriteItem',
                    'dynamodb:PutItem',
                    'dynamodb:DeleteItem',
                    'dynamodb:GetItem',
                    'dynamodb:Scan',
                    'dynamodb:Query',
                    'dynamodb:UpdateItem',
                ],
                resources: [
                    cdk.Fn.sub('arn:${AWS::Partition}:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${tablename}', {
                        tablename: tableName,
                    }),
                    cdk.Fn.sub('arn:${AWS::Partition}:dynamodb:${AWS::Region}:${AWS::AccountId}:table/${tablename}/*', {
                        tablename: tableName,
                    }),
                ],
            }),
        ],
    }));
}
function syncDataSourceConfig() {
    return {
        DeltaSyncTableName: joinWithEnv('-', [
            graphql_transformer_common_1.SyncResourceIDs.syncTableName,
            cdk.Fn.getAtt(graphql_transformer_common_1.ResourceConstants.RESOURCES.GraphQLAPILogicalID, 'ApiId'),
        ]),
        DeltaSyncTableTTL: 30,
        BaseTableTTL: 43200,
    };
}
exports.syncDataSourceConfig = syncDataSourceConfig;
function validateResolverConfigForType(ctx, typeName) {
    var _a;
    const resolverConfig = ctx.getResolverConfig();
    const typeResolverConfig = (_a = resolverConfig === null || resolverConfig === void 0 ? void 0 : resolverConfig.models) === null || _a === void 0 ? void 0 : _a[typeName];
    if (typeResolverConfig && (!typeResolverConfig.ConflictDetection || !typeResolverConfig.ConflictHandler)) {
        console.warn(`Invalid resolverConfig for type ${typeName}. Using the project resolverConfig instead.`);
    }
}
exports.validateResolverConfigForType = validateResolverConfigForType;
function getSyncConfig(ctx, typeName) {
    var _a;
    let syncConfig;
    const resolverConfig = ctx.getResolverConfig();
    syncConfig = resolverConfig === null || resolverConfig === void 0 ? void 0 : resolverConfig.project;
    const typeResolverConfig = (_a = resolverConfig === null || resolverConfig === void 0 ? void 0 : resolverConfig.models) === null || _a === void 0 ? void 0 : _a[typeName];
    if (typeResolverConfig && typeResolverConfig.ConflictDetection && typeResolverConfig.ConflictHandler) {
        syncConfig = typeResolverConfig;
    }
    if (syncConfig && isLambdaSyncConfig(syncConfig) && !syncConfig.LambdaConflictHandler.lambdaArn) {
        const { name, region } = syncConfig.LambdaConflictHandler;
        const syncLambdaArn = syncLambdaArnResource(ctx.synthParameters, name, region);
        syncConfig.LambdaConflictHandler.lambdaArn = syncLambdaArn;
    }
    return syncConfig;
}
exports.getSyncConfig = getSyncConfig;
function isLambdaSyncConfig(syncConfig) {
    const lambdaConfigKey = 'LambdaConflictHandler';
    if (syncConfig && syncConfig.ConflictHandler === 'LAMBDA') {
        if (syncConfig.hasOwnProperty(lambdaConfigKey)) {
            return true;
        }
        throw Error(`Invalid Lambda SyncConfig`);
    }
    return false;
}
exports.isLambdaSyncConfig = isLambdaSyncConfig;
function createSyncLambdaIAMPolicy(context, scope, name, region) {
    return new iam.Policy(scope, 'InvokeLambdaFunction', {
        statements: [
            new iam.PolicyStatement({
                effect: iam.Effect.ALLOW,
                actions: ['lambda:InvokeFunction'],
                resources: [syncLambdaArnResource(context.synthParameters, name, region)],
            }),
        ],
    });
}
exports.createSyncLambdaIAMPolicy = createSyncLambdaIAMPolicy;
function syncLambdaArnResource(synthParameters, name, region) {
    const substitutions = {};
    if (referencesEnv(name)) {
        Object.assign(substitutions, { env: synthParameters.amplifyEnvironmentName });
    }
    return cdk.Fn.conditionIf(graphql_transformer_common_1.ResourceConstants.CONDITIONS.HasEnvironmentParameter, cdk.Fn.sub(lambdaArnKey(name, region), substitutions), cdk.Fn.sub(lambdaArnKey(removeEnvReference(name), region), {})).toString();
}
function referencesEnv(value) {
    return value.match(/(\${env})/) !== null;
}
function lambdaArnKey(name, region) {
    return region
        ? `arn:\${AWS:Partition}:lambda:${region}:\${AWS::AccountId}:function:${name}`
        : `arn:\${AWS:Partition}:lambda:\${AWS::Region}:\${AWS::AccountId}:function:${name}`;
}
function removeEnvReference(value) {
    return value.replace(/(-\${env})/, '');
}
function joinWithEnv(separator, listToJoin) {
    return cdk.Fn.conditionIf(graphql_transformer_common_1.ResourceConstants.CONDITIONS.HasEnvironmentParameter, cdk.Fn.join(separator, [...listToJoin, cdk.Fn.ref(graphql_transformer_common_1.ResourceConstants.PARAMETERS.Env)]), cdk.Fn.join(separator, listToJoin));
}
//# sourceMappingURL=sync-utils.js.map