"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StackManager = void 0;
class StackManager {
    constructor(scope, nestedStackProvider, parameterProvider, resourceMapping) {
        this.scope = scope;
        this.nestedStackProvider = nestedStackProvider;
        this.parameterProvider = parameterProvider;
        this.stacks = new Map();
        this.createStack = (stackName) => {
            const newStack = this.nestedStackProvider.provide(this.scope, stackName);
            this.stacks.set(stackName, newStack);
            return newStack;
        };
        this.hasStack = (stackName) => this.stacks.has(stackName);
        this.getScopeFor = (resourceId, defaultStackName) => {
            const stackName = this.resourceToStackMap.has(resourceId) ? this.resourceToStackMap.get(resourceId) : defaultStackName;
            if (!stackName) {
                return this.scope;
            }
            if (this.hasStack(stackName)) {
                return this.getStack(stackName);
            }
            return this.createStack(stackName);
        };
        this.getStackFor = (resourceId, defaultStackName) => this.getScopeFor(resourceId, defaultStackName);
        this.getParameter = (name) => this.parameterProvider && this.parameterProvider.provide(name);
        this.getStack = (stackName) => {
            if (this.stacks.has(stackName)) {
                return this.stacks.get(stackName);
            }
            throw new Error(`Stack ${stackName} is not created`);
        };
        this.resourceToStackMap = new Map(Object.entries(resourceMapping));
    }
}
exports.StackManager = StackManager;
//# sourceMappingURL=stack-manager.js.map