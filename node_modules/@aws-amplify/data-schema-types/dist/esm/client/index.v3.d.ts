import { ExtractModelMeta } from './symbol';
export { __modelMeta__, ExtractModelMeta } from './symbol';
export type ModelTypes<_T extends Record<any, any> = never, _Context extends string = 'CLIENT', _ModelMeta extends Record<any, any> = any> = any;
export type EnumTypes<_T extends Record<any, any> = never, _ModelMeta extends Record<any, any> = any> = any;
export type SelectionSet<_Model, _Path> = any;
/**
 * Custom headers that can be passed either to the client or to individual
 * model operations, either as a static object or a function that returns a
 * promise.
 */
export type CustomHeaders = Record<string, string> | (() => Promise<Record<string, string>>);
export type CustomQueries<Schema extends Record<any, any>, _Context extends string = 'CLIENT', _ModelMeta extends Record<any, any> = ExtractModelMeta<Schema>> = any;
export type CustomMutations<Schema extends Record<any, any>, _Context extends string = 'CLIENT', _ModelMeta extends Record<any, any> = ExtractModelMeta<Schema>> = any;
export type CustomSubscriptions<Schema extends Record<any, any>, _Context extends string = 'CLIENT', _<PERSON>Meta extends Record<any, any> = ExtractModelMeta<Schema>> = any;
export type ModelSortDirection = 'ASC' | 'DESC';
