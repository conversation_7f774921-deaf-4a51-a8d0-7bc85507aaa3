"use strict";
/*
  Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.

  Licensed under the Apache License, Version 2.0 (the "License").
  You may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateTableDefinition = void 0;
const types_1 = require("../types");
const helpers_1 = require("./helpers");
/**
 * Helper that turns the View model into definition that can be used to render
 * Tables in the customer project and in Studio preview.
 * @param table View, converted from the API shape.
 * @param fields (Optional) holds type information about the DataStore model fields being represented.
 * @returns a definition that translates to rendered JSX elements.
 */
function generateTableDefinition(table, dataSchema) {
    var _a, _b, _c;
    if (table.viewConfiguration.type !== 'Table') {
        throw new Error(`Cannot generate a Table definition for viewConfiguration type ${table.viewConfiguration.type}`);
    }
    const definition = types_1.DEFAULT_TABLE_DEFINITION;
    definition.tableStyle = {
        ...types_1.DEFAULT_TABLE_STYLE,
        ...table.style,
    };
    const { columns, ...rest } = (_a = table.viewConfiguration.table) !== null && _a !== void 0 ? _a : {};
    if (rest) {
        definition.tableConfig = {
            ...types_1.DEFAULT_TABLE_CONFIG,
            ...rest,
        };
    }
    definition.tableDataSource = {
        ...types_1.DEFAULT_TABLE_SOURCE,
        ...table.dataSource,
    };
    let fields = [];
    if (table.dataSource.model) {
        if (table.dataSource.type === 'DataStore') {
            const dataModel = (_c = (_b = dataSchema === null || dataSchema === void 0 ? void 0 : dataSchema.models[table.dataSource.model]) === null || _b === void 0 ? void 0 : _b.fields) !== null && _c !== void 0 ? _c : {};
            fields = Object.entries(dataModel).map(([key, value]) => ({
                name: key,
                type: value.dataType,
                isReadOnly: value.readOnly,
                isArray: value.isArray,
                isRequired: value.required,
            }));
        }
        else {
            const customModel = JSON.parse(table.dataSource.model);
            fields = Object.keys(customModel).map((key) => ({
                name: key,
                type: 'String',
                isReadOnly: false,
                isArray: false,
                isRequired: false,
            }));
        }
    }
    definition.columns = (0, helpers_1.orderAndFilterVisibleColumns)(columns !== null && columns !== void 0 ? columns : {}, fields);
    return definition;
}
exports.generateTableDefinition = generateTableDefinition;
//# sourceMappingURL=generate-table-definition.js.map