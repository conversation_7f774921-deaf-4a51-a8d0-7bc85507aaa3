import { ClientConfigFormat, ClientConfigVersionOption, DEFAULT_CLIENT_CONFIG_VERSION, } from '@aws-amplify/client-config';
import { AmplifyUserError } from '@aws-amplify/platform-core';
/**
 * Command that generates client config aka amplify_outputs.
 */
export class GenerateOutputsCommand {
    clientConfigGenerator;
    backendIdentifierResolver;
    /**
     * @inheritDoc
     */
    command;
    /**
     * @inheritDoc
     */
    describe;
    /**
     * Creates client config (amplify-outputs.json) generation command.
     */
    constructor(clientConfigGenerator, backendIdentifierResolver) {
        this.clientConfigGenerator = clientConfigGenerator;
        this.backendIdentifierResolver = backendIdentifierResolver;
        this.command = 'outputs';
        this.describe = 'Generates Amplify backend outputs';
    }
    /**
     * @inheritDoc
     */
    handler = async (args) => {
        const backendIdentifier = await this.backendIdentifierResolver.resolveDeployedBackendIdentifier(args);
        if (!backendIdentifier) {
            throw new AmplifyUserError('BackendIdentifierResolverError', {
                message: 'Could not resolve the backend identifier.',
                resolution: 'Ensure stack name or Amplify App ID and branch specified are correct and exists, then re-run this command.',
            });
        }
        await this.clientConfigGenerator.generateClientConfigToFile(backendIdentifier, args.outputsVersion, args.outDir, args.format);
    };
    /**
     * @inheritDoc
     */
    builder = (yargs) => {
        return yargs
            .option('stack', {
            conflicts: ['app-id', 'branch'],
            describe: 'A stack name that contains an Amplify backend',
            type: 'string',
            array: false,
            group: 'Stack identifier',
        })
            .option('app-id', {
            conflicts: ['stack'],
            describe: 'The Amplify App ID of the project',
            type: 'string',
            array: false,
            implies: 'branch',
            group: 'Project identifier',
        })
            .option('branch', {
            conflicts: ['stack'],
            describe: 'A git branch of the Amplify project',
            type: 'string',
            array: false,
            implies: 'app-id',
            group: 'Project identifier',
        })
            .option('format', {
            describe: 'The format which the configuration should be exported into.',
            type: 'string',
            array: false,
            choices: Object.values(ClientConfigFormat),
        })
            .option('out-dir', {
            describe: 'A path to directory where config is written. If not provided defaults to current process working directory.',
            type: 'string',
            array: false,
        })
            .option('outputs-version', {
            describe: 'Version of the configuration. Version 0 represents classic amplify-cli config file amplify-configuration and 1 represents newer config file amplify_outputs',
            type: 'string',
            array: false,
            choices: Object.values(ClientConfigVersionOption),
            default: DEFAULT_CLIENT_CONFIG_VERSION,
        });
    };
}
//# sourceMappingURL=data:application/json;base64,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