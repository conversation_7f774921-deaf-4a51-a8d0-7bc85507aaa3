"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TelemetrySpanProcessorFactory = void 0;
const sdk_trace_base_1 = require("@opentelemetry/sdk-trace-base");
const local_configuration_controller_factory_1 = require("../config/local_configuration_controller_factory");
const constants_1 = require("./constants");
const telemetry_payload_exporter_1 = require("./telemetry_payload_exporter");
/**
 * Creates TelemetrySpanProcessorFactory
 */
class TelemetrySpanProcessorFactory {
    getInstance = async (dependencies) => {
        const configController = local_configuration_controller_factory_1.configControllerFactory.getInstance('usage_data_preferences.json');
        const telemetryTrackingDisabledLocalFile = (await configController.get(constants_1.TELEMETRY_ENABLED)) === false;
        if (process.env.AMPLIFY_DISABLE_TELEMETRY ||
            telemetryTrackingDisabledLocalFile) {
            return new sdk_trace_base_1.NoopSpanProcessor();
        }
        return new sdk_trace_base_1.SimpleSpanProcessor(new telemetry_payload_exporter_1.DefaultTelemetryPayloadExporter(dependencies));
    };
}
exports.TelemetrySpanProcessorFactory = TelemetrySpanProcessorFactory;
//# sourceMappingURL=data:application/json;base64,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