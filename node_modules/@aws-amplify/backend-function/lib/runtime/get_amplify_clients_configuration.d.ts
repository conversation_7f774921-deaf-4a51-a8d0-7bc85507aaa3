import { S3Client } from '@aws-sdk/client-s3';
export type DataClientEnv = {
    AWS_ACCESS_KEY_ID: string;
    AWS_SECRET_ACCESS_KEY: string;
    AWS_SESSION_TOKEN: string;
    AWS_REGION: string;
    AMPLIFY_DATA_DEFAULT_NAME: string;
} & Record<string, unknown>;
export type ResourceConfig = {
    API: {
        GraphQL: {
            endpoint: string;
            region: string;
            defaultAuthMode: 'iam';
            modelIntrospection: any;
        };
    };
};
export type LibraryOptions = {
    Auth: {
        credentialsProvider: {
            getCredentialsAndIdentityId: () => Promise<{
                credentials: {
                    accessKeyId: string;
                    secretAccessKey: string;
                    sessionToken: string;
                };
            }>;
            clearCredentialsAndIdentityId: () => void;
        };
    };
};
export type DataClientConfig = {
    resourceConfig: ResourceConfig;
    libraryOptions: LibraryOptions;
};
/**
 * Generate the `resourceConfig` and `libraryOptions` need to configure
 * Amplify for the data client in a lambda.
 *
 * Your function needs to be granted resource access on your schema for this to work
 * `a.schema(...).authorization((allow) => [a.resource(myFunction)])`
 * @param env - The environment variables for the data client
 * @returns An object containing the `resourceConfig` and `libraryOptions`
 */
export declare const getAmplifyDataClientConfig: (env: DataClientEnv, s3Client?: S3Client) => Promise<DataClientConfig>;
//# sourceMappingURL=get_amplify_clients_configuration.d.ts.map