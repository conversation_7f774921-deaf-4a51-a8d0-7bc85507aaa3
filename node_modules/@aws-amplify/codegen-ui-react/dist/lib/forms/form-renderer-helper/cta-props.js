"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.buildUpdateDatastoreQuery = exports.onSubmitValidationRun = exports.buildExpression = exports.replaceEmptyStringStatement = void 0;
const typescript_1 = require("typescript");
const helpers_1 = require("../../helpers");
const model_values_1 = require("./model-values");
const relationship_1 = require("./relationship");
const map_from_fieldConfigs_1 = require("./map-from-fieldConfigs");
const bidirectional_relationship_1 = require("./bidirectional-relationship");
const parse_fields_1 = require("./parse-fields");
const graphql_1 = require("../../utils/graphql");
const getRecordCreateCallExpression = ({ savedObjectName, importedModelName, importCollection, dataApi, renderConfigDependencies, }) => {
    if (dataApi === 'GraphQL') {
        const inputs = [typescript_1.factory.createSpreadAssignment(typescript_1.factory.createIdentifier(savedObjectName))];
        return (0, graphql_1.getGraphqlCallExpression)(graphql_1.ActionType.CREATE, importedModelName, importCollection, { inputs }, undefined, renderConfigDependencies);
    }
    return typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('DataStore'), typescript_1.factory.createIdentifier('save')), undefined, [
        typescript_1.factory.createNewExpression(typescript_1.factory.createIdentifier(importedModelName), undefined, [
            typescript_1.factory.createIdentifier(savedObjectName),
        ]),
    ]);
};
const getRecordUpdateDataStoreCallExpression = ({ savedObjectName, savedRecordName, thisModelPrimaryKeys, modelName, importedModelName, fieldConfigs, importCollection, dataApi, renderConfigDependencies, }) => {
    const updatedObjectName = 'updated';
    // TODO: remove after DataStore addresses issue: https://github.com/aws-amplify/amplify-js/issues/10750
    // temporary solution to remove hasOne & belongsTo records
    const relationshipBasedUpdates = (0, relationship_1.getRelationshipBasedRecordUpdateStatements)({
        updatedObjectName,
        savedObjectName,
        fieldConfigs,
    });
    if (dataApi === 'GraphQL') {
        const inputs = [
            ...thisModelPrimaryKeys.map((key) => typescript_1.factory.createPropertyAssignment(typescript_1.factory.createIdentifier(key), typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier(`${savedRecordName}Record`), key))),
            typescript_1.factory.createSpreadAssignment(typescript_1.factory.createIdentifier(savedObjectName)),
        ];
        return (0, graphql_1.getGraphqlCallExpression)(graphql_1.ActionType.UPDATE, importedModelName, importCollection, { inputs }, undefined, renderConfigDependencies);
    }
    return typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('DataStore'), typescript_1.factory.createIdentifier('save')), undefined, [
        typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier(importedModelName), typescript_1.factory.createIdentifier('copyOf')), undefined, [
            typescript_1.factory.createIdentifier(`${(0, helpers_1.lowerCaseFirst)(modelName)}Record`),
            typescript_1.factory.createArrowFunction(undefined, undefined, [
                typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, typescript_1.factory.createIdentifier(updatedObjectName), undefined, undefined, undefined),
            ], undefined, typescript_1.factory.createToken(typescript_1.SyntaxKind.EqualsGreaterThanToken), typescript_1.factory.createBlock([
                typescript_1.factory.createExpressionStatement(typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('Object'), typescript_1.factory.createIdentifier('assign')), undefined, [typescript_1.factory.createIdentifier(updatedObjectName), typescript_1.factory.createIdentifier(savedObjectName)])),
                ...relationshipBasedUpdates,
            ], true)),
        ]),
    ]);
};
/**
  Object.entries(modelFields).forEach(([key, value]) => {
    if (typeof value === 'string' && value === "") {
      modelFields[key] = undefined;
    }
  });
 */
exports.replaceEmptyStringStatement = typescript_1.factory.createExpressionStatement(typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('Object'), typescript_1.factory.createIdentifier('entries')), undefined, [typescript_1.factory.createIdentifier('modelFields')]), typescript_1.factory.createIdentifier('forEach')), undefined, [
    typescript_1.factory.createArrowFunction(undefined, undefined, [
        typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, typescript_1.factory.createArrayBindingPattern([
            typescript_1.factory.createBindingElement(undefined, undefined, typescript_1.factory.createIdentifier('key'), undefined),
            typescript_1.factory.createBindingElement(undefined, undefined, typescript_1.factory.createIdentifier('value'), undefined),
        ]), undefined, undefined, undefined),
    ], undefined, typescript_1.factory.createToken(typescript_1.SyntaxKind.EqualsGreaterThanToken), typescript_1.factory.createBlock([
        typescript_1.factory.createIfStatement(typescript_1.factory.createBinaryExpression(typescript_1.factory.createBinaryExpression(typescript_1.factory.createTypeOfExpression(typescript_1.factory.createIdentifier('value')), typescript_1.factory.createToken(typescript_1.SyntaxKind.EqualsEqualsEqualsToken), typescript_1.factory.createStringLiteral('string')), typescript_1.factory.createToken(typescript_1.SyntaxKind.AmpersandAmpersandToken), typescript_1.factory.createBinaryExpression(typescript_1.factory.createIdentifier('value'), typescript_1.factory.createToken(typescript_1.SyntaxKind.EqualsEqualsEqualsToken), typescript_1.factory.createStringLiteral(''))), typescript_1.factory.createBlock([
            typescript_1.factory.createExpressionStatement(typescript_1.factory.createBinaryExpression(typescript_1.factory.createElementAccessExpression(typescript_1.factory.createIdentifier('modelFields'), typescript_1.factory.createIdentifier('key')), typescript_1.factory.createToken(typescript_1.SyntaxKind.EqualsToken), typescript_1.factory.createNull())),
        ], true), undefined),
    ], true)),
]));
const buildExpression = (dataStoreActionType, modelName, importedModelName, fieldConfigs, dataSchema, importCollection, dataApi, renderConfigDependencies) => {
    const modelFieldsObjectName = 'modelFields';
    const modelFieldsObjectToSaveName = 'modelFieldsToSave';
    const thisModelPrimaryKeys = dataSchema.models[modelName].primaryKeys;
    // promises.push(...statements that handle hasMany/ manyToMany/ hasOne-belongsTo relationships)
    const relationshipsPromisesAccessStatements = [];
    const savedRecordName = (0, helpers_1.lowerCaseFirst)(modelName);
    Object.entries(fieldConfigs).forEach((fieldConfig) => {
        var _a;
        const [, fieldConfigMetaData] = fieldConfig;
        relationshipsPromisesAccessStatements.push(...(0, bidirectional_relationship_1.getBiDirectionalRelationshipStatements)({
            formActionType: dataStoreActionType,
            dataSchema,
            importCollection,
            fieldConfig,
            modelName,
            savedRecordName,
            thisModelPrimaryKeys,
            dataApi,
            renderConfigDependencies,
        }));
        if (((_a = fieldConfigMetaData.relationship) === null || _a === void 0 ? void 0 : _a.type) === 'HAS_MANY') {
            if ((0, map_from_fieldConfigs_1.isManyToManyRelationship)(fieldConfigMetaData)) {
                const joinTable = dataSchema.models[fieldConfigMetaData.relationship.relatedJoinTableName];
                relationshipsPromisesAccessStatements.push(...(0, relationship_1.buildManyToManyRelationshipStatements)(dataStoreActionType, importedModelName, fieldConfig, thisModelPrimaryKeys, joinTable, savedRecordName, importCollection, dataApi, renderConfigDependencies));
            }
            else {
                relationshipsPromisesAccessStatements.push(...(0, relationship_1.buildHasManyRelationshipStatements)(dataStoreActionType, importedModelName, fieldConfig, thisModelPrimaryKeys, savedRecordName, importCollection, dataApi, renderConfigDependencies));
            }
        }
    });
    if (relationshipsPromisesAccessStatements.length) {
        relationshipsPromisesAccessStatements.unshift(typescript_1.factory.createVariableStatement(undefined, typescript_1.factory.createVariableDeclarationList([
            typescript_1.factory.createVariableDeclaration(typescript_1.factory.createIdentifier('promises'), undefined, undefined, typescript_1.factory.createArrayLiteralExpression([], false)),
        ], typescript_1.NodeFlags.Const)));
    }
    const { modelObjectToSave, isDifferentFromModelObject } = (0, parse_fields_1.generateModelObjectToSave)(fieldConfigs, modelFieldsObjectName, dataSchema.models, dataApi === 'GraphQL', dataStoreActionType);
    const modelObjectToSaveStatements = [];
    let savedObjectName = modelFieldsObjectName;
    if (isDifferentFromModelObject) {
        savedObjectName = modelFieldsObjectToSaveName;
        modelObjectToSaveStatements.push(typescript_1.factory.createVariableStatement(undefined, typescript_1.factory.createVariableDeclarationList([
            typescript_1.factory.createVariableDeclaration(typescript_1.factory.createIdentifier(modelFieldsObjectToSaveName), undefined, undefined, modelObjectToSave),
        ], typescript_1.NodeFlags.Const)));
    }
    const resolvePromisesStatement = typescript_1.factory.createExpressionStatement(typescript_1.factory.createAwaitExpression(typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('Promise'), typescript_1.factory.createIdentifier('all')), undefined, [typescript_1.factory.createIdentifier('promises')])));
    if (dataStoreActionType === 'update') {
        const recordUpdateDataStoreCallExpression = getRecordUpdateDataStoreCallExpression({
            savedObjectName,
            savedRecordName,
            thisModelPrimaryKeys,
            modelName,
            importedModelName,
            fieldConfigs,
            importCollection,
            dataApi,
            renderConfigDependencies,
        });
        const genericUpdateStatement = relationshipsPromisesAccessStatements.length
            ? [
                typescript_1.factory.createExpressionStatement(typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('promises'), typescript_1.factory.createIdentifier('push')), undefined, [recordUpdateDataStoreCallExpression])),
                resolvePromisesStatement,
            ]
            : [typescript_1.factory.createExpressionStatement(typescript_1.factory.createAwaitExpression(recordUpdateDataStoreCallExpression))];
        return [...relationshipsPromisesAccessStatements, ...modelObjectToSaveStatements, ...genericUpdateStatement];
    }
    const recordCreateCallExpression = getRecordCreateCallExpression({
        savedObjectName,
        importedModelName,
        importCollection,
        dataApi,
        renderConfigDependencies,
    });
    const genericCreateStatement = relationshipsPromisesAccessStatements.length
        ? [
            typescript_1.factory.createVariableStatement(undefined, typescript_1.factory.createVariableDeclarationList([
                typescript_1.factory.createVariableDeclaration(typescript_1.factory.createIdentifier(savedRecordName), undefined, undefined, dataApi === 'GraphQL'
                    ? typescript_1.factory.createPropertyAccessChain(typescript_1.factory.createPropertyAccessChain(typescript_1.factory.createParenthesizedExpression(typescript_1.factory.createAwaitExpression(recordCreateCallExpression)), typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionDotToken), typescript_1.factory.createIdentifier('data')), typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionDotToken), typescript_1.factory.createIdentifier((0, graphql_1.getGraphqlQueryForModel)(graphql_1.ActionType.CREATE, importedModelName)))
                    : typescript_1.factory.createAwaitExpression(recordCreateCallExpression)),
            ], typescript_1.NodeFlags.Const)),
        ]
        : [typescript_1.factory.createExpressionStatement(typescript_1.factory.createAwaitExpression(recordCreateCallExpression))];
    const createStatements = [
        ...modelObjectToSaveStatements,
        ...genericCreateStatement,
        ...relationshipsPromisesAccessStatements,
    ];
    if (relationshipsPromisesAccessStatements.length) {
        createStatements.push(resolvePromisesStatement);
    }
    return createStatements;
};
exports.buildExpression = buildExpression;
/**
  example: const validationResponses = await Promise.all(
    Object.keys(validations).reduce((promises, fieldName) => {
      if (Array.isArray(modelFields[fieldName])) {
        promises.push(
          ...modelFields[fieldName].map((item) =>
            runValidationTasks(fieldName, item, getDisplayValue[fieldName]),
          ),
        );
        return promises;
      }
      promises.push(runValidationTasks(fieldName, modelFields[fieldName], getDisplayValue[fieldName]));
      return promises;
    }, []),
  );
  if (validationResponses.some((r) => r.hasError)) {
    return;
  }
*/
const onSubmitValidationRun = (shouldUseGetDisplayValue) => {
    const getDisplayValueAccess = typescript_1.factory.createElementAccessExpression(typescript_1.factory.createIdentifier(model_values_1.getDisplayValueObjectName), typescript_1.factory.createIdentifier('fieldName'));
    const runValidationTasksArgsForArray = [
        typescript_1.factory.createIdentifier('fieldName'),
        typescript_1.factory.createIdentifier('item'),
    ];
    const runValidationTasksArgs = [
        typescript_1.factory.createIdentifier('fieldName'),
        typescript_1.factory.createElementAccessExpression(typescript_1.factory.createIdentifier('modelFields'), typescript_1.factory.createIdentifier('fieldName')),
    ];
    if (shouldUseGetDisplayValue) {
        runValidationTasksArgsForArray.push(getDisplayValueAccess);
        runValidationTasksArgs.push(getDisplayValueAccess);
    }
    return [
        typescript_1.factory.createVariableStatement(undefined, typescript_1.factory.createVariableDeclarationList([
            typescript_1.factory.createVariableDeclaration(typescript_1.factory.createIdentifier('validationResponses'), undefined, undefined, typescript_1.factory.createAwaitExpression(typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('Promise'), typescript_1.factory.createIdentifier('all')), undefined, [
                typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('Object'), typescript_1.factory.createIdentifier('keys')), undefined, [typescript_1.factory.createIdentifier('validations')]), typescript_1.factory.createIdentifier('reduce')), undefined, [
                    typescript_1.factory.createArrowFunction(undefined, undefined, [
                        typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, typescript_1.factory.createIdentifier('promises'), undefined, undefined, undefined),
                        typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, typescript_1.factory.createIdentifier('fieldName'), undefined, undefined, undefined),
                    ], undefined, typescript_1.factory.createToken(typescript_1.SyntaxKind.EqualsGreaterThanToken), typescript_1.factory.createBlock([
                        typescript_1.factory.createIfStatement(typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('Array'), typescript_1.factory.createIdentifier('isArray')), undefined, [
                            typescript_1.factory.createElementAccessExpression(typescript_1.factory.createIdentifier('modelFields'), typescript_1.factory.createIdentifier('fieldName')),
                        ]), typescript_1.factory.createBlock([
                            typescript_1.factory.createExpressionStatement(typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('promises'), typescript_1.factory.createIdentifier('push')), undefined, [
                                typescript_1.factory.createSpreadElement(typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createElementAccessExpression(typescript_1.factory.createIdentifier('modelFields'), typescript_1.factory.createIdentifier('fieldName')), typescript_1.factory.createIdentifier('map')), undefined, [
                                    typescript_1.factory.createArrowFunction(undefined, undefined, [
                                        typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, typescript_1.factory.createIdentifier('item'), undefined, undefined, undefined),
                                    ], undefined, typescript_1.factory.createToken(typescript_1.SyntaxKind.EqualsGreaterThanToken), typescript_1.factory.createCallExpression(typescript_1.factory.createIdentifier('runValidationTasks'), undefined, runValidationTasksArgsForArray)),
                                ])),
                            ])),
                            typescript_1.factory.createReturnStatement(typescript_1.factory.createIdentifier('promises')),
                        ], true), undefined),
                        typescript_1.factory.createExpressionStatement(typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('promises'), typescript_1.factory.createIdentifier('push')), undefined, [
                            typescript_1.factory.createCallExpression(typescript_1.factory.createIdentifier('runValidationTasks'), undefined, runValidationTasksArgs),
                        ])),
                        typescript_1.factory.createReturnStatement(typescript_1.factory.createIdentifier('promises')),
                    ], true)),
                    typescript_1.factory.createArrayLiteralExpression([], false),
                ]),
            ]))),
        ], typescript_1.NodeFlags.Const)),
        typescript_1.factory.createIfStatement(typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('validationResponses'), typescript_1.factory.createIdentifier('some')), undefined, [
            typescript_1.factory.createArrowFunction(undefined, undefined, [
                typescript_1.factory.createParameterDeclaration(undefined, undefined, undefined, typescript_1.factory.createIdentifier('r'), undefined, undefined, undefined),
            ], undefined, typescript_1.factory.createToken(typescript_1.SyntaxKind.EqualsGreaterThanToken), typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('r'), typescript_1.factory.createIdentifier('hasError'))),
        ]), typescript_1.factory.createBlock([typescript_1.factory.createReturnStatement(undefined)], true), undefined),
    ];
};
exports.onSubmitValidationRun = onSubmitValidationRun;
const buildUpdateDatastoreQuery = (importedModelName, lowerCaseDataTypeName, relatedModelStatements, primaryKeyPropName, importCollection, primaryKeys, dataApi, renderConfigDependencies) => {
    // if there are multiple primaryKeys, it's a composite key and we're using 'id' for a composite key prop
    const pkPropIdentifier = typescript_1.factory.createIdentifier(primaryKeyPropName);
    const queryCall = dataApi === 'GraphQL'
        ? (0, graphql_1.wrapInParenthesizedExpression)((0, graphql_1.getGraphqlCallExpression)(graphql_1.ActionType.GET, importedModelName, importCollection, {
            inputs: [
                primaryKeys.length > 1
                    ? typescript_1.factory.createSpreadAssignment(pkPropIdentifier)
                    : typescript_1.factory.createPropertyAssignment(typescript_1.factory.createIdentifier(primaryKeys[0]), pkPropIdentifier),
            ],
        }, undefined, renderConfigDependencies), ['data', (0, graphql_1.getGraphqlQueryForModel)(graphql_1.ActionType.GET, importedModelName)])
        : typescript_1.factory.createAwaitExpression(typescript_1.factory.createCallExpression(typescript_1.factory.createPropertyAccessExpression(typescript_1.factory.createIdentifier('DataStore'), typescript_1.factory.createIdentifier('query')), undefined, [typescript_1.factory.createIdentifier(importedModelName), pkPropIdentifier]));
    const statements = [];
    const setRecordStatement = typescript_1.factory.createExpressionStatement(typescript_1.factory.createCallExpression((0, helpers_1.getSetNameIdentifier)(`${lowerCaseDataTypeName}Record`), undefined, [
        typescript_1.factory.createIdentifier('record'),
    ]));
    if (dataApi === 'GraphQL') {
        statements.push(...relatedModelStatements);
        statements.push(setRecordStatement);
    }
    else {
        statements.push(setRecordStatement);
        statements.push(...relatedModelStatements);
    }
    return [
        typescript_1.factory.createVariableStatement(undefined, typescript_1.factory.createVariableDeclarationList([
            typescript_1.factory.createVariableDeclaration(typescript_1.factory.createIdentifier('queryData'), undefined, undefined, typescript_1.factory.createArrowFunction([typescript_1.factory.createModifier(typescript_1.SyntaxKind.AsyncKeyword)], undefined, [], undefined, typescript_1.factory.createToken(typescript_1.SyntaxKind.EqualsGreaterThanToken), typescript_1.factory.createBlock([
                typescript_1.factory.createVariableStatement(undefined, typescript_1.factory.createVariableDeclarationList([
                    typescript_1.factory.createVariableDeclaration(typescript_1.factory.createIdentifier('record'), undefined, undefined, typescript_1.factory.createConditionalExpression(pkPropIdentifier, typescript_1.factory.createToken(typescript_1.SyntaxKind.QuestionToken), queryCall, typescript_1.factory.createToken(typescript_1.SyntaxKind.ColonToken), typescript_1.factory.createIdentifier((0, helpers_1.getModelNameProp)(lowerCaseDataTypeName)))),
                ], typescript_1.NodeFlags.Const)),
                ...statements,
            ], true))),
        ], typescript_1.NodeFlags.Const)),
        typescript_1.factory.createExpressionStatement(typescript_1.factory.createCallExpression(typescript_1.factory.createIdentifier('queryData'), undefined, [])),
    ];
};
exports.buildUpdateDatastoreQuery = buildUpdateDatastoreQuery;
//# sourceMappingURL=cta-props.js.map