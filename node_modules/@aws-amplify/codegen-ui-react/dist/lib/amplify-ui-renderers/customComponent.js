"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const typescript_1 = require("typescript");
const helpers_1 = require("../helpers");
const react_component_renderer_1 = require("../react-component-renderer");
class CustomComponentRenderer extends react_component_renderer_1.ReactComponentRenderer {
    renderElement(renderChildren) {
        const children = this.component.children ? this.component.children : [];
        const element = typescript_1.factory.createJsxElement(this.renderOpeningElement(), renderChildren(children), typescript_1.factory.createJsxClosingElement(typescript_1.factory.createIdentifier(this.component.componentType)));
        if ((0, helpers_1.isAliased)(this.component.componentType)) {
            this.importCollection.addImport(`./${(0, helpers_1.removeAlias)(this.component.componentType)}`, `${(0, helpers_1.removeAlias)(this.component.componentType)} as ${this.component.componentType}`);
        }
        else {
            this.importCollection.addImport(`./${this.component.componentType}`, 'default');
        }
        return element;
    }
}
exports.default = CustomComponentRenderer;
//# sourceMappingURL=customComponent.js.map