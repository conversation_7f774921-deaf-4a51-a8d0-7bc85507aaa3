import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_DescribeTypeCommand, se_DescribeTypeCommand } from "../protocols/Aws_query";
export { $Command };
export class DescribeTypeCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("CloudFormation", "DescribeType", {})
    .n("CloudFormationClient", "DescribeTypeCommand")
    .f(void 0, void 0)
    .ser(se_DescribeTypeCommand)
    .de(de_DescribeTypeCommand)
    .build() {
}
