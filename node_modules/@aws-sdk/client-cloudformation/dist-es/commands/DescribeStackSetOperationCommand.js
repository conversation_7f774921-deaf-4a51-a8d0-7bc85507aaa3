import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { de_DescribeStackSetOperationCommand, se_DescribeStackSetOperationCommand } from "../protocols/Aws_query";
export { $Command };
export class DescribeStackSetOperationCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
    ];
})
    .s("CloudFormation", "DescribeStackSetOperation", {})
    .n("CloudFormationClient", "DescribeStackSetOperationCommand")
    .f(void 0, void 0)
    .ser(se_DescribeStackSetOperationCommand)
    .de(de_DescribeStackSetOperationCommand)
    .build() {
}
