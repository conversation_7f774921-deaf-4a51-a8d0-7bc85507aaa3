import {
  HttpRequest as __HttpRequest,
  HttpResponse as __HttpResponse,
} from "@smithy/protocol-http";
import { SerdeContext as __SerdeContext } from "@smithy/types";
import {
  GetRoleCredentialsCommandInput,
  GetRoleCredentialsCommandOutput,
} from "../commands/GetRoleCredentialsCommand";
import {
  ListAccountRolesCommandInput,
  ListAccountRolesCommandOutput,
} from "../commands/ListAccountRolesCommand";
import {
  ListAccountsCommandInput,
  ListAccountsCommandOutput,
} from "../commands/ListAccountsCommand";
import {
  LogoutCommandInput,
  LogoutCommandOutput,
} from "../commands/LogoutCommand";
export declare const se_GetRoleCredentialsCommand: (
  input: GetRoleCredentialsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListAccountRolesCommand: (
  input: ListAccountRolesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListAccountsCommand: (
  input: ListAccountsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_LogoutCommand: (
  input: LogoutCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const de_GetRoleCredentialsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetRoleCredentialsCommandOutput>;
export declare const de_ListAccountRolesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListAccountRolesCommandOutput>;
export declare const de_ListAccountsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListAccountsCommandOutput>;
export declare const de_LogoutCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<LogoutCommandOutput>;
