import { AwsCredentialIdentityProvider } from "@smithy/types";
import { RemoteProviderInit } from "./remoteProvider/RemoteProviderInit";
/**
 * @internal
 */
export declare const ENV_CMDS_FULL_URI = "AWS_CONTAINER_CREDENTIALS_FULL_URI";
/**
 * @internal
 */
export declare const ENV_CMDS_RELATIVE_URI = "AWS_CONTAINER_CREDENTIALS_RELATIVE_URI";
/**
 * @internal
 */
export declare const ENV_CMDS_AUTH_TOKEN = "AWS_CONTAINER_AUTHORIZATION_TOKEN";
/**
 * @internal
 *
 * Creates a credential provider that will source credentials from the ECS
 * Container Metadata Service
 */
export declare const fromContainerMetadata: (init?: RemoteProviderInit) => AwsCredentialIdentityProvider;
