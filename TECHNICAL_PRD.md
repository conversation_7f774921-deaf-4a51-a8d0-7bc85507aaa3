# Technical Product Requirements Document (PRD)
## RootsConnect - Indian Diaspora Discovery Platform

### 1. Technical Architecture Overview

**Architecture Pattern:** Serverless, Mobile-First, Cloud-Native
**Development Approach:** AI-Assisted Single Developer Workflow
**Deployment Strategy:** Multi-environment with CI/CD

### 2. Frontend Technology Stack

#### 2.1 Mobile Application Framework
- **Framework:** React Native 0.73+
- **Language:** TypeScript 5.0+
- **Build Tool:** Metro Bundler
- **Package Manager:** npm or yarn

#### 2.2 Navigation & UI Framework
```
Navigation Stack:
├── React Navigation 6.x
│   ├── Stack Navigator (Auth Flow)
│   ├── Tab Navigator (Main App)
│   └── Modal Navigator (Connection Requests)
├── UI Framework: NativeBase 3.x (Recommended - Rich component library)
├── Alternative Options:
│   ├── Tamagui (High-performance, newer)
│   ├── React Native Elements (Mature, stable)
│   └── UI Kitten (Eva Design System)
├── Theme Management: React Context + AsyncStorage
├── Dark/Light Mode: Automatic system detection + manual toggle
└── Icons: React Native Vector Icons + NativeBase Icons
```

#### 2.3 State Management
- **Global State:** React Context API + useReducer
- **Local State:** React Hooks (useState, useEffect)
- **Data Caching:** AWS Amplify DataStore (offline-first)
- **Form Management:** React Hook Form

#### 2.4 Map Integration
- **iOS:** Apple MapKit (react-native-maps)
- **Android:** Google Maps (react-native-maps)
- **Geolocation:** @react-native-community/geolocation
- **Permissions:** react-native-permissions

### 3. Backend Technology Stack

#### 3.1 AWS Amplify Gen 2 Architecture
```
Backend Structure:
├── amplify/
│   ├── auth/
│   │   └── resource.ts (Cognito configuration)
│   ├── data/
│   │   └── resource.ts (GraphQL schema & DynamoDB)
│   ├── storage/
│   │   └── resource.ts (S3 configuration)
│   ├── analytics/
│   │   └── resource.ts (Pinpoint configuration)
│   └── backend.ts (Main backend configuration)
```

#### 3.2 Data Layer
- **Database:** Amazon DynamoDB (NoSQL)
- **API:** AWS AppSync (GraphQL)
- **Real-time:** GraphQL Subscriptions
- **Offline Sync:** Amplify DataStore
- **File Storage:** Amazon S3

#### 3.3 Authentication & Authorization
- **Service:** Amazon Cognito
- **Social Providers:** Google OAuth 2.0, Facebook Login
- **Token Management:** JWT with automatic refresh
- **Authorization:** Cognito User Groups & IAM policies

#### 3.4 Analytics & Notifications
- **Analytics:** AWS Pinpoint
- **Push Notifications:** AWS Pinpoint (iOS APNS, Android FCM)
- **Email Campaigns:** AWS Pinpoint Email
- **User Tracking:** Custom events and user attributes

### 4. Project Structure

#### 4.1 React Native Project Structure
```
RootsConnect/
├── src/
│   ├── components/           # Reusable UI components
│   │   ├── common/
│   │   │   ├── Button.tsx
│   │   │   ├── Input.tsx
│   │   │   └── LoadingSpinner.tsx
│   │   ├── auth/
│   │   │   ├── LoginForm.tsx
│   │   │   └── SocialSignIn.tsx
│   │   ├── profile/
│   │   │   ├── ProfileCard.tsx
│   │   │   ├── ContactFields.tsx
│   │   │   └── ProfileImage.tsx
│   │   ├── map/
│   │   │   ├── MapView.tsx
│   │   │   ├── UserMarker.tsx
│   │   │   └── MapFilters.tsx
│   │   └── connection/
│   │       ├── ConnectionRequest.tsx
│   │       ├── ContactSharing.tsx
│   │       └── IntroductionMessage.tsx
│   ├── screens/              # Screen components
│   │   ├── auth/
│   │   │   ├── LoginScreen.tsx
│   │   │   ├── SignUpScreen.tsx
│   │   │   └── ForgotPasswordScreen.tsx
│   │   ├── profile/
│   │   │   ├── ProfileScreen.tsx
│   │   │   ├── EditProfileScreen.tsx
│   │   │   └── ProfileSetupScreen.tsx
│   │   ├── discovery/
│   │   │   ├── MapScreen.tsx
│   │   │   ├── SearchScreen.tsx
│   │   │   └── UserDetailScreen.tsx
│   │   ├── connections/
│   │   │   ├── ConnectionsScreen.tsx
│   │   │   ├── RequestsScreen.tsx
│   │   │   └── ContactSharingScreen.tsx
│   │   └── settings/
│   │       ├── SettingsScreen.tsx
│   │       └── PrivacyScreen.tsx
│   ├── navigation/           # Navigation configuration
│   │   ├── AppNavigator.tsx
│   │   ├── AuthNavigator.tsx
│   │   ├── MainTabNavigator.tsx
│   │   └── types.ts
│   ├── services/            # Business logic & API calls
│   │   ├── auth/
│   │   │   ├── authService.ts
│   │   │   └── socialAuth.ts
│   │   ├── user/
│   │   │   ├── userService.ts
│   │   │   └── profileService.ts
│   │   ├── connection/
│   │   │   ├── connectionService.ts
│   │   │   └── contactSharingService.ts
│   │   ├── location/
│   │   │   ├── locationService.ts
│   │   │   └── mapService.ts
│   │   └── analytics/
│   │       └── analyticsService.ts
│   ├── hooks/               # Custom React hooks
│   │   ├── useAuth.ts
│   │   ├── useLocation.ts
│   │   ├── useConnections.ts
│   │   └── useAnalytics.ts
│   ├── context/             # React Context providers
│   │   ├── AuthContext.tsx
│   │   ├── LocationContext.tsx
│   │   ├── ThemeContext.tsx
│   │   └── AppProvider.tsx
│   ├── types/               # TypeScript type definitions
│   │   ├── auth.ts
│   │   ├── user.ts
│   │   ├── connection.ts
│   │   ├── location.ts
│   │   └── api.ts
│   ├── utils/               # Utility functions
│   │   ├── validation.ts
│   │   ├── formatting.ts
│   │   ├── constants.ts
│   │   └── helpers.ts
│   ├── assets/              # Static assets
│   │   ├── images/
│   │   ├── icons/
│   │   └── fonts/
│   └── styles/              # Global styles & theming
│       ├── theme/
│       │   ├── index.ts
│       │   ├── lightTheme.ts
│       │   ├── darkTheme.ts
│       │   ├── culturalThemes.ts
│       │   └── customThemes.ts
│       ├── colors.ts
│       ├── typography.ts
│       └── spacing.ts
├── amplify/                 # AWS Amplify Gen 2 configuration
│   ├── auth/
│   │   └── resource.ts
│   ├── data/
│   │   └── resource.ts
│   ├── storage/
│   │   └── resource.ts
│   ├── analytics/
│   │   └── resource.ts
│   └── backend.ts
├── android/                 # Android-specific code
├── ios/                     # iOS-specific code
├── __tests__/               # Test files
├── package.json
├── tsconfig.json
├── metro.config.js
├── babel.config.js
└── README.md
```

#### 4.2 AWS Amplify Gen 2 Backend Structure
```
amplify/
├── auth/
│   └── resource.ts          # Cognito User Pool configuration
│       ├── Social providers (Google, Facebook)
│       ├── User attributes (email, phone, cultural_background)
│       ├── Password policies
│       └── MFA settings
├── data/
│   └── resource.ts          # GraphQL schema & DynamoDB tables
│       ├── User model
│       ├── Connection model
│       ├── Event model
│       ├── ContactInfo model
│       └── Geospatial indexes
├── storage/
│   └── resource.ts          # S3 bucket configuration
│       ├── Profile images
│       ├── Event images
│       └── Access policies
├── analytics/
│   └── resource.ts          # Pinpoint configuration
│       ├── App analytics
│       ├── Push notifications
│       ├── Email campaigns
│       └── Custom events
└── backend.ts               # Main backend configuration
    ├── Environment variables
    ├── Resource dependencies
    └── Deployment settings
```

### 5. Component Framework Comparison & Recommendation

#### 5.1 NativeBase 3.x (Recommended Choice)
**Why Perfect for Your Use Case:**
```typescript
// Rich pre-built components perfect for your app
import {
  Box, VStack, HStack, Center, Spacer,
  Button, IconButton, Fab,
  Input, TextArea, Select, Checkbox, Radio, Switch,
  Avatar, Image, Icon,
  Card, Divider, Badge,
  Modal, AlertDialog, Actionsheet,
  FlatList, ScrollView,
  FormControl, WarningOutlineIcon,
  useToast, useDisclose
} from 'native-base';

// Your specific use cases covered:
<VStack space={4}>
  {/* User Profile Card - Perfect for discovery */}
  <Card>
    <HStack space={3} alignItems="center">
      <Avatar source={{ uri: user.profileImage }} />
      <VStack>
        <Text bold>{user.name}</Text>
        <Text color="gray.500">{user.culturalBackground}</Text>
        <Badge colorScheme="green">Willing to Meet</Badge>
      </VStack>
      <Spacer />
      <IconButton icon={<Icon as={MaterialIcons} name="connect" />} />
    </HStack>
  </Card>

  {/* Contact Sharing Form - Multiple inputs handled easily */}
  <FormControl>
    <FormControl.Label>Email Addresses</FormControl.Label>
    <VStack space={2}>
      <Input placeholder="Personal email" />
      <Input placeholder="Work email" />
    </VStack>
  </FormControl>

  {/* Connection Request Modal */}
  <Modal isOpen={isOpen} onClose={onClose}>
    <Modal.Content>
      <Modal.Header>Send Connection Request</Modal.Header>
      <Modal.Body>
        <TextArea placeholder="Introduction message..." />
      </Modal.Body>
      <Modal.Footer>
        <Button.Group space={2}>
          <Button variant="ghost" onPress={onClose}>Cancel</Button>
          <Button onPress={sendRequest}>Send Request</Button>
        </Button.Group>
      </Modal.Footer>
    </Modal.Content>
  </Modal>
</VStack>
```

**Advantages:**
- ✅ **Rich Component Library**: 40+ pre-built components
- ✅ **Excellent Theming**: Built-in theme system with variants
- ✅ **Form Handling**: FormControl, validation, error states
- ✅ **Layout System**: VStack, HStack, Center for easy layouts
- ✅ **Accessibility**: ARIA support built-in
- ✅ **TypeScript**: Full TypeScript support
- ✅ **Active Development**: Regular updates and community support

#### 5.2 Alternative Options Analysis

**Tamagui (High Performance)**
```typescript
// Pros: Compile-time optimization, better performance
// Cons: Newer, smaller component library, steeper learning curve
import { Button, Card, H2, Paragraph, XStack, YStack } from 'tamagui';

<Card>
  <XStack space="$4" alignItems="center">
    <YStack>
      <H2>{user.name}</H2>
      <Paragraph color="$gray10">{user.location}</Paragraph>
    </YStack>
  </XStack>
</Card>
```

**React Native Elements (Mature)**
```typescript
// Pros: Very stable, large community
// Cons: Less modern, theming not as flexible
import { Card, Button, Avatar, Badge } from 'react-native-elements';

<Card>
  <Avatar source={{ uri: user.profileImage }} />
  <Button title="Connect" />
</Card>
```

**UI Kitten (Eva Design)**
```typescript
// Pros: Eva Design System, good theming
// Cons: Smaller community, fewer components
import { Card, Button, Avatar, Text } from '@ui-kitten/components';
```

#### 5.3 Recommended Component Mapping for Your App

**NativeBase Components for Your Features:**
```typescript
// User Discovery & Profiles
- Avatar, Card, Badge, VStack, HStack
- Button (variants: solid, outline, ghost)
- Image with fallback

// Contact Information Forms
- FormControl, Input, TextArea, Select
- Checkbox, Switch (for privacy settings)
- WarningOutlineIcon (for validation)

// Map & Location
- Fab (floating action button for "willing to meet")
- Modal, Actionsheet (for map filters)
- Badge (for status indicators)

// Connection Requests
- Modal, AlertDialog (for connection requests)
- Button.Group (for accept/decline)
- useToast (for notifications)

// Navigation & Layout
- Box, Center, Spacer (for layouts)
- ScrollView, FlatList (for lists)
- Divider (for sections)
```

#### 5.4 Installation & Setup
```bash
# Install NativeBase
npm install native-base react-native-svg react-native-safe-area-context

# iOS additional setup
cd ios && pod install

# Android - no additional setup needed
```

#### 5.5 Quick Start Template
```typescript
// App.tsx with NativeBase
import React from 'react';
import { NativeBaseProvider, Box, Text, Button } from 'native-base';
import { customTheme } from './src/styles/theme';

export default function App() {
  return (
    <NativeBaseProvider theme={customTheme}>
      <Box flex={1} bg="#fff" alignItems="center" justifyContent="center">
        <Text>RootsConnect</Text>
        <Button onPress={() => console.log('hello')}>
          Find Community Members
        </Button>
      </Box>
    </NativeBaseProvider>
  );
}
```

### 6. Theming & Styling Framework

#### 5.1 NativeBase Theming System
```typescript
// Theme Configuration
import { extendTheme } from 'native-base';

const customTheme = extendTheme({
  colors: {
    // Brand colors for Indian diaspora community
    primary: {
      50: '#fff5f5',
      500: '#e53e3e',  // Saffron-inspired
      900: '#742a2a',
    },
    secondary: {
      50: '#f0fff4',
      500: '#38a169',  // Green from Indian flag
      900: '#22543d',
    },
    cultural: {
      saffron: '#FF9933',
      white: '#FFFFFF',
      green: '#138808',
      navy: '#000080',
    }
  },
  fonts: {
    heading: 'Roboto',
    body: 'Roboto',
    mono: 'Courier',
  },
  components: {
    Button: {
      variants: {
        cultural: {
          bg: 'cultural.saffron',
          _text: { color: 'white' },
          _pressed: { bg: 'cultural.green' }
        }
      }
    }
  }
});
```

#### 5.2 Theme Management Architecture
```typescript
// Theme Context Provider
interface ThemeContextType {
  currentTheme: 'light' | 'dark' | 'cultural' | 'custom';
  toggleTheme: () => void;
  setCustomTheme: (theme: CustomTheme) => void;
  culturalThemes: CulturalTheme[];
}

// Cultural Theme Options
const culturalThemes = {
  indian: {
    primary: '#FF9933',    // Saffron
    secondary: '#138808',  // Green
    accent: '#000080',     // Navy Blue
  },
  regional: {
    punjabi: { primary: '#FFD700', secondary: '#8B0000' },
    tamil: { primary: '#FF6B35', secondary: '#004225' },
    bengali: { primary: '#DC143C', secondary: '#FFD700' },
    gujarati: { primary: '#FF4500', secondary: '#32CD32' },
  }
};
```

#### 5.3 Theme Switching Implementation
```typescript
// Theme Hook
export const useTheme = () => {
  const [theme, setTheme] = useState<ThemeType>('light');

  const toggleTheme = useCallback(() => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    AsyncStorage.setItem('userTheme', newTheme);
  }, [theme]);

  const applyCulturalTheme = useCallback((culturalBackground: string) => {
    const culturalTheme = getCulturalTheme(culturalBackground);
    setTheme(culturalTheme);
    AsyncStorage.setItem('userTheme', JSON.stringify(culturalTheme));
  }, []);

  return { theme, toggleTheme, applyCulturalTheme };
};
```

#### 5.4 Component Theming Structure
```
src/styles/theme/
├── index.ts                 # Main theme export
├── lightTheme.ts           # Light mode theme
├── darkTheme.ts            # Dark mode theme
├── culturalThemes.ts       # Indian cultural themes
├── customThemes.ts         # User custom themes
├── components/             # Component-specific themes
│   ├── buttonThemes.ts
│   ├── cardThemes.ts
│   ├── inputThemes.ts
│   └── mapThemes.ts
└── utils/
    ├── themeHelpers.ts
    └── colorUtils.ts
```

#### 5.5 Alternative: Tamagui Setup (High Performance)
```typescript
// Tamagui Configuration
import { createTamagui, createTokens } from '@tamagui/core';

const tokens = createTokens({
  color: {
    saffron: '#FF9933',
    green: '#138808',
    navy: '#000080',
    white: '#FFFFFF',
  },
  space: {
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
  },
  size: {
    sm: 20,
    md: 28,
    lg: 36,
  }
});

const config = createTamagui({
  tokens,
  themes: {
    light: {
      background: tokens.color.white,
      color: tokens.color.navy,
    },
    dark: {
      background: tokens.color.navy,
      color: tokens.color.white,
    },
    cultural: {
      background: tokens.color.saffron,
      color: tokens.color.white,
    }
  }
});
```

### 6. Data Models & Schema

#### 5.1 GraphQL Schema Design
```typescript
// User Model
type User {
  id: ID!
  email: String!
  name: String!
  culturalBackground: CulturalBackground!
  location: Location
  contactInfo: ContactInfo
  profileImage: String
  willingToMeet: Boolean!
  privacy: PrivacySettings!
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

// Cultural Background
type CulturalBackground {
  originState: String!
  originCity: String
  languages: [String!]!
  traditions: [String]
  profession: String
  interests: [String]
}

// Contact Information
type ContactInfo {
  emails: [EmailContact]
  phones: [PhoneContact]
  whatsapp: String
  facebook: String
  telegram: String
  linkedin: String
}

// Location with Geospatial Support
type Location {
  latitude: Float!
  longitude: Float!
  city: String!
  country: String!
  address: String
  isVisible: Boolean!
}

// Connection Model
type Connection {
  id: ID!
  requester: User!
  recipient: User!
  status: ConnectionStatus!
  introductionMessage: String
  sharedContacts: [ContactType!]
  createdAt: AWSDateTime!
  updatedAt: AWSDateTime!
}

enum ConnectionStatus {
  PENDING
  ACCEPTED
  DECLINED
  BLOCKED
}
```

### 6. Development Workflow

#### 6.1 Local Development Setup
```bash
# 1. Initialize React Native project
npx react-native@latest init RootsConnect --template react-native-template-typescript

# 2. Setup Amplify Gen 2
cd RootsConnect
npx ampx generate react-native

# 3. Install dependencies
npm install @aws-amplify/react-native react-native-maps react-navigation

# 4. Start development
npx ampx sandbox  # Backend sandbox
npm run android   # Android app
npm run ios       # iOS app
```

#### 6.2 Environment Management
- **Development:** `npx ampx sandbox` (local DynamoDB, mock services)
- **Staging:** `npx ampx deploy --branch staging`
- **Production:** `npx ampx deploy --branch main`

#### 6.3 Testing Strategy
- **Unit Tests:** Jest + React Native Testing Library
- **Integration Tests:** Detox for E2E testing
- **Backend Tests:** Amplify Gen 2 built-in testing
- **Manual Testing:** Device testing (iOS/Android)

### 7. Security & Privacy Implementation

#### 7.1 Data Protection
- **Encryption:** AES-256 encryption at rest (DynamoDB, S3)
- **Transport:** TLS 1.3 for all API communications
- **Authentication:** JWT tokens with automatic refresh
- **Authorization:** Fine-grained IAM policies

#### 7.2 Privacy Controls
- **Granular Permissions:** Per-field contact sharing controls
- **Location Privacy:** Opt-in location sharing with radius controls
- **Data Retention:** Configurable data retention policies
- **GDPR Compliance:** Data export and deletion capabilities

### 8. Performance Optimization

#### 8.1 Mobile App Performance
- **Bundle Splitting:** Code splitting for faster app startup
- **Image Optimization:** WebP format with lazy loading
- **Caching:** Aggressive caching with Amplify DataStore
- **Offline Support:** Full offline functionality with sync

#### 8.2 Backend Performance
- **DynamoDB:** Optimized partition keys and GSI design
- **GraphQL:** Efficient query design with DataLoader pattern
- **CDN:** CloudFront for global content delivery
- **Lambda:** Cold start optimization with provisioned concurrency

### 9. Monitoring & Analytics

#### 9.1 Application Monitoring
- **Crash Reporting:** AWS Pinpoint crash analytics
- **Performance:** Custom metrics for app performance
- **User Analytics:** User journey and engagement tracking
- **Backend Monitoring:** CloudWatch for API and database metrics

#### 9.2 Business Analytics
- **User Engagement:** Connection success rates, map usage
- **Geographic Analytics:** User distribution and activity by region
- **Cultural Insights:** Popular cultural backgrounds and connections
- **Feature Usage:** Most used features and user flows

### 10. Deployment & DevOps

#### 10.1 CI/CD Pipeline
```yaml
# GitHub Actions workflow
name: Deploy RootsConnect
on:
  push:
    branches: [main, staging]

jobs:
  deploy-backend:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy Amplify Backend
        run: npx ampx deploy --branch ${{ github.ref_name }}
  
  build-mobile:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      - name: Build iOS
        run: cd ios && xcodebuild
      - name: Build Android
        run: cd android && ./gradlew assembleRelease
```

#### 10.2 Release Strategy
- **Backend:** Blue-green deployment with Amplify
- **Mobile:** Staged rollout (5% → 25% → 100%)
- **Feature Flags:** Amplify feature flags for gradual feature rollout
- **Rollback:** Automated rollback on error threshold

This technical PRD provides the complete framework and structure for building the RootsConnect platform using modern, scalable technologies with AI-assisted development.
